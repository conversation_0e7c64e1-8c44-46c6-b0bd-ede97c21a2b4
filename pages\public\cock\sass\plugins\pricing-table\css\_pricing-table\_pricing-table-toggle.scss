/*

  Toggle

*/

.switch {
  &.s-outline {
    &[class*="s-outline-"] .slider:before {
      bottom: 1px;
      left: 1px;
      border: 2px solid $m-color_4;
      background-color: $m-color_4;
    }

    .slider {
      border: 2px solid $m-color_4;
    }
  }

  &.s-icons {
    &.s-outline-primary {
      color: $primary;
      margin: 0 3px 0 11px;
    }

    .slider:before {
      content: url('data:image/svg+xml, <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="%231b55e2" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-x"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>') !important;
    }

    input:checked + .slider:before {
      content: url('data:image/svg+xml, <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="%23fff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-check"><polyline points="20 6 9 17 4 12"></polyline></svg>') !important;
    }
  }
}

.billing-cycle-radios {
  display: block;
  margin: 0 auto;
  text-align: center;

  &.text-align-left {
    text-align: left;
  }

  .radio {
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.pricing-plans-container {
  border-radius: 4px;
  display: flex;
}

.pricing-plan {
  flex: 1;
  padding: 35px 35px;
  position: relative;
  color: $dark;
  border: 1px solid $m-color_3;
  background-color: $white;

  &:hover {
    box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
  }

  &:first-of-type {
    border-right: none;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  &:last-of-type {
    border-left: none;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    letter-spacing: 1px;
  }

  p {
    margin: 0;
    color: $m-color_6;
    font-size: 14px;
    letter-spacing: 1px;
  }
}

.billed-yearly-label {
  display: none;
}

.billed-yearly {
  .billed-yearly-label {
    display: block;
  }

  .billed-monthly-label {
    display: none;
  }
}

.pricing-plan-label {
  border-radius: 4px;
  font-size: 18px;
  color: $m-color_6;
  text-align: center;
  line-height: 24px;
  padding: 15px;
  margin: 30px 0;

  strong {
    font-size: 32px;
    font-weight: 600;
    color: $dark;
    margin-right: 5px;
    line-height: 30px;
  }
}

.pricing-plan-features {
  strong {
    color: $m-color_10;
    font-weight: 600;
    margin-bottom: 5px;
    line-height: 24px;
    display: inline-block;
    letter-spacing: 1px;
    font-size: 15px;
  }

  ul {
    padding: 0;
    margin: 0;

    li {
      display: block;
      margin: 0;
      padding: 3px 0;
      line-height: 24px;
      color: $m-color_6;
      font-size: 13px;
      letter-spacing: 1px;
    }
  }
}

.pricing-plan .button {
  border: none;
  padding: 10px;

  &:hover {
    box-shadow: 0 4px 12px rgba(102, 103, 107, 0.15);
  }
}

.billed-yearly-radio {
  &.billed-yearly-switch span.txt-monthly {
    color: $m-color_6;
    font-weight: 600;
  }

  span {
    &.txt-yearly {
      color: $m-color_6;
      font-weight: 600;
    }

    &.txt-monthly {
      color: $primary;
      font-weight: 700;
    }
  }

  &.billed-yearly-switch span.txt-yearly {
    color: $primary;
    font-weight: 700;
  }

  span.txt-yearly span.badge {
    visibility: hidden;
    padding: 4px 11px;
  }

  &.billed-yearly-switch span.txt-yearly span.badge {
    visibility: visible;
  }

  span {
    &.txt-monthly, &.txt-yearly {
      letter-spacing: 1px;
    }
  }
}

.pricing-plan {
  &.recommended {
    box-shadow: 0 0 45px rgba(0, 0, 0, 0.09);
    padding: 35px;
    margin: 6rem 0 0 0;
  }

  .recommended-badge {
    background-color: $primary;
    color: $white;
    position: absolute;
    width: 100%;
    height: 59px;
    top: -45px;
    left: 0;
    text-align: center;
    border-radius: 4px 4px 0 0;
    font-weight: 600;
    line-height: 59px;
    letter-spacing: 2px;
    font-size: 16px;
  }
}

.recommended .pricing-plan-label {
  color: $l-dark;

  strong {
    color: $primary;
    font-weight: 700;
    font-size: 44px;
  }
}

.pricing-plan.recommended .button {
  color: #fff !important;
  background-color: $primary !important;
}

@media (max-width: 767px) {
  .pricing-plan {
    &:first-of-type {
      border-right: 1px solid $m-color_3;
      border-radius: 6px;
    }

    &:last-of-type {
      border-left: 1px solid $m-color_3;
      border-radius: 6px;
    }
  }
}