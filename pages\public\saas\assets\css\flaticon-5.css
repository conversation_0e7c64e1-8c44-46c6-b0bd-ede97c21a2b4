@font-face {
    font-family: "Flaticon";
      src: url("../fonts-5/Flaticon.eot");
      src: url("../fonts-5/Flaticon.eot?#iefix") format("embedded-opentype"),
      url("../fonts-5/Flaticon.woff") format("woff"),
      url("../fonts-5/Flaticon.ttf") format("truetype"),
      url("../fonts-5/Flaticon.svg#Flaticon") format("svg");
      font-weight: normal;
      font-style: normal;
}

i[class^="flaticon-"]:before, i[class*=" flaticon-"]:before {
    font-family: flaticon !important;
    font-style: normal;
    font-weight: normal !important;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.flaticon-play:before {
    content: "\f101";
}
.flaticon-idea:before {
    content: "\f102";
}
.flaticon-data-analysis:before {
    content: "\f103";
}
.flaticon-profit:before {
    content: "\f104";
}
.flaticon-digital:before {
    content: "\f105";
}
.flaticon-link:before {
    content: "\f106";
}
.flaticon-right-arrow:before {
    content: "\f107";
}
.flaticon-rating:before {
    content: "\f108";
}
.flaticon-project-plan:before {
    content: "\f109";
}
.flaticon-employee:before {
    content: "\f10a";
}
.flaticon-conversation:before {
    content: "\f10b";
}
.flaticon-data-processing:before {
    content: "\f10c";
}
.flaticon-setting:before {
    content: "\f10d";
}
.flaticon-software-development:before {
    content: "\f10e";
}
.flaticon-analytics:before {
    content: "\f10f";
}
.flaticon-agile:before {
    content: "\f110";
}
.flaticon-design-thinking:before {
    content: "\f111";
}
.flaticon-play-button:before {
    content: "\f112";
}
.flaticon-right-arrows:before {
    content: "\f113";
}
