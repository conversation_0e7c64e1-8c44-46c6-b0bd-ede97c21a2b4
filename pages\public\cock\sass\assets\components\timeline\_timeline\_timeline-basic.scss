/*
=====================
    Basic
=====================
*/

.timeline-line .item-timeline {
  display: flex;

  .t-dot {
    position: relative;

    &:before {
      content: '';
      position: absolute;
      border-color: inherit;
      border-width: 2px;
      border-style: solid;
      border-radius: 50%;
      width: 10px;
      height: 10px;
      top: 15px;
      left: 50%;
      transform: translateX(-50%);
      border-color: #2196f3;
    }

    &:after {
      content: '';
      position: absolute;
      border-color: inherit;
      border-width: 2px;
      border-style: solid;
      border-radius: 50%;
      width: 10px;
      height: 10px;
      top: 15px;
      left: 50%;
      transform: translateX(-50%);
      border-color: #2196f3;
      width: 0;
      height: auto;
      top: 25px;
      bottom: -15px;
      border-right-width: 0;
      border-top-width: 0;
      border-bottom-width: 0;
      border-radius: 0;
    }

    &.t-dot-primary:before {
      border-color: #1b55e2;
    }

    &.t-dot-success:before {
      border-color: #8dbf42;
    }

    &.t-dot-warning:before {
      border-color: #e2a03f;
    }

    &.t-dot-info:before {
      border-color: #2196f3;
    }

    &.t-dot-danger:before {
      border-color: #e7515a;
    }

    &.t-dot-dark:before {
      border-color: #3b3f5c;
    }

    &.t-dot-primary:after {
      border-color: #1b55e2;
    }

    &.t-dot-success:after {
      border-color: #8dbf42;
    }

    &.t-dot-warning:after {
      border-color: #e2a03f;
    }

    &.t-dot-info:after {
      border-color: #2196f3;
    }

    &.t-dot-danger:after {
      border-color: #e7515a;
    }

    &.t-dot-dark:after {
      border-color: #3b3f5c;
    }
  }

  &:last-child .t-dot:after {
    display: none;
  }

  .t-meta-time {
    margin: 0;
    min-width: 100px;
    max-width: 100px;
    font-size: 12px;
    font-weight: 700;
    color: #888ea8;
    align-self: center;
  }

  .t-text {
    padding: 10px;
    align-self: center;
    margin-left: 10px;

    p {
      font-size: 13px;
      margin: 0;
      color: #3b3f5c;
      font-weight: 600;

      a {
        color: #1b55e2;
        font-weight: 600;
      }
    }
  }

  .t-time {
    margin: 0;
    min-width: 58px;
    max-width: 100px;
    font-size: 16px;
    font-weight: 600;
    color: #3b3f5c;
    padding: 10px 0;
  }

  .t-text .t-meta-time {
    margin: 0;
    min-width: 100px;
    max-width: 100px;
    font-size: 12px;
    font-weight: 700;
    color: #888ea8;
    align-self: center;
  }
}