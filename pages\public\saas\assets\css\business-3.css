/*-------------------------------------------------------------
Template Name: Saa<PERSON><PERSON>
Template URL: 
Author Name: Themexriver
Author URL: https://themeforest.net/user/themexriver/portfolio
Version: 1.0
Description: 
Tags: SaaSio
-------------------------------------------------------------*/


/*------------------------------------------------------------- 
TABLE OF CONTENTS: 
---------------------------------------------------------------
>> Variables
>> Mixin
>> Preloader
--------------------------------------------------------------*/

@import url("https://fonts.googleapis.com/css2?family=Lexend:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap");
.pr20-body {
	margin: 0;
	padding: 0;
	overflow-x: hidden;
	font-size: 16px;
	line-height: 1.556;
	color: #666666;
	font-family: "Roboto", sans-serif;
	-moz-osx-font-smoothing: antialiased;
	-webkit-font-smoothing: antialiased;
}

::-moz-selection {
	color: #ffffff;
	background-color: #0096ff;
}

::selection {
	color: #ffffff;
	background-color: #0096ff;
}

::-moz-selection {
	color: #ffffff;
	background-color: #0096ff;
}

.container {
	max-width: 1200px;
}

ul {
	margin: 0;
	padding: 0;
	list-style: none;
}

ul li {
	list-style: none;
}

[data-background] {
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center center;
}

a {
	color: inherit;
	text-decoration: none;
	-webkit-transition: .3s all ease-in-out;
	-o-transition: .3s all ease-in-out;
	transition: .3s all ease-in-out;
}

a:hover,
a:focus {
	text-decoration: none;
}

img {
	width: 100%;
	height: auto;
}

section {
	overflow: hidden;
}

button {
	cursor: pointer;
}

.form-control:focus,
button:visited,
button.active,
button:hover,
button:focus,
input:visited,
input.active,
input:hover,
input:focus,
textarea:hover,
textarea:focus,
a:hover,
a:focus,
a:visited,
a.active,
select,
select:hover,
select:focus,
select:visited {
	outline: none;
	-webkit-box-shadow: none;
	box-shadow: none;
	text-decoration: none;
	color: inherit;
}

.form-control {
	-webkit-box-shadow: none;
	box-shadow: none;
}

.relative-position {
	position: relative;
}

.no-padding {
	padding: 0;
}

.pr20-headline h1,
.pr20-headline h2,
.pr20-headline h3,
.pr20-headline h4,
.pr20-headline h5,
.pr20-headline h6 {
	font-family: "Lexend", sans-serif;
	font-weight: 700;
	text-transform: none;
	line-height: 1.25;
	margin-bottom: 0;
	color: #1a0b60;
	text-transform: capitalize;
}

.pr20-headline h1 {
	font-size: 60px;
}

@media (max-width: 767.98px) {
	.pr20-headline h1 {
		font-size: 50px;
	}
}

.pr20-headline h2 {
	font-size: 48px;
}

@media (max-width: 767.98px) {
	.pr20-headline h2 {
		font-size: 36px;
	}
}

@media (max-width: 575.98px) {
	.pr20-headline h2 {
		font-size: 30px;
	}
}

.pr20-headline h3 {
	font-size: 36px;
}

@media (max-width: 767.98px) {
	.pr20-headline h3 {
		font-size: 30px;
	}
}

.pr20-headline h4 {
	font-size: 24px;
}

@media (max-width: 767.98px) {
	.pr20-headline h4 {
		font-size: 20px;
	}
}

.pr20-headline h5 {
	font-size: 22px;
}

.pr20-headline h6 {
	font-size: 18px;
}

.pr20-pera-txt p {
	color: #666666;
	margin: 0;
}

[class^="flaticon-"]:before,
[class*=" flaticon-"]:before,
[class^="flaticon-"]:after,
[class*=" flaticon-"]:after {
	margin-left: 0;
}

.loading-preloader {
	background-color: white;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 900;
}

#loading-preloader {
	position: fixed;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	height: 50px;
	width: 150px;
	visibility: visible;
	z-index: 1000;
}

.line_shape {
	width: 8px;
	height: 50px;
	margin-right: 5px;
	background-color: #0067ff;
	-webkit-animation: animate24 1s infinite;
	animation: animate24 1s infinite;
	float: left;
	opacity: 1;
}

.line_shape:last-child {
	margin-right: 0px;
}

.line_shape:nth-child(10) {
	-webkit-animation-delay: 0.9s;
	animation-delay: 0.9s;
}

.line_shape:nth-child(9) {
	-webkit-animation-delay: 0.8s;
	animation-delay: 0.8s;
}

.line_shape:nth-child(8) {
	-webkit-animation-delay: 0.7s;
	animation-delay: 0.7s;
}

.line_shape:nth-child(7) {
	-webkit-animation-delay: 0.6s;
	animation-delay: 0.6s;
}

.line_shape:nth-child(6) {
	-webkit-animation-delay: 0.5s;
	animation-delay: 0.5s;
}

.line_shape:nth-child(5) {
	-webkit-animation-delay: 0.4s;
	animation-delay: 0.4s;
}

.line_shape:nth-child(4) {
	-webkit-animation-delay: 0.3s;
	animation-delay: 0.3s;
}

.line_shape:nth-child(3) {
	-webkit-animation-delay: 0.2s;
	animation-delay: 0.2s;
}

.line_shape:nth-child(2) {
	-webkit-animation-delay: 0.1s;
	animation-delay: 0.1s;
}

@-webkit-keyframes animate24 {
	50% {
		-webkit-transform: scaleY(0);
		transform: scaleY(0);
	}
}

@keyframes animate24 {
	50% {
		-webkit-transform: scaleY(0);
		transform: scaleY(0);
	}
}

.pr20-title-area {
	margin-bottom: 30px;
}

.pr20-title-area .pr20-subtitle {
	color: #0096ff;
	font-weight: 700;
	font-size: 18px;
	margin-bottom: 6px;
	display: inline-block;
	text-transform: capitalize;
}

.pr20-title-area h3 {
	margin-bottom: 20px;
}

.pr20-title-area h3 span {
	color: #0096ff;
}

.pr20-scroll-top {
	width: 50px;
	height: 50px;
	color: #ffffff !important;
	background-color: #0096ff;
	font-size: 24px;
	text-align: center;
	line-height: 50px;
	display: inline-block;
	position: fixed;
	bottom: 30px;
	right: 30px;
	z-index: 100;
	border-radius: 4px;
	display: none;
	-webkit-transition: initial;
	-o-transition: initial;
	transition: initial;
}

.pr20-primary-btn a {
	width: 170px;
	height: 55px;
	color: #ffffff;
	background-color: transparent;
	display: inline-block;
	text-align: center;
	line-height: 52px;
	border-radius: 4px;
	position: relative;
	background-image: -webkit-linear-gradient(20deg, #0096ff 0%, #00e9f0 100%);
	background-image: -o-linear-gradient(20deg, #0096ff 0%, #00e9f0 100%);
	background-image: linear-gradient(-290deg, #0096ff 0%, #00e9f0 100%);
	z-index: 1;
	font-family: "Lexend", sans-serif;
	font-weight: 600;
	text-transform: capitalize;
}

.pr20-primary-btn a::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: -webkit-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: -o-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: linear-gradient(290deg, #0096ff 0%, #00e9f0 100%);
	z-index: -1;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
	border-radius: 4px;
}

.pr20-primary-btn a:hover::after {
	opacity: 0;
}

@-webkit-keyframes pr20_rotate_animation {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@keyframes pr20_rotate_animation {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}

@-webkit-keyframes pr20_object_animation {
	0% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
	}
	50% {
		-webkit-transform: translateY(-15px);
		transform: translateY(-15px);
	}
	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
	}
}

@keyframes pr20_object_animation {
	0% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
	}
	50% {
		-webkit-transform: translateY(-15px);
		transform: translateY(-15px);
	}
	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
	}
}

@-webkit-keyframes pr20-ripple-animation {
	0% {
		-webkit-box-shadow: 0 0 0 0rem rgba(255, 98, 114, 0.2), 0 0 0 1rem rgba(255, 98, 114, 0.2), 0 0 0 2rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0.2);
		box-shadow: 0 0 0 0rem rgba(255, 98, 114, 0.2), 0 0 0 1rem rgba(255, 98, 114, 0.2), 0 0 0 2rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0.2);
	}
	100% {
		-webkit-box-shadow: 0 0 0 1rem rgba(255, 98, 114, 0.2), 0 0 0 2rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0);
		box-shadow: 0 0 0 1rem rgba(255, 98, 114, 0.2), 0 0 0 2rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0);
	}
}

@keyframes pr20-ripple-animation {
	0% {
		-webkit-box-shadow: 0 0 0 0rem rgba(255, 98, 114, 0.2), 0 0 0 1rem rgba(255, 98, 114, 0.2), 0 0 0 2rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0.2);
		box-shadow: 0 0 0 0rem rgba(255, 98, 114, 0.2), 0 0 0 1rem rgba(255, 98, 114, 0.2), 0 0 0 2rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0.2);
	}
	100% {
		-webkit-box-shadow: 0 0 0 1rem rgba(255, 98, 114, 0.2), 0 0 0 2rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0);
		box-shadow: 0 0 0 1rem rgba(255, 98, 114, 0.2), 0 0 0 2rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0.2), 0 0 0 3rem rgba(255, 98, 114, 0);
	}
}


/********* Layout **************/

.pr20-body-overlay {
	position: fixed;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 100;
	opacity: 0;
	visibility: hidden;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-body-overlay-on {
	opacity: 1;
	visibility: visible;
}

.pr20-header-area {
	background-color: #1a0b60;
}

@media (max-width: 1024.98px) {
	.pr20-header-area {
		padding: 10px 0;
	}
}

.pr20-header-area.pr20-sticky-on {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 200;
	-webkit-animation: 1s slideInDown;
	animation: 1s slideInDown;
}

.pr20-header-area .container {
	position: relative;
}

@media (max-width: 1024.98px) {
	.pr20-header-area .pr20-desktop-menu {
		display: none;
	}
}

.pr20-header-area .pr20-logo-wrapper a {
	width: 150px;
	display: inline-block;
}

.pr20-header-area .pr20-nav-menu nav {
	text-align: right;
}

.pr20-header-area .pr20-nav-menu ul {
	display: inline-block;
}

.pr20-header-area .pr20-nav-menu ul li {
	display: inline-block;
	position: relative;
}

.pr20-header-area .pr20-nav-menu ul li a {
	padding: 30px 18px;
	display: block;
	color: #ffffff;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
	text-transform: capitalize;
}
.pr20-header-area .side-demo span {
	top: 5px;
}
.pr20-header-area .pr20-nav-menu ul li a:hover,
.pr20-header-area .pr20-nav-menu ul li a.active {
	color: #00e9f0;
}
@media (max-width: 1199.98px) {
	.pr20-header-area .pr20-nav-menu ul li a {
		padding: 30px 16px;
	}
}

.pr20-header-area .pr20-nav-menu ul li.has-submenu::after {
	content: '\f067';
	position: absolute;
	z-index: 1;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
	font-size: 10px;
	top: 50%;
	right: 6px;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	color: #ffffff;
}

.pr20-header-area .pr20-nav-menu ul li.has-submenu ul {
	position: absolute;
	top: 100%;
	left: 0;
	display: block;
	width: 220px;
	background: #1a0b60;
	z-index: 20;
	opacity: 0;
	visibility: hidden;
	top: 100px;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-header-area .pr20-nav-menu ul li.has-submenu ul li {
	display: block;
}

.pr20-header-area .pr20-nav-menu ul li.has-submenu ul li a {
	display: block;
	text-align: left;
	color: #ffffff;
	padding: 12px 18px;
}

.pr20-header-area .pr20-nav-menu ul li.has-submenu ul li a:hover {
	padding-left: 25px;
}

.pr20-header-area .pr20-nav-menu ul li.has-submenu ul li+li a {
	border-top: 1px solid #dddddd96;
}

.pr20-header-area .pr20-nav-menu ul li.has-submenu ul li.has-submenu ul {
	right: -100%;
	left: auto;
	top: 0;
}

.pr20-header-area .pr20-nav-menu ul li:hover.has-submenu::after {
	-webkit-transform: translateY(-50%) rotate(45deg);
	-ms-transform: translateY(-50%) rotate(45deg);
	transform: translateY(-50%) rotate(45deg);
}

.pr20-header-area .pr20-nav-menu ul li:hover>ul {
	opacity: 1;
	visibility: visible;
	top: 100%;
}

.pr20-header-area .pr20-header-right {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
}

.pr20-header-area .pr20-header-right .pr20-sidebar-menu-open {
	display: inline-block;
	width: 45px;
	height: 45px;
	text-align: center;
	line-height: 45px;
	border-radius: 50%;
	border: 1px solid #0096ff;
	color: #0096ff;
	font-size: 25px;
	margin-right: 30px;
	cursor: pointer;
}

@media (max-width: 1024.98px) {
	.pr20-header-area .pr20-header-right .pr20-sidebar-menu-open {
		display: none;
	}
}

@media (max-width: 1024.98px) {
	.pr20-header-area .pr20-header-right .pr20-primary-btn {
		margin-right: 60px;
	}
}

@media (max-width: 575.98px) {
	.pr20-header-area .pr20-header-right .pr20-primary-btn {
		display: none;
	}
}

.pr20-mobile-menu {
	display: none;
}

@media (max-width: 1024.98px) {
	.pr20-mobile-menu {
		display: block;
	}
}

.pr20-mobile-menu .pr20-mobile-menu-open {
	position: absolute;
	top: 50%;
	right: 15px;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	font-size: 36px;
	z-index: 10;
	color: #ffffff;
}

.pr20-mobile-menu-wrapper {
	position: fixed;
	top: 0;
	right: -300px;
	background-color: #1a0b60;
	width: 280px;
	height: 100vh;
	z-index: 100;
	overflow-y: scroll;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-mobile-menu-wrapper .pr20-mobile-menu-close {
	position: absolute;
	top: 20px;
	left: 15px;
	color: #ffffff;
	cursor: pointer;
}

.pr20-mobile-menu-wrapper .pr20-mobile-navigation {
	padding-top: 60px;
}

.pr20-mobile-menu-wrapper ul li a {
	color: #ffffff;
	padding: 12px 18px;
	display: block;
	text-transform: capitalize;
}

.pr20-mobile-menu-wrapper ul li+li a {
	border-top: 1px solid #dddddd5c;
}

.pr20-mobile-menu-visible {
	right: 0;
}

.pr20-sidebar-info .pr20-overlay-on {
	opacity: 1;
	visibility: visible;
}

.pr20-sidebar-info .pr20_sidebar_info_content {
	width: 380px;
	height: 100%;
	position: fixed;
	right: -380px;
	top: 0;
	background-color: #ffffff;
	z-index: 9999999;
	padding: 30px 40px;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-sidebar-info .pr20_sidebar_info_content .close-menu {
	cursor: pointer;
}

.pr20-sidebar-info .pr20_sidebar_info_content .pr20_sidebar_logo {
	text-align: center;
	margin-bottom: 60px;
}

.pr20-sidebar-info .pr20_sidebar_info_content .pr20_sidebar_logo img {
	width: 140px;
}

.pr20-sidebar-info .pr20_sidebar_info_content .pr20-pera-txt {
	line-height: 1.8em;
}

.pr20-sidebar-info .pr20_sidebar_info_content .pr20-sidebar-gallery {
	margin-top: 30px;
}

.pr20-sidebar-info .pr20_sidebar_info_content .pr20-sidebar-gallery ul li {
	display: inline-block;
	margin: 5px 5px;
}

.pr20-sidebar-info .pr20_sidebar_info_content .pr20-sidebar-social {
	margin-top: 30px;
}

.pr20-sidebar-info .pr20_sidebar_info_content .pr20-sidebar-social h5 {
	margin-bottom: 15px;
}

.pr20-sidebar-info .pr20_sidebar_info_content .pr20-sidebar-social a+a {
	margin-left: 10px;
}

.pr20-sidebar-info .pr20_sidebar_info_content .pr20-sidebar-copyright {
	text-align: center;
	margin-top: 40px;
}

.pr20-sidebar-info .pr20-sidebar-on {
	right: 0;
}

.pr20-hero-slider {
	position: relative;
}

.pr20-hero-slider .pr20-wave-shape {
	position: absolute;
	bottom: -2px;
	left: 0;
	width: 100%;
	height: 190px;
	z-index: 10;
}

.pr20-service-section {
	padding: 100px 0 0px;
	padding-top: 0;
	margin-top: -100px;
	position: relative;
	z-index: 10;
}

@media (max-width: 991.98px) {
	.pr20-service-section {
		padding-top: 100px;
		margin-top: 0;
	}
}

.pr20-service-section .pr20-service-top {
	position: relative;
}

.pr20-service-section .pr20-service-top .pr20-sr-shape-1 {
	position: absolute;
	bottom: -50px;
	left: -30px;
}

.pr20-service-section .pr20-service-top .pr20-sr-shape-2 {
	position: absolute;
	bottom: -70px;
	right: -100px;
}

.pr20-service-column {
	padding: 30px 20px 20px 20px;
	background-color: #ffffff;
	-webkit-box-shadow: 0px 14px 50px 0px rgba(175, 175, 175, 0.2);
	box-shadow: 0px 14px 50px 0px rgba(175, 175, 175, 0.2);
	text-align: center;
	margin-bottom: 30px;
}

.pr20-service-column .pr20-icon-wrapper {
	text-align: center;
	margin-bottom: 30px;
}

.pr20-service-column .pr20-icon-wrapper i {
	width: 60px;
	height: 60px;
	background-color: #e5fafe;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	font-size: 30px;
	border-radius: 50%;
	color: #0096ff;
}

.pr20-service-column .pr20-headline {
	margin-bottom: 10px;
}

.pr20-service-bottom {
	margin-top: 60px;
	position: relative;
}

.pr20-service-bottom .pr20-sr-shape-3 {
	position: absolute;
	bottom: 30px;
	right: -60px;
}

.pr20-service-col2 .pr20-sr-column {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 30px;
}

.pr20-service-col2 .pr20-sr-column .pr20-icon-wrapper i {
	width: 100px;
	height: 100px;
	display: -webkit-inline-box;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	background-color: #e5fafe;
	border-radius: 50%;
	font-size: 45px;
	color: #0096ff;
	position: relative;
	z-index: 2;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-service-col2 .pr20-sr-column .pr20-icon-wrapper i::after {
	content: '';
	width: 100%;
	height: 100%;
	background-image: -webkit-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: -o-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: linear-gradient(290deg, #0096ff 0%, #00e9f0 100%);
	left: 0;
	right: 0;
	position: absolute;
	border-radius: 50%;
	z-index: -1;
	opacity: 0;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-service-col2 .pr20-sr-column .pr20-sr-column-content {
	background-color: #ffffff;
	padding: 40px 30px 30px 80px;
	-webkit-box-shadow: 0px 14px 40px 0px rgba(144, 144, 144, 0.15);
	box-shadow: 0px 14px 40px 0px rgba(144, 144, 144, 0.15);
	margin-left: -50px;
}

.pr20-service-col2 .pr20-sr-column .pr20-sr-column-content .pr20-headline {
	margin-bottom: 20px;
}

.pr20-service-col2 .pr20-sr-column:hover .pr20-icon-wrapper i {
	color: #ffffff;
}

.pr20-service-col2 .pr20-sr-column:hover .pr20-icon-wrapper i::after {
	opacity: 1;
}

.pr20-service-col2 .pr20-primary-btn {
	margin-top: 30px;
}

.pr20-about-section {
	padding: 100px 0;
	background-color: #ffffff;
}

.pr20-about-left .pr20-about-list {
	margin-top: 10px;
	display: inline-block;
}

.pr20-about-left .pr20-about-item {
	background-color: #ffffff;
	-webkit-box-shadow: 0px 20px 60px 0px rgba(135, 135, 135, 0.2);
	box-shadow: 0px 20px 60px 0px rgba(135, 135, 135, 0.2);
	margin-bottom: 30px;
	padding: 20px;
	border-radius: 6px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.pr20-about-left .pr20-about-item .pr20-icon-wrapper {
	margin-right: 20px;
}

.pr20-about-left .pr20-about-item .pr20-icon-wrapper i {
	font-size: 45px;
	color: #0096ff;
}

.pr20-about-left .pr20-about-item .pr20-headline {
	margin-bottom: 10px;
}

.pr20-about-left .pr20-primary-btn {
	margin-top: 10px;
}

.pr20-about-right {
	position: relative;
	z-index: 2;
}

@media (max-width: 991.98px) {
	.pr20-about-right {
		margin-top: 60px;
	}
}

.pr20-about-right .pr20-about-shape-1 {
	position: absolute;
	width: 200px;
	right: 0;
	top: 0;
	z-index: -1;
}

.pr20-about-right .pr20-about-shape-2 {
	position: absolute;
	bottom: -50px;
	left: 50%;
	-webkit-transform: translateX(-80%);
	-ms-transform: translateX(-80%);
	transform: translateX(-80%);
	z-index: -1;
}

.pr20-project-showcase {
	padding-top: 100px;
	position: relative;
	z-index: 2;
	height: 450px;
	overflow: visible;
}

@media (max-width: 991.98px) {
	.pr20-project-showcase {
		height: auto;
		padding-bottom: 70px;
	}
}

.pr20-project-showcase::before {
	content: '';
	width: 100%;
	height: 100%;
	background-color: rgba(30, 15, 196, 0.4);
	position: absolute;
	left: 0;
	top: 0;
	z-index: -1;
}

.pr20-project-showcase .pr20-video-project {
	text-align: center;
	margin-bottom: 60px;
}

.pr20-project-showcase .pr20-video-project a {
	width: 40px;
	height: 40px;
	background-color: #ff6272;
	color: #ffffff;
	text-align: center;
	line-height: 40px;
	display: inline-block;
	border-radius: 50%;
	-webkit-animation: 2s pr20-ripple-animation linear infinite;
	animation: 2s pr20-ripple-animation linear infinite;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-project-showcase .pr20-video-project a:hover {
	-webkit-transform: translateY(-5px);
	-ms-transform: translateY(-5px);
	transform: translateY(-5px);
}

.pr20-project-showcase .pr20-headline h3 {
	color: #ffffff;
}

.pr20-project-content {
	margin-top: 80px;
}

.pr20-project-content .pr20-project-column {
	padding: 40px 30px 30px 30px;
	background-color: #ffffff;
	-webkit-box-shadow: 0px 20px 50px 0px rgba(144, 144, 144, 0.2);
	box-shadow: 0px 20px 50px 0px rgba(144, 144, 144, 0.2);
	margin-bottom: 30px;
}

.pr20-project-content .pr20-project-column .pr20-headline h2 {
	display: inline-block;
}

.pr20-project-content .pr20-project-column .pr20-headline span {
	font-size: 48px;
	font-weight: 700;
	display: inline-block;
	color: #1a0b60;
	font-family: "Lexend", sans-serif;
}

.pr20-project-content .pr20-project-column .pr20-headline h4 {
	margin-bottom: 10px;
}

.pr20-business-section {
	padding-top: 250px;
	padding-bottom: 100px;
}

@media (max-width: 991.98px) {
	.pr20-business-section {
		padding-top: 100px;
	}
}

.pr20-business-content {
	background-size: 650px;
	background-position: center;
}

@media (max-width: 991.98px) {
	.pr20-business-content .pr20-business-middle {
		margin-top: 30px;
		margin-bottom: 30px;
	}
}

.pr20-business-item {
	background-color: #ffffff;
	-webkit-box-shadow: 0px 20px 60px 0px rgba(135, 135, 135, 0.2);
	box-shadow: 0px 20px 60px 0px rgba(135, 135, 135, 0.2);
	margin-bottom: 30px;
	padding: 20px;
	border-radius: 6px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.pr20-business-item .pr20-icon-wrapper {
	margin-right: 20px;
}

.pr20-business-item .pr20-icon-wrapper i {
	font-size: 45px;
	color: #0096ff;
}

.pr20-business-item .pr20-headline {
	margin-bottom: 10px;
}

.pr20-software-section {
	padding: 100px 0;
	background-color: #f7f9ff;
	position: relative;
	overflow: visible;
}

.pr20-software-section .pr20-software-shape-1 {
	position: absolute;
	width: 100px;
	left: 0;
	top: -50px;
}

.pr20-software-section .pr20-software-shape-2 {
	position: absolute;
	width: 300px;
	right: 0;
	top: 0;
}

.pr20-software-section .pr20-software-right {
	margin-top: 60px;
}

.pr20-pricing-table {
	padding: 100px 0 150px 0;
	position: relative;
}

@media (max-width: 575.98px) {
	.pr20-pricing-table {
		padding-bottom: 50px;
	}
}

.pr20-pricing-table .pr20-pr-shape-1 {
	position: absolute;
	right: 0;
	bottom: 0;
}

.pr20-pricing-left {
	position: relative;
}

.pr20-pricing-left .pr20-pr-shape-2 {
	position: absolute;
	bottom: -60px;
	left: -80px;
}

.pr20-pricing-left .pr20-title-area {
	margin-bottom: 50px;
}

.pr20-pricing-left .pr20-pricing-btns li a {
	margin-right: 30px;
	width: 170px;
	height: 55px;
	color: #ffffff;
	background-color: #1a0b60;
	display: inline-block;
	text-align: center;
	line-height: 52px;
	border-radius: 4px;
	font-family: "Lexend", sans-serif;
	font-weight: 600;
	text-transform: capitalize;
	position: relative;
	z-index: 1;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

@media (max-width: 575.98px) {
	.pr20-pricing-left .pr20-pricing-btns li a {
		margin-bottom: 20px;
	}
}

.pr20-pricing-left .pr20-pricing-btns li a.active {
	width: 170px;
	height: 55px;
	color: #ffffff;
	background-color: transparent;
	background-image: -webkit-linear-gradient(20deg, #0096ff 0%, #00e9f0 100%);
	background-image: -o-linear-gradient(20deg, #0096ff 0%, #00e9f0 100%);
	background-image: linear-gradient(-290deg, #0096ff 0%, #00e9f0 100%);
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-pricing-left .pr20-pricing-btns li a.active::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: -webkit-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: -o-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: linear-gradient(290deg, #0096ff 0%, #00e9f0 100%);
	z-index: -1;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
	border-radius: 4px;
}

.pr20-pricing-left .pr20-pricing-btns li a.active:hover::after {
	opacity: 0;
}

@media (max-width: 991.98px) {
	.pr20-pricing-right {
		margin-top: 60px;
	}
}

.pr20-pricing-right .pr20-pricing-column {
	position: relative;
	-webkit-box-shadow: 0px 60px 27px 0px rgba(135, 135, 135, 0.2);
	box-shadow: 0px 60px 27px 0px rgba(135, 135, 135, 0.2);
	padding-bottom: 30px;
	background-color: #ffffff;
}

@media (max-width: 575.98px) {
	.pr20-pricing-right .pr20-pricing-column {
		max-width: 260px;
		margin-bottom: 100px;
	}
}

.pr20-pricing-right .pr20-pricing-column::after {
	content: '';
	position: absolute;
	bottom: -50px;
	left: 0;
	width: 100%;
	height: 60px;
	background-image: url(../images/pr-angle-1.png);
	background-size: cover;
	z-index: 1;
}

.pr20-pricing-right .pr20-pricing-column .pr20-pr-column-top {
	text-align: center;
	position: relative;
}

.pr20-pricing-right .pr20-pricing-column .pr20-pr-column-top .pr20-pr-table-shape-1 {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 2;
	width: 100%;
}

.pr20-pricing-right .pr20-pricing-column .pr20-pr-column-top .pr20-pr-table-shape-2 {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	width: 100%;
}

.pr20-pricing-right .pr20-pricing-column .pr20-pr-column-top .pr20-headline {
	position: relative;
	z-index: 3;
	padding-top: 50px;
}

.pr20-pricing-right .pr20-pricing-column .pr20-pr-column-top .pr20-headline h6,
.pr20-pricing-right .pr20-pricing-column .pr20-pr-column-top .pr20-headline h3 {
	color: #ffffff;
}

.pr20-pricing-right .pr20-pricing-column .pr20-pr-column-top .pr20-headline h6 {
	margin-bottom: 6px;
}

.pr20-pricing-right .pr20-pricing-column .pr20-pr-table-list {
	padding-top: 70px;
	text-align: center;
}

.pr20-pricing-right .pr20-pricing-column .pr20-pr-table-list ul li {
	color: #666666;
	text-transform: capitalize;
}

.pr20-pricing-right .pr20-pricing-column .pr20-pr-table-list ul li+li {
	margin-top: 10px;
}

.pr20-pricing-right .pr20-pricing-column .pr20-primary-btn {
	text-align: center;
	margin-top: 30px;
}

.pr20-pricing-right .pr20-pricing-column .pr20-primary-btn.pr20-pr-btn a {
	background-image: none;
	background-color: #1a0b60;
}

.pr20-pricing-right .pr20-pricing-column .pr20-primary-btn.pr20-pr-btn a::after {
	opacity: 0;
}

.pr20-pricing-right .pr20-pricing-column .pr20-primary-btn.pr20-pr-btn a:hover::after {
	opacity: 1;
}

.pr20-sf-feature {
	padding: 0 0 70px 0;
	background-color: #f7f9ff;
	position: relative;
}

.pr20-sf-feature .pr20-sf-shape-3 {
	position: absolute;
	left: 0;
	bottom: 0;
}

.pr20-sf-feature-right .pr20-sf-feature-list .pr20-sf-item {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-shadow: 0px 14px 50px 0px rgba(135, 135, 135, 0.15);
	box-shadow: 0px 14px 50px 0px rgba(135, 135, 135, 0.15);
	padding: 20px;
	background-color: #ffffff;
	margin-bottom: 20px;
}

.pr20-sf-feature-right .pr20-sf-feature-list .pr20-sf-item .pr20-icon-wrapper {
	margin-right: 20px;
}

.pr20-sf-feature-right .pr20-sf-feature-list .pr20-sf-item .pr20-icon-wrapper i {
	color: #0096ff;
	font-size: 36px;
	display: inline-block;
	line-height: 0;
}

.pr20-sf-feature-right .pr20-sf-feature-list .pr20-sf-item .pr20-pera-txt p {
	color: #1a0b60;
}

.pr20-get-in-touch {
	padding: 30px 0;
}

@media (max-width: 991.98px) {
	.pr20-get-in-touch {
		padding: 100px 0;
	}
}

.pr20-git-left span {
	color: #ffffff;
	text-transform: capitalize;
	margin-bottom: 20px;
	display: inline-block;
}

.pr20-git-left h3 {
	color: #ffffff;
	margin-bottom: 30px;
}

.pr20-git-right {
	position: relative;
}

@media (max-width: 991.98px) {
	.pr20-git-right {
		display: none;
	}
}

.pr20-git-right img {
	width: 240px;
}

.pr20-git-right .pr20-git-man {
	position: absolute;
	top: -30px;
	right: 0;
}

.pr20-testimonial-section {
	padding: 100px 0;
}

.pr20-testimonial-slider .slick-list {
	margin: 0 -15px;
	padding-top: 20px;
	padding-bottom: 20px;
}

.pr20-testimonial-slider .slick-slide {
	margin: 0 15px;
}

.pr20-testimonial-slider .slick-dots {
	display: inline-block;
	position: absolute;
	bottom: 32px;
	right: -92px;
	z-index: 10;
}

.pr20-testimonial-slider .slick-dots li {
	display: inline-block;
	margin-right: 6px;
	cursor: pointer;
}

.pr20-testimonial-slider .slick-dots li button {
	border: 0;
	font-size: 0;
	width: 8px;
	height: 8px;
	background-color: #1a0b60;
	border-radius: 50%;
	padding: 0;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-testimonial-slider .slick-dots li.slick-active button {
	width: 25px;
	border-radius: 6px;
	background-color: #0096ff;
}

.pr20-testimonial-slider .pr20-tt-item {
	position: relative;
	background-color: #ffffff;
	-webkit-box-shadow: 0px 10px 25px 0px rgba(135, 135, 135, 0.15);
	box-shadow: 0px 10px 25px 0px rgba(135, 135, 135, 0.15);
	padding: 30px;
}

.pr20-testimonial-slider .pr20-tt-item span.quote {
	position: absolute;
	width: 70px;
	right: 10px;
	top: 0;
	display: inline-block;
}

.pr20-testimonial-slider .pr20-tt-item .pr20-tt-top {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 20px;
}

.pr20-testimonial-slider .pr20-tt-item .pr20-tt-top .pr20-img-container {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	overflow: hidden;
	background-color: red;
}

.pr20-testimonial-slider .pr20-tt-item .pr20-tt-top .pr20-tt-title {
	margin-left: 20px;
}

.pr20-testimonial-slider .pr20-tt-item .pr20-tt-top .pr20-tt-title h4 {
	font-size: 22px;
	margin-bottom: 6px;
}

.pr20-testimonial-slider .pr20-tt-item .pr20-tt-top .pr20-tt-title span {
	color: #1a0b60;
	font-weight: 500;
	text-transform: capitalize;
}

@media (max-width: 991.98px) {
	.pr20-testimonial-content {
		margin-top: 60px;
	}
}

.pr20-work-process {
	position: relative;
	overflow: visible;
}

.pr20-work-process .pr20-wk-shape-1 {
	position: absolute;
	left: 0;
	bottom: -200px;
	width: 300px;
}

.pr20-wp-content {
	margin-top: 30px;
}

.pr20-wp-column {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 30px;
}

@media (max-width: 767.98px) {
	.pr20-wp-column {
		-webkit-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
	}
}

.pr20-wp-column .pr20-icon-wrapper i {
	width: 90px;
	height: 90px;
	border-radius: 50%;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	font-size: 40px;
	background-image: -webkit-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: -o-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: linear-gradient(290deg, #0096ff 0%, #00e9f0 100%);
	color: #ffffff;
	position: relative;
	z-index: 1;
	-webkit-box-shadow: 0px 14px 30px 0px rgba(0, 173, 251, 0.3);
	box-shadow: 0px 14px 30px 0px rgba(0, 173, 251, 0.3);
}

.pr20-wp-column .pr20-icon-wrapper i::after {
	content: '';
	width: 100%;
	height: 100%;
	background-image: -webkit-linear-gradient(20deg, #0096ff 0%, #00e9f0 100%);
	background-image: -o-linear-gradient(20deg, #0096ff 0%, #00e9f0 100%);
	background-image: linear-gradient(-290deg, #0096ff 0%, #00e9f0 100%);
	position: absolute;
	top: 0;
	left: 0;
	border-radius: 50%;
	top: 0;
	left: 0;
	z-index: -1;
	opacity: 0;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-wp-column .pr20-wpcl-content {
	padding: 40px 20px 40px 50px;
	text-align: center;
	background-color: #ffffff;
	-webkit-box-shadow: 0px 13px 60px 0px rgba(135, 135, 135, 0.2);
	box-shadow: 0px 13px 60px 0px rgba(135, 135, 135, 0.2);
	margin-left: -40px;
}

.pr20-wp-column .pr20-wpcl-content .pr20-headline {
	margin-bottom: 10px;
}

.pr20-wp-column:hover .pr20-icon-wrapper i::after {
	opacity: 1;
}

.pr20-blog-section {
	padding: 70px 0 160px 0;
}

.pr20-blog-section .pr20-title-area {
	margin-bottom: 0;
}

.pr20-blog-section .pr20-title-area h3 {
	margin-bottom: 0;
}

.pr20-blog-section .pr20-blog-top-right .pr20-primary-btn {
	text-align: right;
}

@media (max-width: 991.98px) {
	.pr20-blog-section .pr20-blog-top-right .pr20-primary-btn {
		text-align: left;
		margin-top: 60px;
	}
}

.pr20-blog-section .pr20-blog-top-right .pr20-primary-btn a {
	margin-bottom: 10px;
}

.pr20-blog-content {
	margin-top: 60px;
}

.pr20-blog-content .pr20-blog-column {
	margin-bottom: 60px;
}

.pr20-blog-content .pr20-blog-column .pr20-thumb-wrapper {
	position: relative;
	border-radius: 10px;
	overflow: hidden;
}

.pr20-blog-content .pr20-blog-column .pr20-thumb-wrapper::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	height: 100%;
	width: 100%;
	background-image: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 0, 0, 0.9)), to(rgba(255, 255, 255, 0.1)));
	background-image: -webkit-linear-gradient(bottom, rgba(0, 0, 0, 0.9), rgba(255, 255, 255, 0.1));
	background-image: -o-linear-gradient(bottom, rgba(0, 0, 0, 0.9), rgba(255, 255, 255, 0.1));
	background-image: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(255, 255, 255, 0.1));
	z-index: 1;
	opacity: 0;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-blog-content .pr20-blog-column .pr20-thumb-wrapper img {
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-blog-content .pr20-blog-column .pr20-thumb-wrapper .pr20-blog-date {
	position: absolute;
	bottom: 0;
	left: 0;
	display: inline-block;
	background-color: #0096ff;
	padding: 10px 20px;
	z-index: 2;
}

.pr20-blog-content .pr20-blog-column .pr20-thumb-wrapper .pr20-blog-date h4 {
	color: #ffffff;
}

.pr20-blog-content .pr20-blog-column .pr20-thumb-wrapper .pr20-blog-date span {
	color: #ffffff;
}

.pr20-blog-content .pr20-blog-column .pr20-blog-meta {
	margin: 20px 0;
}

.pr20-blog-content .pr20-blog-column .pr20-blog-meta span {
	color: #1a0b60;
	font-size: 15px;
}

.pr20-blog-content .pr20-blog-column .pr20-blog-meta span i {
	margin-right: 6px;
}

.pr20-blog-content .pr20-blog-column .pr20-blog-meta span+span {
	margin-left: 30px;
}

.pr20-blog-content .pr20-blog-column .pr20-readmore-btn {
	margin-top: 20px;
}

.pr20-blog-content .pr20-blog-column .pr20-readmore-btn a {
	font-size: 15px;
	color: #1a0b60;
	font-weight: 600;
	font-family: "Lexend", sans-serif;
	text-transform: capitalize;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-blog-content .pr20-blog-column .pr20-readmore-btn a i {
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
}

.pr20-blog-content .pr20-blog-column:hover .pr20-thumb-wrapper img {
	-webkit-transform: scale(1.1);
	-ms-transform: scale(1.1);
	transform: scale(1.1);
}

.pr20-blog-content .pr20-blog-column:hover .pr20-thumb-wrapper::after {
	opacity: 1;
}

.pr20-blog-content .pr20-blog-column:hover .pr20-readmore-btn a {
	color: #0096ff;
}

.pr20-blog-content .pr20-blog-column:hover .pr20-readmore-btn a i {
	-webkit-transform: translateX(5px);
	-ms-transform: translateX(5px);
	transform: translateX(5px);
}

.pr20-footer-section {
	padding: 100px 0 0 0;
}

.pr20-footer-section .pr20-footer-top {
	margin-top: -200px;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area {
	background-color: #ffffff;
	border-radius: 6px;
	-webkit-box-shadow: 0px 13px 60px 0px rgba(135, 135, 135, 0.3);
	box-shadow: 0px 13px 60px 0px rgba(135, 135, 135, 0.3);
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-left {
	padding: 40px 30px;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-left .pr20-title-area {
	margin: 0;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right {
	padding: 40px 30px;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right form {
	width: 100%;
	position: relative;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right form input[type="email"] {
	width: 100%;
	padding: 15px 180px 15px 12px;
	border: 1px solid #e7e7e7;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right form input[type="email"]::-webkit-input-placeholder {
	color: #e7e7e7;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right form input[type="email"]:-ms-input-placeholder {
	color: #e7e7e7;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right form input[type="email"]::-ms-input-placeholder {
	color: #e7e7e7;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right form input[type="email"]::placeholder {
	color: #e7e7e7;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right form .pr20-submit-btn {
	position: absolute;
	top: 50%;
	right: 0;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	border: 0;
	width: 170px;
	height: 55px;
	color: #ffffff;
	background-color: transparent;
	display: inline-block;
	text-align: center;
	line-height: 52px;
	border-radius: 4px;
	background-image: -webkit-linear-gradient(20deg, #0096ff 0%, #00e9f0 100%);
	background-image: -o-linear-gradient(20deg, #0096ff 0%, #00e9f0 100%);
	background-image: linear-gradient(-290deg, #0096ff 0%, #00e9f0 100%);
	z-index: 1;
	font-family: "Lexend", sans-serif;
	font-weight: 600;
	text-transform: capitalize;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right form .pr20-submit-btn::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image: -webkit-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: -o-linear-gradient(160deg, #0096ff 0%, #00e9f0 100%);
	background-image: linear-gradient(290deg, #0096ff 0%, #00e9f0 100%);
	z-index: -1;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
	border-radius: 4px;
}

.pr20-footer-section .pr20-footer-top .pr20-ft-sb-area .pr20-footer-sb-right form .pr20-submit-btn:hover::after {
	opacity: 0;
}

.pr20-footer-bottom {
	padding-top: 60px;
}

.pr20-footer-widget {
	margin-bottom: 60px;
}

.pr20-footer-widget .logo {
	width: 170px;
	display: inline-block;
	margin-bottom: 20px;
}

.pr20-footer-widget .pr20-pera-txt p {
	color: #ffffff;
}

.pr20-footer-widget .pr20-footer-socials {
	margin-top: 30px;
}

.pr20-footer-widget .pr20-footer-socials a {
	width: 30px;
	height: 30px;
	text-align: center;
	line-height: 30px;
	background-color: transparent;
	color: #ffffff;
	border-radius: 50%;
	border: 1px solid #ffffff;
	-webkit-transition: all 0.3s ease-in;
	-o-transition: all 0.3s ease-in;
	transition: all 0.3s ease-in;
	display: inline-block;
}

.pr20-footer-widget .pr20-footer-socials a+a {
	margin-left: 8px;
}

.pr20-footer-widget .pr20-footer-socials a:hover {
	background-color: #0096ff;
	border-color: #0096ff;
}

.pr20-footer-widget .pr20-headline {
	margin-bottom: 20px;
}

.pr20-footer-widget .pr20-headline h4 {
	color: #ffffff;
}

.pr20-footer-widget .pr20-footer-links ul li+li {
	margin-top: 10px;
}

.pr20-footer-widget .pr20-footer-links ul li a {
	color: #ffffff;
	text-transform: capitalize;
	position: relative;
	padding-left: 20px;
}

.pr20-footer-widget .pr20-footer-links ul li a:after {
	content: '\f101';
	position: absolute;
	top: 1px;
	left: 0px;
	color: #ffffff;
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
}

.pr20-footer-widget .pr20-footer-links ul li a:hover {
	padding-left: 25px;
}

.pr20-footer-widget .pr20-footer-address li {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	color: #ffffff;
}

.pr20-footer-widget .pr20-footer-address li i {
	margin-top: 6px;
}

.pr20-footer-widget .pr20-footer-address li span {
	margin-left: 10px;
}

.pr20-footer-widget .pr20-footer-address li+li {
	margin-top: 10px;
}

.pr20-footer-widget .pr20-footer-blog li {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.pr20-footer-widget .pr20-footer-blog li+li {
	margin-top: 6px;
}

@media (max-width: 991.98px) {
	.pr20-footer-widget .pr20-footer-blog li+li {
		margin-top: 10px;
	}
}

.pr20-footer-widget .pr20-footer-blog li .pr20-blog-thumb {
	margin-right: 10px;
}

.pr20-footer-widget .pr20-footer-blog li .pr20-blog-thumb img {
	border-radius: 5px;
	width: 70px;
}

.pr20-footer-widget .pr20-footer-blog li .pr20-blog-title h6 {
	color: #ffffff;
	font-size: 16px;
	font-weight: 600;
}

.pr20-footer-widget .pr20-footer-blog li .pr20-blog-title .date {
	color: #ffffff;
	display: inline-block;
	margin-top: 6px;
}

.pr20-footer-copyright {
	padding-top: 80px;
	padding-bottom: 5px;
}

@media (max-width: 991.98px) {
	.pr20-footer-copyright {
		padding-top: 60px;
	}
}

.pr20-footer-copyright hr {
	border: 0;
	border-bottom: 1px solid #ffffff9e;
	margin: 0;
}

@media (max-width: 991.98px) {
	.pr20-footer-copyright hr {
		margin-bottom: 20px;
	}
}

.pr20-footer-copyright .pr20-pera-txt p {
	color: #ffffff;
	font-size: 15px;
}

.pr20-footer-copyright .pr20-copyright-links {
	text-align: right;
}

@media (max-width: 991.98px) {
	.pr20-footer-copyright .pr20-copyright-links {
		text-align: left;
	}
}

.pr20-footer-copyright .pr20-copyright-links a {
	color: #ffffff;
	padding: 10px 0;
	display: inline-block;
	position: relative;
}

.pr20-footer-copyright .pr20-copyright-links a+a {
	margin-left: 15px;
}

.pr20-footer-copyright .pr20-copyright-links a+a::after {
	content: '';
	position: absolute;
	top: 50%;
	left: -10px;
	width: 1px;
	height: 20px;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	background-color: #ffffff9e;
}

.pr20-footer-copyright .pr20-copyright-links a:hover {
	text-decoration: underline;
}