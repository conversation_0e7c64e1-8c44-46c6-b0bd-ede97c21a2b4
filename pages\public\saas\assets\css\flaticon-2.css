	/*
  	Flaticon icon font: Flaticon
  	Creation date: 01/04/2020 11:54
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("../fonts-2/Flaticon.eot");
  src: url("../fonts-2/Flaticon.eot?#iefix") format("embedded-opentype"),
       url("../fonts-2/Flaticon.woff2") format("woff2"),
       url("../fonts-2/Flaticon.woff") format("woff"),
       url("../fonts-2/Flaticon.ttf") format("truetype"),
       url("../fonts-2/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("../fonts-2/Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
  font-style: normal;
}

.flaticon-diamond:before { content: "\f100"; }
.flaticon-settings:before { content: "\f101"; }
.flaticon-user:before { content: "\f102"; }
.flaticon-pencil:before { content: "\f103"; }
.flaticon-link:before { content: "\f104"; }
.flaticon-check:before { content: "\f105"; }
.flaticon-right-arrow:before { content: "\f106"; }
.flaticon-left-arrow:before { content: "\f107"; }
.flaticon-analytics:before { content: "\f108"; }