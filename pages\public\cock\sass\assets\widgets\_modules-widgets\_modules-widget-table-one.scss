/*
    ==================
        Transaction
    ==================
*/

.widget-table-one {
  .widget-heading {
    display: flex;
  }

  .widget-content {}

  .transactions-list {
    padding: 12px 12px;
    border: 1px dashed $m-color_4;
    border-radius: 6px;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease;

    &:not(:last-child) {
      margin-bottom: 15px;
    }

    &:hover {
      -webkit-transform: translateY(-1px) scale(1.01);
      transform: translateY(-1px) scale(1.01);
    }

    .t-item {
      display: flex;
      justify-content: space-between;

      .t-company-name {
        display: flex;
      }

      .t-icon {
        margin-right: 12px;

        .avatar {
          position: relative;
          display: inline-block;
          width: 38px;
          height: 38px;
          font-size: 14px;
          font-weight: 600;
          letter-spacing: 1px;

          .avatar-title {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background-color: $l-danger;
            color: $danger;
          }
        }

        .icon {
          position: relative;
          display: inline-block;
          padding: 10px;
          background-color: $l-warning;
          border-radius: 50%;

          svg {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 19px;
            height: 19px;
            color: $warning;
            stroke-width: 2;
          }
        }
      }

      .t-name {
        align-self: center;

        h4 {
          font-size: 15px;
          letter-spacing: 0px;
          font-weight: 600;
          margin-bottom: 0;
        }

        .meta-date {
          font-size: 12px;
          margin-bottom: 0;
          font-weight: 600;
          color: $m-color_6;
        }
      }

      .t-rate {
        align-self: center;

        p {
          margin-bottom: 0;
          font-size: 13px;
          letter-spacing: 0px;
        }

        svg {
          width: 14px;
          height: 14px;
          vertical-align: baseline;
        }

        &.rate-inc {
          p, svg {
            color: $m-color_14;
          }
        }

        &.rate-dec {
          p, svg {
            color: $danger;
          }
        }
      }
    }
  }
}