@import url("https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,600,500,700|Roboto:100,300,400,500,700&display=swap");
@keyframes fadeFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeFromUp {
  animation-name: fadeFromUp;
}

.fadeFromRight {
  animation-name: fadeFromRight;
}

.fadeFromLeft {
  animation-name: fadeFromLeft;
}
/*global area*/
/*----------------------------------------------------*/
.seo-2-home {
  margin: 0;
  padding: 0;
  color: #7990a4;
  font-size: 16px;
  overflow-x: hidden;
  line-height: 1.625;
  font-family: "Roboto";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

.seo-2-home::selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.seo-2-home::-moz-selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.container {
  max-width: 1200px;
}

.ul-li ul {
  margin: 0;
  padding: 0;
}
.ul-li ul li {
  list-style: none;
  display: inline-block;
}

.ul-li-block ul {
  margin: 0;
  padding: 0;
}
.ul-li-block ul li {
  list-style: none;
  display: block;
}

div#seo-2-preloader {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999;
  width: 100%;
  height: 100%;
  overflow: visible;
  background-color: #f1f2f3;
  background: #fff url("../img/pre.svg") no-repeat center center;
}

[data-background] {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

a {
  color: inherit;
  text-decoration: none;
}
a:hover, a:focus {
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

section {
  overflow: hidden;
}

button {
  cursor: pointer;
}

.form-control:focus,
button:visited,
button.active,
button:hover,
button:focus,
input:visited,
input.active,
input:hover,
input:focus,
textarea:hover,
textarea:focus,
a:hover,
a:focus,
a:visited,
a.active,
select,
select:hover,
select:focus,
select:visited {
  outline: none;
  box-shadow: none;
  text-decoration: none;
  color: inherit;
}

.form-control {
  box-shadow: none;
}

.pera-content p {
  margin-bottom: 0;
}
@keyframes zooming {
  0% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.05, 1.05);
  }
  100% {
    transform: scale(1, 1);
  }
}
.zooming {
  animation: zooming 18s infinite both;
}

.seo-2-headline h1,
.seo-2-headline h2,
.seo-2-headline h3,
.seo-2-headline h4,
.seo-2-headline h5,
.seo-2-headline h6 {
  margin: 0;
  font-family: "Poppins";
}

.seo-2-scrollup {
  width: 55px;
  right: 20px;
  z-index: 5;
  height: 55px;
  bottom: 20px;
  display: none;
  position: fixed;
  line-height: 55px;
  border-radius: 100%;
  background-color: #01e07b;
}
.seo-2-scrollup i {
  color: #fff;
  font-size: 20px;
}

.seo-2-section-title span {
  font-size: 14px;
  font-weight: 700;
  background: linear-gradient(90deg, #0160e7 0%, #04bdef 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.seo-2-section-title h2 {
  color: #003a6c;
  font-size: 48px;
  font-weight: 700;
  padding-top: 5px;
  line-height: 1.208;
}
.seo-2-section-title p {
  color: #7990a4;
  font-size: 20px;
  padding-top: 10px;
}

/*---------------------------------------------------- */
/*Header area*/
/*----------------------------------------------------*/
.seo-2-main-header {
  top: 0;
  width: 100%;
  z-index: 10;
  padding-top: 40px;
  position: absolute;
}
.seo-2-main-header .seo-2-brand-logo {
  margin-top: 5px;
  padding-left: 60px;
}
.seo-2-main-header .dropdown {
  position: relative;
}
.seo-2-main-header .dropdown:after {
  top: -2px;
  color: #fff;
  right: -14px;
  content: "+";
  font-size: 18px;
  font-weight: 700;
  position: absolute;
  transition: 0.3s all ease-in-out;
}
.seo-2-main-header .dropdown .dropdown-menu {
  top: 65px;
  left: 0;
  opacity: 0;
  z-index: 2;
  margin: 0px;
  padding: 0px;
  height: auto;
  width: 200px;
  border: none;
  display: block;
  border-radius: 0;
  overflow: hidden;
  visibility: hidden;
  position: absolute;
  background-color: #fff;
  transition: all 0.4s ease-in-out;
  border-bottom: 2px solid #01e07b;
  box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
}
.seo-2-main-header .dropdown .dropdown-menu li {
  width: 100%;
  margin-left: 0;
  border-bottom: 1px solid #e5e5e5;
}
.seo-2-main-header .dropdown .dropdown-menu li a {
  width: 100%;
  color: #343434;
  display: block;
  font-size: 14px;
  padding: 10px 25px;
  position: relative;
  transition: 0.3s all ease-in-out;
}
.seo-2-main-header .dropdown .dropdown-menu li a:before {
  display: none;
}
.seo-2-main-header .dropdown .dropdown-menu li a:after {
  left: 10px;
  top: 16px;
  width: 8px;
  height: 8px;
  content: "";
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}
.seo-2-main-header .dropdown .dropdown-menu li a:hover {
  background-color: #01e07b;
  color: #fff;
}
.seo-2-main-header .dropdown .dropdown-menu li a:hover:after {
  transform: scale(1);
}
.seo-2-main-header .dropdown .dropdown-menu li:last-child {
  border-bottom: none;
}
.seo-2-main-header .dropdown:hover .dropdown-menu {
  top: 45px;
  opacity: 1;
  visibility: visible;
}
.seo-2-main-header .seo-2-sidebar-toggle {
  left: 0;
  top: 5px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  position: absolute;
  border-radius: 100%;
  background-color: #003a6c;
}
.seo-2-main-header .seo-2-sidebar-toggle span {
  display: inline-block;
  padding-top: 10px;
  text-align: center;
}
.seo-2-main-header .seo-2-sidebar-toggle span svg {
  fill: #fff;
  height: 17px;
}

.seo-2-main-menu-item .navbar-nav {
  display: inherit;
}
.seo-2-main-menu-item .seo-2-main-navigation {
  padding-top: 15px;
  display: inline-block;
}
.seo-2-main-menu-item .seo-2-main-navigation li {
  margin: 0px 25px;
}
.seo-2-main-menu-item .seo-2-main-navigation li a {
  color: #fff;
  font-size: 16px;
  display: inline;
  font-weight: 700;
  padding-bottom: 30px;
}
.seo-2-main-menu-item .seo-2-main-navigation li a.active {
  color:  #003a6c;
}
.seo-2-main-menu-item .seo-2-header-btn {
  height: 50px;
  width: 170px;
  line-height: 48px;
  margin-left: 45px;
  border-radius: 30px;
  border: 2px solid #ffffff5c;
  transition: 0.3s all ease-in-out;
}
.seo-2-main-menu-item .seo-2-header-btn a {
  color: #fff;
  width: 100%;
  display: block;
  font-size: 15px;
  font-weight: 600;
  transition: 0.3s all ease-in-out;
}
.seo-2-main-menu-item .seo-2-header-btn:hover {
  background-color: #003a6c;
  border: 2px solid #003a6c;
}
.seo-2-main-menu-item .seo-2-header-btn:hover a {
  color: #fff;
}

.seo-2-side-bar-toggle {
  z-index: 1;
  width: 70px;
  height: 70px;
  right: 240px;
  bottom: -60px;
  cursor: pointer;
  line-height: 65px;
  text-align: center;
  position: absolute;
  border-radius: 10px;
  background-color: #fff;
  border: 2px solid #7990a4;
}
.seo-2-side-bar-toggle:before {
  top: 8px;
  left: 8px;
  content: "";
  width: 50px;
  height: 50px;
  z-index: -1;
  position: absolute;
  border-radius: 10px;
  background-color: #fc5a84;
}
.seo-2-side-bar-toggle i {
  color: #fff;
  font-size: 26px;
}

.sm-side_inner_content {
  top: 0px;
  bottom: 0;
  right: -420px;
  height: 110vh;
  z-index: 101;
  position: fixed;
  width: 400px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 50px 50px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s;
}
.sm-side_inner_content p {
  text-align: left;
}
.sm-side_inner_content .side_inner_logo {
  margin: 30px 0px;
}
.sm-side_inner_content .side_contact {
  margin-bottom: 30px;
}
.sm-side_inner_content .side_contact .social_widget {
  margin-bottom: 40px;
}
.sm-side_inner_content .side_contact .social_widget h3 {
  font-size: 20px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}
.sm-side_inner_content .side_contact .social_widget li {
  color: #fff;
  width: 30px;
  height: 30px;
  margin: 0px 3px;
  line-height: 30px;
  text-align: center;
  border-radius: 4px;
  background-color: #7990a4;
}
.sm-side_inner_content .side_contact .social_widget li i {
  font-size: 14px;
}
.sm-side_inner_content .side_contact .seo-2-sidebar-gallary {
  margin-bottom: 25px;
}
.sm-side_inner_content .side_contact .seo-2-sidebar-gallary h3 {
  font-size: 20px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}
.sm-side_inner_content .side_contact .seo-2-sidebar-gallary li {
  float: left;
  margin: 5px 3px;
}
.sm-side_inner_content .side_copywright {
  font-size: 14px;
}
.sm-side_inner_content .close_btn {
  top: 30px;
  right: 20px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  line-height: 40px;
  text-align: center;
  position: absolute;
  background-color: #f5f5f5;
  transition: 0.3s all ease-in-out;
}
.sm-side_inner_content .close_btn i {
  font-size: 14px;
}
.sm-side_inner_content .close_btn:hover {
  background-color: #fc5a84;
}
.sm-side_inner_content .close_btn:hover i {
  color: #fff;
}

.seo-2-sidebar-inner.wide_side_on .sm-side_inner_content {
  right: -15px;
  z-index: 99;
  transition: all 0.7s;
}

.seo-2-sidebar-inner .side_overlay {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  width: 100%;
  z-index: 11;
  height: 110vh;
  visibility: hidden;
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  transition: all 0.3s ease-in-out;
  cursor: url(../img/cl.png), auto;
}

.body_overlay_on {
  overflow: hidden;
}

.seo-2-sidebar-inner.wide_side_on .side_overlay {
  opacity: 1;
  visibility: visible;
}

.seo-2-sticky-on {
  top: 0px;
  position: fixed;
  padding: 10px 0px;
  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 0px 18px 1px rgba(0, 0, 0, 0.1);
  background: linear-gradient(45deg, #0160e7 0%, #04bdef 100%);
}
.seo-2-sticky-on .seo-2-main-menu-item .seo-2-main-navigation {
  padding-top: 15px;
}

.seo-2-main-header .seo-2-mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 280px;
  overflow-y: scroll;
  background-color: #1b0234;
  padding: 40px 0px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s ease-in;
}
.seo-2-main-header .seo-2-mobile_menu_content .seo-2-mobile-main-navigation {
  width: 100%;
}
.seo-2-main-header .seo-2-mobile_menu_content .seo-2-mobile-main-navigation .navbar-nav {
  width: 100%;
}
.seo-2-main-header .seo-2-mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}
.seo-2-main-header .seo-2-mobile_menu_content .seo-2-mobile-main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid #36125a;
}
.seo-2-main-header .seo-2-mobile_menu_content .seo-2-mobile-main-navigation .navbar-nav li:first-child {
  border-bottom: 1px solid #36125a;
}
.seo-2-main-header .seo-2-mobile_menu_content .seo-2-mobile-main-navigation .navbar-nav li a {
  color: #afafaf;
  padding: 0;
  width: 100%;
  display: block;
  font-weight: 700;
  font-size: 14px;
  padding: 10px 30px;
  font-family: "Poppins";
  text-transform: uppercase;
}
.seo-2-main-header .seo-2-mobile_menu_content .m-brand-logo {
  width: 160px;
  margin: 0 auto;
  margin-bottom: 30px;
}
.seo-2-main-header .seo-2-mobile_menu_wrap.mobile_menu_on .seo-2-mobile_menu_content {
  right: 0px;
  transition: all 0.7s ease-out;
}
.seo-2-main-header .mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: 0%;
  height: 120vh;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.5s ease-in-out;
}
.seo-2-main-header .mobile_menu_overlay_on {
  overflow: hidden;
}
.seo-2-main-header .seo-2-mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  opacity: 1;
  visibility: visible;
}
.seo-2-main-header .seo-2-mobile_menu_button {
  right: 0;
  top: -40px;
  z-index: 5;
  color: #02e079;
  display: none;
  cursor: pointer;
  font-size: 30px;
  line-height: 40px;
  position: absolute;
  text-align: center;
}
.seo-2-main-header .seo-2-mobile_menu .seo-2-mobile-main-navigation .navbar-nav li a:after {
  display: none;
}
.seo-2-main-header .seo-2-mobile_menu .seo-2-mobile-main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.seo-2-main-header .seo-2-mobile_menu .seo-2-mobile_menu_content .seo-2-mobile-main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
  width: 100%;
  background-color: transparent;
}
.seo-2-main-header .seo-2-mobile_menu .seo-2-mobile_menu_content .seo-2-mobile-main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 0 20px;
  line-height: 1;
}
.seo-2-main-header .seo-2-mobile_menu .dropdown {
  position: relative;
}
.seo-2-main-header .seo-2-mobile_menu .dropdown .dropdown-btn {
  position: absolute;
  top: 6px;
  right: 10px;
  height: 30px;
  color: #afafaf;
  line-height: 22px;
  padding: 5px 10px;
  border: 1px solid #480b86;
}
.seo-2-main-header .seo-2-mobile_menu .dropdown:after {
  display: none;
}
.seo-2-main-header .seo-2-mobile_menu .seo-2-mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

/*---------------------------------------------------- */
/*banner area*/
/*----------------------------------------------------*/
@keyframes man-updown {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(0px);
  }
}
@keyframes man-updown {
  0% {
    transform: translateY(1px);
  }
  100% {
    transform: translateY(-1px);
  }
}
@keyframes man-updown {
  0% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(10px);
  }
}
@keyframes floatY {
  0% {
    transform: translatey(0px);
  }
  50% {
    transform: translatey(-20px);
  }
  100% {
    transform: translatey(0px);
  }
}
@keyframes floatX {
  0% {
    transform: translateX(140px);
  }
  50% {
    transform: translateX(60px);
  }
  100% {
    transform: translateX(140px);
  }
}
.seo-2-banner-content {
  z-index: 1;
  margin: 0 auto;
  max-width: 825px;
  position: relative;
  padding: 175px 0px 580px;
}
.seo-2-banner-content .seo-2-banner-text span {
  color: #fff;
  display: block;
  font-size: 18px;
  font-weight: 700;
  padding-bottom: 10px;
}
.seo-2-banner-content .seo-2-banner-text h1 {
  color: #fff;
  font-size: 72px;
  font-weight: 700;
}
.seo-2-banner-content .banner-input-field {
  margin-top: 25px;
  justify-content: center;
}
.seo-2-banner-content .banner-input-field .seo-2-form-input {
  margin: 0px 8px;
}
.seo-2-banner-content .banner-input-field .seo-2-form-input span {
  top: 23px;
  left: 35px;
  color: #fff;
  position: absolute;
}
.seo-2-banner-content .banner-input-field .seo-2-form-input input {
  border: none;
  height: 70px;
  width: 290px;
  color: #fff;
  padding-left: 65px;
  border-radius: 35px;
  background-color: #35a3ec;
}
.seo-2-banner-content .banner-input-field .seo-2-form-input input::placeholder {
  color: #a3daff;
}
.seo-2-banner-content .banner-input-field button {
  color: #fff;
  width: 200px;
  height: 70px;
  border: none;
  margin-left: 8px;
  font-weight: 700;
  border-radius: 35px;
  background-color: #003a6c;
  border: 2px solid #003a6c;
  transition: 0.3s all ease-in-out;
}
.seo-2-banner-content .banner-input-field button:hover {
  border: 2px solid #fff;
  background-color: transparent;
}

.seo-2-banner-section {
  overflow: visible;
  margin-bottom: 180px;
}
.seo-2-banner-section .seo-2-banner-vector {
  right: 0;
  left: 120px;
  bottom: -180px;
  position: absolute;
  animation: man-updown 2s infinite alternate;
}
.seo-2-banner-section .seo-2-banner-shape {
  top: 0;
  left: 0;
}
.seo-2-banner-section .seo-2-banner-shape.shape1 {
  animation: floatY 3s ease-in-out infinite;
}
.seo-2-banner-section .seo-2-banner-shape.shape2 {
  left: 150px;
  animation: floatX 10s ease-in-out infinite;
}

/*---------------------------------------------------- */
/*Service area*/
/*----------------------------------------------------*/
.seo-2-service-section {
  padding: 120px 0px 20px;
}

.seo-2-service-innerbox {
  padding: 50px 40px;
  box-shadow: 0px 16px 32px 0px rgba(24, 37, 50, 0.06);
}
.seo-2-service-innerbox .seo-2-service-icon {
  margin-bottom: 35px;
  transition: all 0.35s cubic-bezier(0.38, 3, 0.57, 1.6);
  transform: translate3d(0px, 0, 0);
}
.seo-2-service-innerbox .seo-2-service-text h3 {
  color: #003a6c;
  font-size: 24px;
  font-weight: 700;
  padding-bottom: 20px;
}
.seo-2-service-innerbox .seo-2-service-text .seo-2-service-btn {
  margin-top: 25px;
  color: #003a6c;
}
.seo-2-service-innerbox .seo-2-service-text .seo-2-service-btn i {
  width: 50px;
  height: 50px;
  z-index: 2;
  line-height: 50px;
  border-radius: 100%;
  background-color: #f6faff;
  transition: 0.4s all ease-in-out;
  position: relative;
}
.seo-2-service-innerbox .seo-2-service-text .seo-2-service-btn i:after {
  content: "";
  top: 0;
  left: 0;
  z-index: -1;
  height: 100%;
  width: 100%;
  position: absolute;
  transform: scale(0);
  border-radius: 100%;
  transition: 0.3s all ease-in-out;
  background-image: linear-gradient(-45deg, #0160e7 0%, #04bdef 100%);
}
.seo-2-service-innerbox .seo-2-service-text .seo-2-service-btn span {
  top: 15px;
  opacity: 0;
  left: 115px;
  font-size: 16px;
  font-weight: 700;
  position: absolute;
  visibility: hidden;
  transition: 0.4s all ease-in-out;
}
.seo-2-service-innerbox .seo-2-service-text .seo-2-service-btn:hover i {
  color: #fff;
  transform: translateX(-40px);
}
.seo-2-service-innerbox .seo-2-service-text .seo-2-service-btn:hover i:after {
  transform: scale(1);
}
.seo-2-service-innerbox .seo-2-service-text .seo-2-service-btn:hover span {
  opacity: 1;
  left: 135px;
  visibility: visible;
}
.seo-2-service-innerbox:hover .seo-2-service-icon {
  transition: all 0.35s cubic-bezier(0.38, 3, 0.57, 1.6);
  transform: translate3d(0px, -6px, 0);
}

.seo-2-about-img {
  position: absolute;
}

/*---------------------------------------------------- */
/*About area*/
/*----------------------------------------------------*/
.seo-2-about-section {
  padding: 120px 0px 110px;
}
.seo-2-about-section .seo-2-about-img {
  top: 0;
  left: -215px;
  position: absolute;
  animation: man-updown 2s infinite alternate;
}

.seo-2-about-text-wrap {
  float: right;
  max-width: 570px;
  padding-left: 35px;
}

.seo-2-about-text {
  color: #7990a4;
  font-size: 20px;
  line-height: 1.5;
  margin: 18px 0px 45px;
}

.seo-2-about-list-item {
  margin-bottom: 32px;
}
.seo-2-about-list-item .seo-2-about-list-icon {
  height: 60px;
  width: 60px;
  margin-right: 25px;
}
.seo-2-about-list-item .seo-2-about-list-text h3 {
  color: #003a6c;
  font-size: 18px;
  font-weight: 700;
  padding-bottom: 10px;
}

.seo-2-cta-content {
  border-radius: 10px;
  margin-bottom: 115px;
  background-color: #f5fbff;
  padding: 28px 15px 28px 40px;
}
.seo-2-cta-content p {
  font-weight: 700;
  color: #003a6c;
  font-size: 20px;
  font-family: "Poppins";
}
.seo-2-cta-content p span {
  color: #ff7eb8;
}
.seo-2-cta-content .seo-2-cta-btn {
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  position: absolute;
  line-height: 50px;
  border-radius: 100%;
  background-image: linear-gradient(-45deg, #0160e7 0%, #04bdef 100%);
}
.seo-2-cta-content .seo-2-cta-btn a {
  width: 100%;
  color: #fff;
  display: block;
}
.seo-2-cta-content .seo-2-cta-btn a i {
  transition: 0.4s all ease-in-out;
}
.seo-2-cta-content .seo-2-cta-btn a:hover i {
  transform: rotate(130deg);
}

.seo-2-partner-content {
  margin: 0px -40px;
}
.seo-2-partner-content li {
  padding: 0px 40px;
  margin-bottom: 50px;
}
.seo-2-partner-content li img {
  transition: 0.4s all ease-in-out;
  filter: grayscale(1);
}
.seo-2-partner-content li img:hover {
  filter: grayscale(0);
  transform: scale(1.1);
}

/*---------------------------------------------------- */
/*Portfolio area*/
/*----------------------------------------------------*/
.seo-2-portfolio-section {
  padding-top: 60px;
}
.seo-2-portfolio-section:after {
  left: 0;
  bottom: 0;
  content: "";
  width: 100%;
  height: 55px;
  position: absolute;
  background-color: #000a2a;
}

.seo-2-portfolio-content {
  margin-right: -500px;
}
.seo-2-portfolio-content .owl-nav {
  bottom: 100px;
  left: -500px;
  position: absolute;
}
.seo-2-portfolio-content .owl-nav .owl-prev,
.seo-2-portfolio-content .owl-nav .owl-next {
  width: 60px;
  height: 60px;
  cursor: pointer;
  line-height: 60px;
  text-align: center;
  margin-right: 15px;
  border-radius: 100%;
  display: inline-block;
  background-color: #f5fbff;
  transition: 0.4s all ease-in-out;
}
.seo-2-portfolio-content .owl-nav .owl-prev:hover,
.seo-2-portfolio-content .owl-nav .owl-next:hover {
  color: #fff;
  background-color: #0289eb;
}

.seo-2-portfolio-img-text .seo-2-portfolio-img img {
  transition: 0.5s all ease-in-out;
}
.seo-2-portfolio-img-text .seo-2-portfolio-img:before {
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  opacity: 0;
  height: 100%;
  z-index: 1;
  position: absolute;
  background-color: #000;
  transition: 0.4s all ease-in-out;
}
.seo-2-portfolio-img-text .seo-2-portfolio-text {
  top: 50%;
  left: 0;
  right: 0;
  width: 50px;
  z-index: 2;
  height: 50px;
  margin: 0 auto;
  border-radius: 100%;
  line-height: 50px;
  position: absolute;
  background-color: #fff;
  transform: translateY(-50%) scale(0);
  transition: 0.4s all ease-in-out;
}
.seo-2-portfolio-img-text .seo-2-portfolio-text a {
  width: 100%;
  display: block;
}
.seo-2-portfolio-img-text:hover .seo-2-portfolio-img:before {
  opacity: 0.7;
}
.seo-2-portfolio-img-text:hover .seo-2-portfolio-text {
  transform: translateY(-50%) scale(1);
}

/*---------------------------------------------------- */
/*Boost area*/
/*----------------------------------------------------*/
.seo-2-boost-section {
  padding: 115px 0px;
  background-color: #000a2a;
}
.seo-2-boost-section .seo-2-section-title h2 {
  color: #fff;
}

.seo-2-boost-form {
  flex-wrap: wrap;
  margin-top: 40px;
}
.seo-2-boost-form .seo-2-boost-input {
  margin-right: 20px;
}
.seo-2-boost-form .seo-2-boost-input span {
  top: 23px;
  left: 35px;
  position: absolute;
}
.seo-2-boost-form .seo-2-boost-input input {
  width: 400px;
  height: 70px;
  border: none;
  padding-left: 70px;
  border-radius: 40px;
  background-color: #0a1539;
}
.seo-2-boost-form .seo-2-boost-input input::placeholder {
  color: #566492;
}
.seo-2-boost-form button {
  color: #fff;
  border: none;
  height: 70px;
  width: 330px;
  font-weight: 700;
  border-radius: 40px;
  background-color: #0289eb;
  border: 2px solid #0289eb;
  transition: 0.3s all ease-in-out;
}
.seo-2-boost-form button i {
  margin-right: 5px;
}
.seo-2-boost-form button:hover {
  border: 2px solid #fff;
  background-color: transparent;
}

/*---------------------------------------------------- */
/*why-choose area*/
/*----------------------------------------------------*/
.seo-2-why-choose-section {
  padding: 120px 0px;
}
.seo-2-why-choose-section .seo-2-why-choose-img {
  animation: man-updown 2s infinite alternate;
}

.seo-2-why-choose-text {
  padding: 20px 0px 0px 20px;
}
.seo-2-why-choose-text .seo-2-section-title p {
  padding-top: 20px;
}

.seo-2-why-choose-counter {
  margin-top: 45px;
}
.seo-2-why-choose-counter .counter-boxed {
  margin-right: 60px;
  display: inline-block;
}
.seo-2-why-choose-counter .counter-boxed:nth-child(2) {
  margin-right: 0;
}
.seo-2-why-choose-counter .counter-boxed .graph-outer {
  margin-bottom: 25px;
}
.seo-2-why-choose-counter .counter-boxed h3 {
  color: #003a6c;
  font-size: 24px;
  font-weight: 700;
  padding-bottom: 15px;
}
.seo-2-why-choose-counter .counter-boxed span {
  color: #03a0ec;
  font-weight: 700;
}
.seo-2-why-choose-counter .counter-boxed .count-text {
  font-size: 40px;
  font-weight: 700;
  font-family: "Poppins";
  background: linear-gradient(90deg, #0160e7 0%, #04bdef 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.seo-2-why-choose-counter .counter-boxed .count-box {
  left: 30px;
  top: 30px;
  font-weight: 700;
  position: absolute;
}

/*---------------------------------------------------- */
/*Blog area*/
/*----------------------------------------------------*/
.seo-2-blog-section {
  z-index: 1;
  padding: 115px 0px;
}
.seo-2-blog-section:before {
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  content: "";
  height: 485px;
  position: absolute;
  background-color: #f5fbff;
}
.seo-2-blog-section .seo-2-blog-content {
  padding-top: 40px;
}

.seo-2-blog-img-text .seo-2-blog-img {
  overflow: hidden;
}
.seo-2-blog-img-text .seo-2-blog-img img {
  transition: 0.5s all ease-in-out;
}
.seo-2-blog-img-text .seo-2-blog-text {
  padding: 40px;
  background-color: #fff;
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.04);
}
.seo-2-blog-img-text .seo-2-blog-text .seo-2-date-meta {
  top: -40px;
  right: 40px;
  width: 80px;
  height: 80px;
  color: #7990a4;
  font-size: 14px;
  font-weight: 700;
  padding-top: 18px;
  position: absolute;
  background-color: #fff;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.04);
}
.seo-2-blog-img-text .seo-2-blog-text .seo-2-date-meta span {
  color: #000a2a;
  line-height: 1;
  display: block;
  font-size: 30px;
  font-family: "Poppins";
}
.seo-2-blog-img-text .seo-2-blog-text .seo-2-blog-cat {
  font-size: 14px;
  font-weight: 700;
  background: linear-gradient(90deg, #0160e7 0%, #04bdef 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.seo-2-blog-img-text .seo-2-blog-text h3 {
  color: #003a6c;
  font-size: 22px;
  font-weight: 700;
  line-height: 1.455;
  padding: 5px 0px 10px;
}
.seo-2-blog-img-text .seo-2-blog-text .seo-2-post-author {
  margin-top: 15px;
}
.seo-2-blog-img-text .seo-2-blog-text .seo-2-post-author .seo-2-author-img {
  width: 40px;
  height: 40px;
  overflow: hidden;
  margin-right: 14px;
  border-radius: 100%;
}
.seo-2-blog-img-text .seo-2-blog-text .seo-2-post-author h4 {
  color: #003a6c;
  font-size: 14px;
  font-weight: 700;
  padding-top: 12px;
}
.seo-2-blog-img-text:hover .seo-2-blog-img img {
  transform: scale(1.2);
}

/*---------------------------------------------------- */
/*footer area*/
/*----------------------------------------------------*/
.seo-2-footer-content-wrap {
  z-index: 1;
  margin: 0 auto;
  max-width: 1310px;
  padding-bottom: 50px;
  background-color: #fff;
}

.seo-2-footer-widget-wrapper:after {
  left: 0;
  bottom: 0;
  z-index: -1;
  content: "";
  width: 100%;
  height: 40px;
  position: absolute;
  background: linear-gradient(45deg, #0160e7 0%, #04bdef 100%);
}

.seo-2-footer-widget {
  display: inline-block;
}
.seo-2-footer-widget .widget-title {
  color: #003378;
  font-size: 18px;
  font-weight: 700;
  padding-bottom: 35px;
}
.seo-2-footer-widget .footer-menu-widget {
  padding-right: 80px;
}
.seo-2-footer-widget .footer-menu-widget li {
  margin-bottom: 15px;
}
.seo-2-footer-widget .footer-menu-widget li a {
  transition: 0.3s all ease-in-out;
}
.seo-2-footer-widget .footer-menu-widget li a:hover {
  color: #04bdef;
}
.seo-2-footer-widget .footer-menu-widget .footer-store a {
  display: block;
  margin-bottom: 5px;
}
.seo-2-footer-widget .seo-2-footer-blog-img-text {
  width: 100%;
  display: inline-block;
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #edf0f8;
}
.seo-2-footer-widget .seo-2-footer-blog-img-text:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}
.seo-2-footer-widget .seo-2-footer-blog-img-text .seo-2-footer-blog-img {
  height: 80px;
  width: 100px;
  overflow: hidden;
  margin-right: 20px;
}
.seo-2-footer-widget .seo-2-footer-blog-img-text .seo-2-footer-blog-text .seo-2-footer-date-meta {
  font-size: 14px;
}
.seo-2-footer-widget .seo-2-footer-blog-img-text .seo-2-footer-blog-text h3 {
  color: #003a6c;
  font-size: 16px;
  font-weight: 700;
  line-height: 1.625;
}

.seo-2-footer-copyright {
  padding: 30px 0px 25px;
  background: linear-gradient(45deg, #0160e7 0%, #04bdef 100%);
}
.seo-2-footer-copyright .seo-2-footer-copyright-menu li {
  margin-right: 70px;
}
.seo-2-footer-copyright .seo-2-footer-copyright-menu li a {
  color: #fff;
  font-weight: 700;
}
.seo-2-footer-copyright .seo-2-footer-copyright-text span {
  color: #fff;
  font-size: 14px;
}
.seo-2-footer-copyright .seo-2-footer-copyright-text span a {
  font-weight: 700;
}

/*---------------------------------------------------- */
/*responsive area*/
/*----------------------------------------------------*/
@media screen and (max-width: 1199px) {
  .seo-2-portfolio-content .owl-nav {
    left: -485px;
  }

  .seo-2-boost-form .seo-2-boost-input input {
    width: 315px;
  }

  .seo-2-boost-form button {
    width: 250px;
  }

  .seo-2-banner-section .seo-2-banner-shape {
    display: none;
  }

  .seo-2-portfolio-content .owl-nav {
    left: -400px;
  }
}
@media screen and (max-width: 1024px) {
  .seo-2-main-menu-item .seo-2-main-navigation li {
    margin: 0px 15px;
  }

  .seo-2-about-section .seo-2-about-img {
    left: -350px;
  }

  .seo-2-portfolio-content .owl-nav {
    bottom: 60px;
  }

  .seo-2-why-choose-counter .counter-boxed {
    margin-right: 15px;
  }

  .seo-2-why-choose-counter .counter-boxed h3 {
    font-size: 20px;
  }

  .seo-2-footer-widget .footer-menu-widget {
    padding-right: 50px;
  }
}
@media screen and (max-width: 991px) {
  .seo-2-main-menu-item .seo-2-main-navigation {
    display: none;
  }

  .seo-2-main-menu-item .seo-2-header-btn {
    height: 50px;
    width: 130px;
    line-height: 48px;
    margin-left: 25px;
    margin-right: 45px;
  }

  .seo-2-main-header {
    padding-top: 10px;
  }

  .seo-2-main-header .seo-2-mobile_menu_button {
    display: block;
  }

  .seo-2-main-header .seo-2-sidebar-toggle {
    display: none;
  }

  .seo-2-main-header .seo-2-brand-logo {
    padding-left: 0;
  }

  .seo-2-banner-content {
    padding: 175px 0px 290px;
  }

  .seo-2-service-innerbox {
    margin-bottom: 40px;
  }

  .seo-2-portfolio-content {
    margin-right: 0;
    margin-top: 40px;
  }

  .seo-2-portfolio-content .owl-nav {
    position: static;
    margin-top: 30px;
    text-align: center;
  }

  .seo-2-portfolio-section {
    padding-bottom: 60px;
  }

  .seo-2-portfolio-section:after {
    display: none;
  }

  .seo-2-why-choose-img {
    text-align: center;
  }

  .seo-2-why-choose-text {
    margin: 0 auto;
    max-width: 570px;
  }

  .seo-2-blog-img-text {
    margin: 0 auto;
    max-width: 370px;
    margin-bottom: 30px;
  }

  .seo-2-footer-widget {
    margin-top: 30px;
  }

  .seo-2-about-text-wrap {
    float: none;
    margin: 0 auto;
    margin-top: 40px;
  }

  .seo-2-about-section .seo-2-about-img {
    position: static;
  }

  .seo-2-cta-content p {
    font-size: 16px;
  }

  .seo-2-boost-form .seo-2-boost-input {
    margin-bottom: 10px;
  }

  .seo-2-footer-copyright .seo-2-footer-copyright-menu li {
    margin-right: 40px;
  }
}
@media screen and (max-width: 850px) {
  .seo-2-banner-content .seo-2-banner-text h1 {
    font-size: 60px;
  }

  .seo-2-banner-content .banner-input-field {
    flex-wrap: wrap;
  }

  .seo-2-banner-content .banner-input-field .seo-2-form-input {
    margin-bottom: 20px;
  }
}
@media screen and (max-width: 767px) {
  .seo-2-banner-section .seo-2-banner-vector {
    position: static;
  }

  .seo-2-banner-content {
    padding: 175px 0px 60px;
  }

  .seo-2-banner-section {
    margin-bottom: 0;
  }
}
@media screen and (max-width: 680px) {
  .seo-2-banner-content .seo-2-banner-text h1 {
    font-size: 50px;
  }

  .seo-2-banner-content .banner-input-field .seo-2-form-input input {
    height: 50px;
    line-height: 50px;
  }

  .seo-2-banner-content .banner-input-field .seo-2-form-input span {
    top: 13px;
  }

  .seo-2-banner-content .banner-input-field button {
    width: 150px;
    height: 50px;
  }

  .seo-2-cta-content .seo-2-cta-btn {
    margin: 0 auto;
    position: static;
    margin-top: 10px;
  }

  .seo-2-boost-form .seo-2-boost-input input {
    height: 50px;
  }

  .seo-2-boost-form .seo-2-boost-input span {
    top: 13px;
  }

  .seo-2-boost-form button {
    height: 50px;
  }

  .seo-2-footer-copyright-text,
.seo-2-footer-copyright-menu {
    float: none !important;
  }
}
@media screen and (max-width: 480px) {
  .seo-2-banner-content .seo-2-banner-text h1 {
    font-size: 32px;
  }

  .seo-2-service-section {
    padding: 60px 0px 20px;
  }

  .seo-2-section-title h2 {
    font-size: 28px;
  }

  .seo-2-about-text-wrap {
    padding-left: 0;
  }

  .seo-2-about-list-text {
    overflow: hidden;
  }

  .seo-2-about-section {
    padding: 25px 0px 50px;
  }

  .seo-2-boost-form .seo-2-boost-input {
    margin-right: 0;
  }

  .seo-2-why-choose-counter .counter-boxed {
    display: block;
    text-align: center;
    margin-bottom: 30px;
  }

  .seo-2-why-choose-counter .counter-boxed .count-box {
    left: 0;
    right: 0;
  }

  .seo-2-why-choose-section {
    padding: 50px 0px;
  }

  .seo-2-blog-section {
    padding: 50px 0px;
  }

  .seo-2-footer-copyright .seo-2-footer-copyright-menu li {
    margin-right: 10px;
  }
}
@media screen and (max-width: 380px) {
  .seo-2-banner-content .seo-2-banner-text h1 {
    font-size: 30px;
  }

  .seo-2-service-innerbox .seo-2-service-text h3 {
    font-size: 20px;
  }

  .seo-2-blog-img-text .seo-2-blog-text {
    padding: 40px 20px;
  }

  .seo-2-why-choose-text {
    padding-left: 0;
  }
}
@media screen and (max-width: 320px) {
  .seo-2-main-menu-item .seo-2-header-btn {
    display: none;
  }

  .seo-2-service-innerbox {
    padding: 50px 20px;
  }
}
/*---------------------------------------------------- */