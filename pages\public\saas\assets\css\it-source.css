@charset "UTF-8";
/*----------------------------------------------------
@File: Default Styles
@Author:  themexriver
@URL: https://themexriver.com/

This file contains the styling for the actual theme, this
is the file you need to edit to change the look of the
theme.
---------------------------------------------------- */
/*=====================================================================
@Author: themexriver

CSS Table of content:-

1. Global Area 
2. Header Section
=====================================================================*/
/*=========
Font load
===========*/
@import url("https://fonts.googleapis.com/css2?family=Caveat:wght@400;700&family=Montserrat:wght@100;400;500;600;700&family=Nunito:wght@200;300;400;600;700&family=Playfair+Display:wght@400;500;600;700&family=Roboto:wght@100;300;400;500;700&display=swap");
/*=========
Color Code
===========*/
/*(1)- global area*/
/*----------------------------------------------------*/
.s-it {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-size: 16px;
  line-height: 1.625;
  color: #535353;
  font-family: "Roboto";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

.s-it::selection {
  color: #ffffff;
  background-color: #ff5b2e;
}

.s-it::-moz-selection {
  color: #ffffff;
  background-color: #ff5b2e;
}

.container {
  max-width: 1200px;
}

.ul-li ul {
  margin: 0;
  padding: 0;
}
.ul-li ul li {
  list-style: none;
  display: inline-block;
}

.ul-li-block ul {
  margin: 0;
  padding: 0;
}
.ul-li-block ul li {
  display: block;
  list-style: none;
}

div#preloader {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999;
  width: 100%;
  height: 100%;
  overflow: visible;
  background: #fff url("../img/pre.svg") no-repeat center center;
}

[data-background] {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

.s-it a {
  color: inherit;
  text-decoration: none;
  transition: 0.3s all ease-in-out;
}
.s-it a:hover, .s-it a:focus {
  text-decoration: none;
}

.s-it img {
  max-width: 100%;
  height: auto;
}

section {
  overflow: hidden;
}

button {
  cursor: pointer;
}

.form-control:focus,
button:visited,
button.active,
button:hover,
button:focus,
input:visited,
input.active,
input:hover,
input:focus,
textarea:hover,
textarea:focus,
a:hover,
a:focus,
a:visited,
a.active,
select,
select:hover,
select:focus,
select:visited {
  outline: none;
  box-shadow: none;
  text-decoration: none;
  color: inherit;
}

.form-control {
  box-shadow: none;
}

.relative-position {
  position: relative;
}

.pera-content p {
  margin-bottom: 0;
}

.no-paading {
  padding: 0;
}

.s-it .headline-1 h1,
.s-it .headline-1 h2,
.s-it .headline-1 h3,
.s-it .headline-1 h4,
.s-it .headline-1 h5,
.s-it .headline-1 h6 {
  margin: 0;
  font-family: "Nunito";
}

.s-it .headline-2 h1,
.s-it .headline-2 h2,
.s-it .headline-2 h3,
.s-it .headline-2 h4,
.s-it .headline-2 h5,
.s-it .headline-2 h6 {
  margin: 0;
  font-family: "Montserrat";
}

.s-it .headline-3 h1,
.s-it .headline-3 h2,
.s-it .headline-3 h3,
.s-it .headline-3 h4,
.s-it .headline-3 h5,
.s-it .headline-3 h6 {
  margin: 0;
  font-family: "Playfair Display";
}

.block-display {
  width: 100%;
  display: block;
}

.background_overlay {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
}

#cursor .it-cursor {
  position: fixed;
  top: inherit;
  left: inherit;
  z-index: 99;
  pointer-events: none;
}

#cursor .cursor_outer {
  width: 30px;
  height: 30px;
  border: 1px solid #5b1d67;
  border-radius: 30px;
  margin-top: -17px;
  margin-left: -16px;
  transition: all 50ms linear 0ms;
}

#cursor .cursor_inner {
  width: 6px;
  height: 6px;
  background-color: #92d3d7;
  border-radius: 6px;
  margin-top: -5px;
  margin-left: -4px;
}

.s-it .scrollup {
  width: 55px;
  right: 20px;
  z-index: 5;
  height: 55px;
  bottom: 20px;
  display: none;
  position: fixed;
  border-radius: 100%;
  line-height: 55px;
  background-color: #ff5b2e;
}
.s-it .scrollup i {
  color: #fff;
  font-size: 20px;
}

.it-up-section-title span,
.it-up-section-title-2 span {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 18px;
  display: inline-block;
  background: linear-gradient(90deg, #ff780b 0%, #ee132f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.it-up-section-title h2,
.it-up-section-title-2 h2 {
  color: #000000;
  font-size: 45px;
  font-weight: 700;
}
.it-up-section-title p,
.it-up-section-title-2 p {
  font-size: 18px;
}

.it-up-section-title-2 span {
  background: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@keyframes IT_animation_1 {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-40px);
  }
  50% {
    transform: translateX(0);
  }
  75% {
    transform: translateX(40px);
  }
  100% {
    transform: translateX(0);
  }
}
@keyframes IT_animation_2 {
  0% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-40px);
  }
  50% {
    transform: translateY(0);
  }
  75% {
    transform: translateY(40px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes IT_animation_3 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes IT_animation_4 {
  0% {
    transform: translate(-300px, 151px) rotate(0);
  }
  100% {
    transform: translate(251px, -200px) rotate(180deg);
  }
}
@keyframes IT_animation_5 {
  0% {
    transform: translate(0, 0) rotate(0);
  }
  20% {
    transform: translate(73px, -1px) rotate(36deg);
  }
  40% {
    transform: translate(111px, 72px) rotate(72deg);
  }
  60% {
    transform: translate(93px, 122px) rotate(108deg);
  }
  80% {
    transform: translate(-70px, 72px) rotate(124deg);
  }
  100% {
    transform: translate(0, 0) rotate(0);
  }
}
.it-up-footer-widget .it-up-footer-logo-widget .footer-logo-btn, .it-up-form-wrap button, .it-up-service-tab-text .it-up-ser-btn, .it-up-ft-btn a, .it-up-about-btn a {
  height: 55px;
  width: 160px;
  color: #fff;
  font-weight: 700;
  line-height: 55px;
  text-align: center;
  border-radius: 5px;
  display: inline-block;
  transition: all 200ms linear 0ms;
  background-size: 200%, 1px;
  box-shadow: 0px 18px 18px 0px rgba(22, 136, 232, 0.21);
  background-image: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 50%, #1ec5fa);
}
.it-up-footer-widget .it-up-footer-logo-widget .footer-logo-btn:hover, .it-up-form-wrap button:hover, .it-up-service-tab-text .it-up-ser-btn:hover, .it-up-ft-btn a:hover, .it-up-about-btn a:hover {
  background-position: 120%;
}

/*---------------------------------------------------- */
/*(1)- Header area*/
/*----------------------------------------------------*/
.it-header-up-seaction {
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  position: absolute;
}
.it-header-up-top {
  padding: 5px 0px;
  background-color: #00052b;
}
.it-header-up-top .it-header-top-cta a {
  color: #fff;
  font-size: 14px;
  margin-right: 30px;
}
.it-header-up-top .it-header-top-cta a i {
  color: #ef172e;
  margin-right: 5px;
}
.it-header-up-top .it-header-top-social a {
  color: #fff;
  font-size: 14px;
  margin-left: 15px;
}

.it-up-header-main {
  padding: 30px 0px;
}

.it-up-main-navigation {
  padding-top: 15px;
}
.it-up-main-navigation .dropdown {
  position: relative;
}
.it-up-main-navigation .dropdown:hover .dropdown-menu {
  transform: scaleY(1);
}
.it-up-main-navigation .dropdown-menu {
  left: 0;
  top: 55px;
  z-index: 100;
  margin: 0px;
  padding: 0px;
  height: auto;
  min-width: 250px;
  display: block;
  border: none;
  border-radius: 0 !important;
  position: absolute;
  transform: scaleY(0);
  background-color: #fff;
  background-clip: inherit;
  border-radius: 6px;
  transition: all 0.4s ease-in-out;
  transform-origin: center top 0;
  box-shadow: 0 8px 83px rgba(40, 40, 40, 0.08);
}
.it-up-main-navigation .dropdown-menu li {
  display: block;
  margin: 0 !important;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.it-up-main-navigation .dropdown-menu li:last-child {
  border-bottom: none;
}
.it-up-main-navigation .dropdown-menu li:hover .dropdown-menu {
  top: 0;
  opacity: 1;
}
.it-up-main-navigation .dropdown-menu li:hover {
  background-color: #fd5d0a;
}
.it-up-main-navigation .dropdown-menu a {
  width: 100%;
  display: block;
  font-weight: 700;
  padding: 10px 20px 10px !important;
  font-size: 15px !important;
}
.it-up-main-navigation .dropdown-menu a:after {
  display: none;
}
.it-up-main-navigation .dropdown-menu a:hover {
  color: #fff !important;
}
.it-up-main-navigation .navbar-nav {
  display: inherit;
}
.it-up-main-navigation li {
  margin-left: 70px;
}
.it-up-main-navigation li a {
  color: #000000;
  font-weight: 700;
  display: inline;
  padding-bottom: 30px;
}
.it-up-main-navigation li a.active {
  background: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.it-up-header-cta-btn a {
  height: 55px;
  width: 160px;
  color: #fff;
  font-weight: 700;
  line-height: 55px;
  border-radius: 5px;
  display: inline-block;
  background-size: 200%, 1px;
  background-image: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 50%, #1ec5fa);
}
.it-up-header-cta-btn a:hover {
  background-position: 120%;
}

.it-header-up-sticky {
  top: 0px;
  width: 100%;
  z-index: 10;
  position: fixed;
  background: #fff;
  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 0px 18px 1px rgba(0, 0, 0, 0.1);
}
.it-header-up-sticky .it-up-header-main {
  padding: 18px 0px;
}
.it-header-up-sticky .it-header-up-top {
  display: none;
}
.mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  width: 280px;
  position: fixed;
  overflow-y: scroll;
  background-color: #020c16;
  padding: 100px 20px 50px 20px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
}
.mobile_menu_content .main-navigation {
  width: 100%;
  margin-right: 0 !important;
}
.mobile_menu_content .main-navigation li {
  margin-left: 0 !important;
}
.mobile_menu_content .main-navigation .navbar-nav {
  width: 100%;
}
.mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
  background-color: transparent;
}
.mobile_menu_content .main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  margin-left: 0;
  padding-left: 0;
  margin: 5px 0px;
  transition: 0.3s all ease-in-out;
}
.mobile_menu_content .main-navigation .navbar-nav li a {
  color: #c5c5c5;
  font-size: 15px;
  font-weight: 700;
  text-transform: uppercase;
}
.mobile_menu_content .m-brand-logo {
  margin-bottom: 30px;
}
.mobile_menu_content .dropdown-btn {
  right: 0;
  top: 0px;
  width: 30px;
  color: #c5c5c5;
  height: 30px;
  line-height: 30px;
  text-align: center;
  position: absolute;
  background-color: #061c31;
  transition: 0.3s all ease-in-out;
}

.it-up-mobile_menu_wrap.it-up-mobile_menu_on .mobile_menu_content {
  right: 0px;
  transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.4s;
}

.mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: -100%;
  height: 120vh;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.8s ease-in 0.8s;
}

.it-up-mobile_menu_overlay_on {
  overflow: hidden;
}

.it-up-mobile_menu_wrap.it-up-mobile_menu_on .mobile_menu_overlay {
  right: 0;
  transition: all 0.8s ease-out 0s;
}

.mobile_menu_button {
  position: absolute;
  display: none;
  right: 0px;
  top: -42px;
  cursor: pointer;
  color: #fd5d0a;
  text-align: center;
  font-size: 25px;
}

.mobile_menu .main-navigation .navbar-nav li a:after {
  display: none;
}
.mobile_menu .main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.mobile_menu .mobile_menu_content .main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
}
.mobile_menu .mobile_menu_content .main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  line-height: 1;
  padding: 5px 20px;
}
.mobile_menu .mobile_menu_content .main-navigation .navbar-nav .dropdown-menu li a {
  color: #c5c5c5;
  font-size: 14px;
}
.mobile_menu .mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

/*---------------------------------------------------- */
/*(2)- Banner area*/
/*----------------------------------------------------*/
.it-up-banner-section .it-up-banner-deco1 {
  left: 0;
  bottom: 0;
}
.it-up-banner-section .it-up-banner-deco2 {
  left: 175px;
  bottom: 210px;
  animation: IT_animation_3 12s infinite linear alternate;
}
.it-up-banner-section .it-up-banner-deco3 {
  left: 15%;
  top: 150px;
  animation: IT_animation_2 12s infinite linear alternate;
}
.it-up-banner-section .it-up-banner-deco4 {
  top: 45%;
  left: 51%;
  z-index: 1;
  animation: IT_animation_1 12s infinite linear alternate;
}

.it-up-banner-text {
  max-width: 615px;
  padding: 305px 0px 180px;
}
.it-up-banner-text span {
  color: #0e50d7;
  font-size: 24px;
  font-weight: 700;
  position: relative;
  margin-bottom: 12px;
  display: inline-block;
}
.it-up-banner-text span:before, .it-up-banner-text span:after {
  left: -11px;
  bottom: 8px;
  content: "";
  height: 3px;
  width: 10px;
  position: absolute;
  background-color: #0e50d7;
}
.it-up-banner-text span:after {
  left: auto;
  right: -11px;
}
.it-up-banner-text h1 {
  color: #000000;
  font-size: 85px;
  font-weight: 700;
  line-height: 0.941;
  padding-bottom: 45px;
}
.it-up-banner-text p {
  font-size: 20px;
  padding-bottom: 30px;
}
.it-up-banner-text .it-up-banner-btn .it-up-banner-play-btn {
  width: 60px;
  height: 60px;
  line-height: 60px;
  margin-right: 25px;
  border-radius: 100%;
  background-image: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 100%);
}
.it-up-banner-text .it-up-banner-btn .it-up-banner-play-btn a {
  color: #fff;
  width: 100%;
  display: block;
}
.it-up-banner-text .it-up-banner-btn .it-up-banner-cta-btn {
  height: 55px;
  width: 150px;
  line-height: 55px;
  border-radius: 5px;
  background-size: 200%, 1px;
  transition: all 200ms linear 0ms;
  box-shadow: 0px 21px 29px 0px rgba(248, 79, 26, 0.27);
  background-image: linear-gradient(90deg, #ff780b 0%, #ee132f 50%, #ff780b);
}
.it-up-banner-text .it-up-banner-btn .it-up-banner-cta-btn a {
  color: #fff;
  width: 100%;
  display: block;
  font-size: 15px;
  font-weight: 700;
}
.it-up-banner-text .it-up-banner-btn .it-up-banner-cta-btn:hover {
  background-position: 120%;
}

.it-up-banner-img {
  right: 0;
  top: 150px;
  position: absolute;
}
.it-up-banner-img .it-up-img-deco1 {
  top: 0;
  right: 20px;
  border-radius: 10px;
  box-shadow: 0px 10px 21px 0px rgba(7, 47, 141, 0.09);
}
.it-up-banner-img .it-up-img-deco3 {
  right: -35px;
  bottom: 250px;
  border-radius: 10px;
  box-shadow: 0px 10px 21px 0px rgba(7, 47, 141, 0.09);
}
.it-up-banner-img .it-up-img-deco2 {
  left: -50px;
  bottom: 140px;
  border-radius: 10px;
  box-shadow: 0px 10px 21px 0px rgba(7, 47, 141, 0.09);
}

/*---------------------------------------------------- */
/*(3)- sponsor area*/
/*----------------------------------------------------*/
.it-up-sponsor-slider {
  padding: 25px 80px;
  border-radius: 20px;
  box-shadow: 0px 9px 21px 0px rgba(0, 13, 42, 0.08);
}

.it-up-sponsor-img img {
  cursor: pointer;
  filter: grayscale(1);
  transition: 0.4s all ease-in-out;
}
.it-up-sponsor-img img:hover {
  filter: grayscale(0);
}
.it-up-sponsor-slider .owl-nav,
.it-up-testimonial-slider-wrap .owl-nav,
.it-up-blog-slide .owl-nav {
  display: none;
}
/*---------------------------------------------------- */
/*(4)- about area*/
/*----------------------------------------------------*/
.it-up-about-section {
  padding: 100px 0px;
}

.it-up-about-img {
  padding-top: 80px;
}

.it-up-about-img-wrap img {
  border-radius: 15px;
}

.it-up-about-circle-progress {
  top: 0;
  left: 0;
  width: 205px;
  height: 215px;
  padding: 20px;
  border-radius: 20px;
  background-color: #fff;
  box-shadow: 0px 9px 21px 0px rgba(0, 13, 42, 0.08);
}
.it-up-about-circle-progress .circle-progress-icon {
  left: 0;
  right: 0;
  top: 50px;
  position: absolute;
}
.it-up-about-circle-progress .progress_area strong {
  left: 0;
  top: 55px;
  right: 0;
  color: #000000;
  font-size: 36px;
  font-weight: 600;
  position: absolute;
}
.it-up-about-circle-progress p {
  color: #1ec1f9;
  font-size: 18px;
  font-weight: 700;
}

.it-up-about-text {
  padding-left: 40px;
}
.it-up-about-text .it-up-section-title h2 {
  max-width: 550px;
  padding-bottom: 30px;
}
.it-up-about-text .it-up-section-title p {
  max-width: 550px;
}

.it-up-about-feature {
  padding: 30px 0px 40px;
}

.it-up-about-ft-item {
  width: 50%;
  float: left;
}
.it-up-about-ft-item .it-up-about-ft-icon {
  width: 60px;
  height: 60px;
  line-height: 70px;
  margin-right: 25px;
  border-radius: 100%;
  transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  box-shadow: 0px 25px 24px 0px rgba(4, 21, 59, 0.1);
}
.it-up-about-ft-item .it-up-about-ft-text {
  overflow: hidden;
}
.it-up-about-ft-item .it-up-about-ft-text h3 {
  color: #000000;
  font-size: 20px;
  font-weight: 700;
  padding-bottom: 8px;
}
.it-up-about-ft-item .it-up-about-ft-text p {
  line-height: 1.733;
}
.it-up-about-ft-item:hover .it-up-about-ft-icon {
  transform: rotateY(360deg);
}

.it-up-about-btn span {
  color: #000000;
  font-size: 18px;
  font-weight: 700;
  font-family: "Nunito";
  margin-right: 20px;
}
/*---------------------------------------------------- */
/*(5)- Featured area*/
/*----------------------------------------------------*/
.it-up-featured-section {
  padding: 100px 0px 80px;
  background-color: #f7fafb;
}
.it-up-featured-section .it-up-ft-bg {
  top: 0;
  left: -50px;
}
.it-up-featured-section .it-up-section-title-2 {
  margin: 0 auto;
  max-width: 500px;
}
.it-up-featured-section .it-up-ft-shape {
  top: 60%;
  left: 10%;
  animation: IT_animation_1 12s infinite linear alternate;
}
.it-up-featured-section .it-up-ft-shape2 {
  bottom: 50px;
  right: 20%;
  animation: IT_animation_1 12s infinite linear alternate;
}

.it-up-featured-content {
  padding-top: 100px;
}
.it-up-featured-content .col-lg-4:nth-child(1) .it-up-featured-innerbox .it-up-featured-text .it-up-ft-more {
  color: #f0202a;
}
.it-up-featured-content .col-lg-4:nth-child(1) .it-up-featured-innerbox .it-up-featured-icon:before, .it-up-featured-content .col-lg-4:nth-child(1) .it-up-featured-innerbox .it-up-featured-icon:after {
  background: linear-gradient(90deg, #ff750c 0%, #ef192d 100%);
}
.it-up-featured-content .col-lg-4:nth-child(2) .it-up-featured-innerbox {
  transform: translateY(-40px);
}
.it-up-featured-content .col-lg-4:nth-child(2) .it-up-featured-text .it-up-ft-more {
  color: #70df11;
}
.it-up-featured-content .col-lg-4:nth-child(3) .it-up-featured-innerbox .it-up-featured-text .it-up-ft-more {
  color: #105adb;
}
.it-up-featured-content .col-lg-4:nth-child(3) .it-up-featured-innerbox .it-up-featured-icon:before, .it-up-featured-content .col-lg-4:nth-child(3) .it-up-featured-innerbox .it-up-featured-icon:after {
  background: linear-gradient(90deg, #1cb6f6 0%, #105bdb 100%);
}
.it-up-featured-content .col-lg-4:nth-child(4) .it-up-featured-innerbox .it-up-featured-text .it-up-ft-more {
  color: #6a63e6;
}
.it-up-featured-content .col-lg-4:nth-child(4) .it-up-featured-innerbox .it-up-featured-icon:before, .it-up-featured-content .col-lg-4:nth-child(4) .it-up-featured-innerbox .it-up-featured-icon:after {
  background: linear-gradient(90deg, #1ec2f9 0%, #0f53d9 100%);
}
.it-up-featured-content .col-lg-4:nth-child(5) .it-up-featured-innerbox {
  transform: translateY(-40px);
}
.it-up-featured-content .col-lg-4:nth-child(5) .it-up-featured-innerbox .it-up-featured-text .it-up-ft-more {
  color: #fd60a9;
}
.it-up-featured-content .col-lg-4:nth-child(5) .it-up-featured-innerbox .it-up-featured-icon:before, .it-up-featured-content .col-lg-4:nth-child(5) .it-up-featured-innerbox .it-up-featured-icon:after {
  background: linear-gradient(90deg, #feb0d6 0%, #fd60a9 100%);
}
.it-up-featured-content .col-lg-4:nth-child(6) .it-up-featured-innerbox .it-up-featured-text .it-up-ft-more {
  color: #1bc29f;
}
.it-up-featured-content .col-lg-4:nth-child(6) .it-up-featured-innerbox .it-up-featured-icon:before, .it-up-featured-content .col-lg-4:nth-child(6) .it-up-featured-innerbox .it-up-featured-icon:after {
  background: linear-gradient(90deg, #8ff9ab 0%, #1bc29f 100%);
}

.it-up-featured-innerbox {
  margin-bottom: 30px;
  border-radius: 10px;
  background-color: #fff;
  padding: 35px 35px 50px 35px;
  box-shadow: 0px 0px 38px 0px rgba(1, 44, 118, 0.08);
}
.it-up-featured-innerbox .it-up-featured-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto;
  line-height: 90px;
  margin-bottom: 35px;
  position: relative;
  border-radius: 100%;
  transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  box-shadow: 0px 15px 24px 0px rgba(4, 21, 59, 0.1);
}
.it-up-featured-innerbox .it-up-featured-icon:before, .it-up-featured-innerbox .it-up-featured-icon:after {
  top: 4px;
  width: 7px;
  right: 20px;
  height: 7px;
  content: "";
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  transition: 0.3s all ease-in-out;
  background: linear-gradient(90deg, #30c677 0%, #70df11 100%);
}
.it-up-featured-innerbox .it-up-featured-icon:after {
  top: auto;
  bottom: 0;
  right: 40px;
}
.it-up-featured-innerbox .it-up-featured-text h3 {
  color: #000;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 12px;
}
.it-up-featured-innerbox .it-up-featured-text p {
  line-height: 1.625;
  padding-bottom: 20px;
}
.it-up-featured-innerbox .it-up-featured-text .it-up-ft-more {
  font-size: 15px;
  font-weight: 700;
}
.it-up-featured-innerbox .it-up-featured-text .it-up-ft-more i {
  transition: 0.4s all ease-in-out;
}
.it-up-featured-innerbox .it-up-featured-text .it-up-ft-more:hover i {
  margin-left: 10px;
}
.it-up-featured-innerbox:hover .it-up-featured-icon:before, .it-up-featured-innerbox:hover .it-up-featured-icon:after {
  transform: scale(1);
}
.it-up-featured-innerbox:hover .it-up-featured-icon {
  transform: rotateY(360deg);
}

.it-up-ft-btn {
  margin-top: 30px;
}
/*---------------------------------------------------- */
/*(6)- Service area*/
/*----------------------------------------------------*/
.it-up-service-shape {
  z-index: -1;
}
.it-up-service-shape.deco1 {
  top: 30%;
  left: 10%;
  animation: IT_animation_4 20s infinite linear alternate;
}
.it-up-service-shape.deco2 {
  left: 25%;
  top: 185px;
  animation: IT_animation_5 12s infinite linear alternate;
}
.it-up-service-shape.deco3 {
  top: 135px;
  right: 10%;
  animation: IT_animation_4 12s infinite linear alternate;
}
.it-up-service-shape.deco4 {
  top: 50%;
  right: 12%;
  animation: IT_animation_5 18s infinite linear alternate;
}
.it-up-service-shape.deco5 {
  bottom: 20%;
  right: 8%;
  animation: IT_animation_4 15s infinite linear alternate;
}

.it-up-service-section {
  z-index: 1;
  padding: 100px 0px;
}
.it-up-service-section .it-up-section-title {
  margin: 0 auto;
  max-width: 625px;
}

.it-up-service-content {
  padding-top: 60px;
}

.it-up-service-tab-btn {
  background-color: #fff;
  padding: 85px 20px 40px 55px;
  box-shadow: 0px 0px 32px 0px rgba(80, 80, 80, 0.1);
}
.it-up-service-tab-btn .nav-tabs:before {
  top: -3px;
  left: -30px;
  width: 6px;
  height: 80%;
  content: "";
  position: absolute;
  background-color: #f8fcfc;
}
.it-up-service-tab-btn .nav-tabs .nav-item.show .nav-link,
.it-up-service-tab-btn .nav-tabs .nav-link.active,
.it-up-service-tab-btn .nav-tabs .nav-link,
.it-up-service-tab-btn .nav-tabs {
  padding: 0;
  border: none;
  position: relative;
}
.it-up-service-tab-btn .nav-tabs .nav-item {
  margin-bottom: inherit;
  padding-bottom: 65px;
}
.it-up-service-tab-btn .nav {
  display: inherit;
}
.it-up-service-tab-btn .nav-tabs .nav-link {
  color: #bebebe;
  font-size: 20px;
  font-weight: 700;
  font-family: "Nunito";
}
.it-up-service-tab-btn .nav-tabs .nav-link:before {
  left: 0;
  bottom: 0;
  top: 1px;
  width: 6px;
  content: "";
  height: 0px;
  left: -30px;
  border-radius: 10px;
  position: absolute;
  background: linear-gradient(90deg, #ff780b 0%, #ee132f 100%);
  transition: 0.4s all ease-in-out;
}
.it-up-service-tab-btn .nav-tabs .nav-link.active {
  color: #000000;
}
.it-up-service-tab-btn .nav-tabs .nav-link.active:before {
  height: 40px;
  background-color: #fff;
}

.it-up-service-tab-wrap {
  padding: 30px 50px;
  border-radius: 10px;
  background-color: #f8f8f8;
}

.it-up-service-tab-text {
  max-width: 480px;
  padding-top: 20px;
}
.it-up-service-tab-text .it-up-tab-icon {
  right: 0;
  bottom: 0;
}
.it-up-service-tab-text h3 {
  color: #000000;
  font-size: 34px;
  font-weight: 700;
  padding-bottom: 22px;
}
.it-up-service-tab-text li {
  font-size: 18px;
  padding-left: 35px;
  position: relative;
  margin-bottom: 8px;
}
.it-up-service-tab-text li:before {
  top: 0;
  left: 0;
  color: #3cc605;
  content: "";
  position: absolute;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}
.it-up-service-tab-text .it-up-ser-btn {
  margin-top: 20px;
}

.it-up-service-img img {
  border-radius: 10px;
}

/*---------------------------------------------------- */
/*(7)- achievement area*/
/*----------------------------------------------------*/
@keyframes rotate-anim {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes icon-bounce {
  0%, 100%, 20%, 50%, 80% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}
.it-up-achivement-section {
  padding: 100px 0px;
  background-color: #fdf8f4;
}
.it-up-achivement-section .it-up-achive-shape1 {
  top: 0;
  left: -50px;
}
.it-up-achivement-section .it-up-achive-shape2 {
  bottom: 0;
  right: -50px;
}

.it-up-achivement-content {
  padding-top: 50px;
}

.col-lg-3:nth-child(2) .it-up-achivement-innerbox .inner-border:before, .col-lg-3:nth-child(2) .it-up-achivement-innerbox .inner-border:after {
  background: linear-gradient(90deg, #1cb6f6 0%, #1161dd 100%);
}
.col-lg-3:nth-child(3) .it-up-achivement-innerbox .inner-border:before, .col-lg-3:nth-child(3) .it-up-achivement-innerbox .inner-border:after {
  background: linear-gradient(90deg, #fd5d0a 0%, #ffe3ab 100%);
}
.col-lg-3:nth-child(4) .it-up-achivement-innerbox .inner-border:before, .col-lg-3:nth-child(4) .it-up-achivement-innerbox .inner-border:after {
  background: linear-gradient(90deg, #2fc678 0%, #6add1a 100%);
}

.it-up-achivement-innerbox {
  width: 190px;
  height: 190px;
  margin: 0 auto;
  padding-top: 22px;
  border-radius: 100%;
}
.it-up-achivement-innerbox .inner-border {
  width: 100%;
  top: 0;
  left: 0;
  height: 100%;
  position: absolute;
  border-radius: 100%;
  border: 2px solid #fff;
  animation-duration: 1500ms;
  animation: rotate-anim 3s infinite linear;
  animation-play-state: paused;
}
.it-up-achivement-innerbox .inner-border:before, .it-up-achivement-innerbox .inner-border:after {
  top: -6px;
  left: 0;
  right: 0;
  content: "";
  width: 12px;
  height: 12px;
  margin: 0 auto;
  position: absolute;
  border-radius: 100%;
  background: linear-gradient(90deg, #feb0d6 0%, #fd60a9 100%);
}
.it-up-achivement-innerbox .inner-border:after {
  top: auto;
  bottom: -6px;
}
.it-up-achivement-innerbox .it-up-achivement-icon {
  width: 65px;
  height: 65px;
  margin: 0 auto;
  margin-bottom: 5px;
}
.it-up-achivement-innerbox .it-up-achivement-text h3 {
  color: #000000;
  font-size: 40px;
  line-height: 1;
  font-weight: 700;
  display: inline-block;
}
.it-up-achivement-innerbox .it-up-achivement-text strong {
  color: #000000;
  font-size: 40px;
  line-height: 1;
  font-family: "Nunito";
}
.it-up-achivement-innerbox .it-up-achivement-text p {
  color: #010101;
}
.it-up-achivement-innerbox:hover .it-up-achivement-icon {
  animation: icon-bounce 0.8s ease-out infinite;
}
.it-up-achivement-innerbox:hover .inner-border {
  animation-play-state: running;
}

/*---------------------------------------------------- */
/*(8)- Testimonial area*/
/*----------------------------------------------------*/
.it-up-testimonial-section {
  padding: 100px 0px;
}
.it-up-testimonial-section .it-up-section-title-2 {
  margin: 0 auto;
  max-width: 630px;
}

.it-up-testimonial-content {
  margin-top: 45px;
}
.it-up-testimonial-content .it-up-testi-shape1 {
  top: -40px;
  left: -40px;
  animation: IT_animation_1 12s infinite linear alternate;
}
.it-up-testimonial-content .it-up-testi-shape2 {
  top: -110px;
  right: -110px;
}
.it-up-testimonial-content .it-up-testi-shape3 {
  left: -100px;
  bottom: -60px;
}
.it-up-testimonial-content .it-up-testi-shape4 {
  right: -60px;
  bottom: 0;
  animation: IT_animation_2 12s infinite linear alternate;
}

.it-up-testimonial-innerbox {
  border-radius: 10px;
  padding: 45px 30px;
  background-color: #fff;
  border: 2px solid #f3f3f3;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 0px 24px 0px rgba(1, 44, 118, 0.05);
}
.it-up-testimonial-innerbox .it-up-testimonial-img-wrap {
  width: 82px;
  height: 82px;
  margin: 0 auto;
  margin-bottom: 30px;
}
.it-up-testimonial-innerbox .it-up-testimonial-img {
  width: 82px;
  height: 82px;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 100%;
}
.it-up-testimonial-innerbox .quote-sign {
  bottom: 0;
  color: #fff;
  width: 40px;
  height: 40px;
  right: -10px;
  font-size: 40px;
  font-weight: 700;
  line-height: 60px;
  position: absolute;
  border-radius: 100%;
  font-family: "Nunito";
  display: inline-block;
  box-shadow: 0px 14px 17px 0px rgba(253, 97, 170, 0.29);
  background: linear-gradient(90deg, #feb0d6 0%, #fd60a9 100%);
}
.it-up-testimonial-innerbox .it-up-testi-author {
  margin-top: 30px;
  padding-top: 28px;
}
.it-up-testimonial-innerbox .it-up-testi-author:before {
  top: 0;
  left: 0;
  right: 0;
  content: "";
  height: 4px;
  width: 44px;
  margin: 0 auto;
  position: absolute;
  background: linear-gradient(90deg, #feb0d6 0%, #fd60a9 100%);
}
.it-up-testimonial-innerbox .it-up-testi-author h4 {
  color: #000000;
  font-size: 22px;
  font-weight: 700;
}
.it-up-testimonial-innerbox .it-up-testi-author span {
  color: #656565;
  font-size: 15px;
}
.it-up-testimonial-innerbox:hover {
  box-shadow: 0px 46px 35px 0px rgba(1, 44, 118, 0.1);
}

.it-up-testimonial-pink .quote-sign {
  box-shadow: 0px 14px 17px 0px rgba(253, 97, 170, 0.29);
  background: linear-gradient(90deg, #feb0d6 0%, #fd60a9 100%);
}
.it-up-testimonial-pink .it-up-testi-author:before {
  background: linear-gradient(90deg, #feb0d6 0%, #fd60a9 100%);
}

.it-up-testimonial-green .quote-sign {
  box-shadow: 0px 10px 11px 0px rgba(107, 236, 2, 0.29);
  background: linear-gradient(90deg, #30c677 0%, #70df11 100%);
}
.it-up-testimonial-green .it-up-testi-author:before {
  background: linear-gradient(90deg, #30c677 0%, #70df11 100%);
}

.it-up-testimonial-blue .quote-sign {
  box-shadow: 0px 18px 18px 0px rgba(22, 136, 232, 0.21);
  background-image: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 100%);
}
.it-up-testimonial-blue .it-up-testi-author:before {
  background-image: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 100%);
}

.it-up-testimonial-slider-wrap .owl-stage-outer,
.it-up-blog-slide .owl-stage-outer {
  overflow: visible;
}
.it-up-testimonial-slider-wrap .owl-item,
.it-up-blog-slide .owl-item {
  opacity: 0;
  transition: opacity 700ms;
}
.it-up-testimonial-slider-wrap .owl-item.active,
.it-up-blog-slide .owl-item.active {
  opacity: 1;
}
.it-up-testimonial-slider-wrap .owl-dots,
.it-up-blog-slide .owl-dots {
  margin-top: 35px;
  text-align: center;
}
.it-up-testimonial-slider-wrap .owl-dots .owl-dot,
.it-up-blog-slide .owl-dots .owl-dot {
  width: 10px;
  height: 10px;
  margin: 0px 6px;
  border-radius: 100%;
  position: relative;
  display: inline-block;
  background: linear-gradient(90deg, #fd5d0a 0%, #ffe3ab 100%);
}
.it-up-testimonial-slider-wrap .owl-dots .owl-dot:before,
.it-up-blog-slide .owl-dots .owl-dot:before {
  top: -5px;
  left: -5px;
  content: "";
  width: 20px;
  opacity: 0;
  height: 20px;
  position: absolute;
  border-radius: 100%;
  border: 3px solid #fd5d0a;
}
.it-up-testimonial-slider-wrap .owl-dots .owl-dot.active:before,
.it-up-blog-slide .owl-dots .owl-dot.active:before {
  opacity: 1;
}

/*---------------------------------------------------- */
/*(9)- contact area*/
/*----------------------------------------------------*/
.it-up-contact-section {
  z-index: 1;
  padding: 100px 0px;
  background-color: #f7fafb;
}
.it-up-contact-section .it-up-section-title-2 {
  margin: 0 auto;
  max-width: 625px;
}

.it-up-contact-content {
  margin-top: 45px;
  border-radius: 8px;
  background-color: #fff;
}

.it-up-contact-img .contact-shape {
  top: 75px;
  left: -85px;
  position: absolute;
}
.it-up-contact-img .it-up-contact-text {
  left: 0;
  right: 0;
  bottom: 70px;
  margin: 0 auto;
  max-width: 340px;
  position: absolute;
}
.it-up-contact-img .it-up-contact-text h4 {
  color: #fff;
  font-size: 24px;
  font-weight: 700;
  padding-top: 30px;
}

.it-up-form-wrap {
  padding: 35px 50px 40px 20px;
}
.it-up-form-wrap button {
  border: none;
  height: 50px;
  width: 210px;
  line-height: 0;
  background-size: 200%, 1px;
  transition: all 200ms linear 0ms;
  box-shadow: 0px 5px 10px 0px rgba(248, 79, 26, 0.2);
  background-image: linear-gradient(90deg, #ff780b 0%, #ee132f 50%, #ff780b);
}
.it-up-form-wrap button:hover {
  background-position: 120%;
}

.it-up-form-input,
.it-up-form-select {
  margin-bottom: 15px;
}
.it-up-form-input label,
.it-up-form-select label {
  width: 100%;
  color: #000000;
  margin-bottom: 0;
  font-weight: 700;
  padding-bottom: 5px;
}
.it-up-form-input input,
.it-up-form-input select,
.it-up-form-input textarea,
.it-up-form-select input,
.it-up-form-select select,
.it-up-form-select textarea {
  width: 100%;
  height: 55px;
  padding-left: 20px;
  background-color: #fcfcfc;
  border: 1px solid #f3f3f3;
  -webkit-appearance: none;
}
.it-up-form-input textarea,
.it-up-form-select textarea {
  height: 125px;
  padding-top: 15px;
}

.it-up-form-select:before {
  top: 45px;
  right: 20px;
  content: "";
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

/*---------------------------------------------------- */
/*(10)- Blog area*/
/*----------------------------------------------------*/
.it-up-blog-section {
  padding: 100px 0px 0px;
}
.it-up-blog-section .it-up-blog-shape-bg1 {
  top: 0;
  left: 0;
}
.it-up-blog-section .it-up-blog-shape-bg2 {
  bottom: 0;
  right: 0;
}
.it-up-blog-section .it-up-section-title {
  margin: 0 auto;
  max-width: 635px;
}

.it-up-blog-content {
  padding-top: 50px;
}

.it-up-blog-innerbox {
  overflow: hidden;
  border-radius: 10px;
  transition: 0.4s all ease-in-out;
  box-shadow: 0px 0px 24px 0px rgba(3, 5, 77, 0.08);
}
.it-up-blog-innerbox .it-up-blog-img {
  overflow: hidden;
}
.it-up-blog-innerbox .it-up-blog-text {
  padding: 45px 20px 30px 30px;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-meta {
  top: -55px;
  left: 35px;
  color: #fff;
  width: 80px;
  height: 80px;
  line-height: 1;
  font-weight: 700;
  font-size: 30px;
  padding-top: 15px;
  position: absolute;
  border-radius: 100%;
  box-shadow: 0px 8px 18px 0px rgba(241, 37, 41, 0.21);
  background: linear-gradient(90deg, #ff750c 0%, #ef192d 100%);
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-meta span {
  display: block;
  font-size: 14px;
  font-weight: 500;
}
.it-up-blog-innerbox .it-up-blog-text h3 {
  color: #000000;
  font-size: 24px;
  font-weight: 700;
  position: relative;
  line-height: 1.333;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 2px solid #eaeaea;
}
.it-up-blog-innerbox .it-up-blog-text h3:after {
  left: 0;
  height: 3px;
  width: 38px;
  content: "";
  bottom: -2px;
  position: absolute;
  background: linear-gradient(90deg, #ff750c 0%, #ef192d 100%);
}
.it-up-blog-innerbox .it-up-blog-text p {
  color: #353535;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom {
  margin-top: 20px;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-author {
  width: 85%;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-author .it-up-blog-ath-img {
  width: 35px;
  height: 35px;
  overflow: hidden;
  margin-right: 15px;
  border-radius: 100%;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-author .it-up-blog-ath-text {
  padding-top: 5px;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-author .it-up-blog-ath-text a {
  color: #000000;
  font-size: 13px;
  font-weight: 700;
  margin-right: 30px;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-author .it-up-blog-ath-text a:last-child {
  margin-right: 0;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-share {
  cursor: pointer;
  padding-top: 5px;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-share ul {
  left: 0px;
  opacity: 0;
  top: -105px;
  visibility: hidden;
  position: absolute;
  transition: 0.4s all ease-in-out;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-share ul li {
  margin-bottom: 5px;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-share:hover span {
  color: #fd5d0a;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-share:hover ul {
  opacity: 1;
  top: -115px;
  visibility: visible;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-share:hover ul li a {
  transition: 0.4s all ease-in-out;
}
.it-up-blog-innerbox .it-up-blog-text .it-up-blog-bottom .it-up-blog-share:hover ul li a:hover {
  color: #fd5d0a;
}
.it-up-blog-innerbox:hover {
  box-shadow: 0px 22px 24px 0px rgba(3, 5, 77, 0.15);
}

.it-up-blog-orange .it-up-blog-text .it-up-blog-meta {
  box-shadow: 0px 8px 18px 0px rgba(241, 37, 41, 0.21);
  background: linear-gradient(90deg, #ff750c 0%, #ef192d 100%);
}
.it-up-blog-orange .it-up-blog-text h3:after {
  background: linear-gradient(90deg, #ff750c 0%, #ef192d 100%);
}

.it-up-blog-blue .it-up-blog-text .it-up-blog-meta {
  box-shadow: 0px 0px 24px 0px rgba(3, 5, 77, 0.08);
  background: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 100%);
}
.it-up-blog-blue .it-up-blog-text h3:after {
  background: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 100%);
}

.it-up-blog-grean .it-up-blog-text .it-up-blog-meta {
  box-shadow: 0px 10px 11px 0px rgba(107, 236, 2, 0.29);
  background: linear-gradient(90deg, #30c677 0%, #70df11 100%);
}
.it-up-blog-grean .it-up-blog-text h3:after {
  background: linear-gradient(90deg, #30c677 0%, #70df11 100%);
}

/*---------------------------------------------------- */
/*(11)- Footer area*/
/*----------------------------------------------------*/
.it-up-footer-section {
  padding: 210px 0px 0px;
}
.it-up-footer-section:before {
  left: 0;
  top: -150px;
  width: 100%;
  content: "";
  height: 180%;
  position: absolute;
  background-repeat: no-repeat;
  background-image: url(../img/its/f-bg.png);
}

.it-up-footer-content-wrap {
  padding-bottom: 70px;
}

.it-up-footer-widget .widget-title {
  color: #fff;
  font-size: 24px;
  font-weight: 700;
  position: relative;
  margin-bottom: 20px !important;
}
.it-up-footer-widget .widget-title:before {
  left: 0;
  height: 3px;
  width: 30px;
  content: "";
  bottom: -5px;
  position: absolute;
  background: linear-gradient(90deg, #ff750c 0%, #ef192d 100%);
}
.it-up-footer-widget .it-up-footer-logo-widget .it-up-footer-logo {
  padding-bottom: 45px;
}
.it-up-footer-widget .it-up-footer-logo-widget p {
  color: #97a0b5;
  padding-bottom: 35px;
}
.it-up-footer-widget .it-up-footer-newslatter-widget p {
  color: #97a0b5;
}
.it-up-footer-widget .it-up-footer-newslatter-widget form {
  margin-top: 30px;
  position: relative;
}
.it-up-footer-widget .it-up-footer-newslatter-widget input {
  height: 45px;
  width: 100%;
  border: none;
  padding-left: 20px;
  background-color: #19243b;
}
.it-up-footer-widget .it-up-footer-newslatter-widget input::placeholder {
  color: #fff;
  font-size: 14px;
}
.it-up-footer-widget .it-up-footer-newslatter-widget button {
  top: 0;
  right: 0;
  width: 60px;
  padding: 0;
  color: #fff;
  height: 45px;
  border: none;
  position: absolute;
  background-color: #fff;
  transition: 0.3s all ease-in-out;
  background: linear-gradient(90deg, #1ec5fa 0%, #0d47d5 100%);
}
.it-up-footer-widget .it-up-footer-newslatter-widget .it-up-footer-social {
  margin-top: 22px;
}
.it-up-footer-widget .it-up-footer-newslatter-widget .it-up-footer-social li {
  margin-right: 15px;
}
.it-up-footer-widget .it-up-footer-newslatter-widget .it-up-footer-social li a {
  color: #494949;
  transition: 0.3s all ease-in-out;
}
.it-up-footer-widget .it-up-footer-newslatter-widget .it-up-footer-social li a:hover {
  color: #fff;
}
.it-up-footer-widget .it-up-footer-info-widget {
  padding-left: 20px;
}
.it-up-footer-widget .it-up-footer-info-widget ul {
  padding-top: 15px;
}
.it-up-footer-widget .it-up-footer-info-widget i {
  float: left;
  font-size: 14px;
  margin-top: 5px;
  color: #fff;
  margin-right: 10px;
}
.it-up-footer-widget .it-up-footer-info-widget a {
  color: #97a0b5;
  display: block;
  font-size: 14px;
  overflow: hidden;
  max-width: 170px;
}
.it-up-footer-widget .it-up-footer-info-widget .office-open-hour {
  margin-top: 10px;
}
.it-up-footer-widget .it-up-footer-info-widget .office-open-hour span {
  color: #fff;
  font-size: 14px;
  display: block;
  margin-bottom: 10px;
  font-weight: 700;
  text-decoration: underline;
}
.it-up-footer-widget .it-up-footer-info-widget .office-open-hour p {
  font-size: 14px;
  color: #97a0b5;
  max-width: 180px;
}
.it-up-footer-widget .it-up-footer-instagram-widget .insta-feed {
  padding-top: 10px;
}
.it-up-footer-widget .it-up-footer-instagram-widget .insta-feed li {
  float: left;
  width: 75px;
  height: 75px;
  margin-right: 10px;
  margin-bottom: 7px;
  position: relative;
  background-color: #000;
  transition: 0.3s all ease-in-out;
}
.it-up-footer-widget .it-up-footer-instagram-widget .insta-feed li:before {
  top: 0;
  opacity: 0;
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #000;
  transition: 0.3s all ease-in-out;
}
.it-up-footer-widget .it-up-footer-instagram-widget .insta-feed li i {
  left: 0;
  top: 50%;
  right: 0;
  opacity: 0;
  color: #fff;
  position: absolute;
  text-align: center;
  transform: translateY(-50%);
  transition: 0.3s all ease-in-out;
}
.it-up-footer-widget .it-up-footer-instagram-widget .insta-feed li:hover:before {
  opacity: 0.7;
}
.it-up-footer-widget .it-up-footer-instagram-widget .insta-feed li:hover i {
  opacity: 1;
}

.it-up-footer-copyright {
  z-index: 1;
  padding: 15px 0px;
  position: relative;
  background-color: #152039;
}
.it-up-footer-copyright p {
  color: #fff;
}

/*---------------------------------------------------- */
/*(12)- Responsive area*/
/*----------------------------------------------------*/
@media screen and (max-width: 6000px) {
  .it-up-footer-section:before {
    display: none;
  }

  .it-up-footer-section {
    margin-top: 100px;
    padding-top: 100px;
    background-color: #00052b;
  }
}
@media screen and (max-width: 1950px) {
  .it-up-footer-section:before {
    display: block;
  }

  .it-up-footer-section {
    margin-top: 0;
    padding-top: 210px;
    background-color: transparent;
  }
}
@media screen and (max-width: 1440px) {
  .it-up-banner-section .it-up-banner-deco3 {
    left: 3%;
  }

  .it-up-banner-section .it-up-banner-deco2 {
    left: 30px;
  }
}
@media screen and (max-width: 1199px) {
  .it-up-banner-section .it-up-banner-deco3 {
    top: 50px;
  }

  .it-up-main-navigation li {
    margin-left: 50px;
  }

  .it-up-header-cta-btn a {
    width: 130px;
  }

  .it-up-banner-text {
    max-width: 580px;
  }

  .it-up-service-tab-text {
    max-width: 350px;
  }
}
@media screen and (max-width: 1040px) {
  .it-up-banner-text {
    max-width: 470px;
  }
}
@media screen and (max-width: 1024px) {
  .it-up-main-navigation li {
    margin-left: 40px;
  }

  .it-up-banner-section {
    padding-bottom: 50px;
  }

  .it-up-banner-btn {
    justify-content: center;
  }

  .it-up-banner-text {
    max-width: 100%;
    text-align: center;
    padding: 250px 0px 50px;
  }

  .it-up-banner-img {
    top: 0;
    z-index: 2;
    margin: 0 auto;
    max-width: 500px;
    position: relative;
  }
}
@media screen and (max-width: 991px) {
  .it-up-main-navigation,
  .it-header-up-top {
    display: none;
  }

  .mobile_menu_button {
    display: block;
  }

  .it-header-up-sticky .it-up-header-main {
    padding: 10px 0px;
  }

  .it-header-up-sticky {
    top: 0;
  }

  .it-up-brand-logo {
    width: 130px;
    padding-top: 5px;
  }

  .it-up-header-cta-btn a {
    height: 45px;
    line-height: 45px;
    margin-right: 40px;
  }

  .it-up-banner-text h1 {
    font-size: 70px;
  }

  .it-up-about-img,
  .it-up-contact-img {
    max-width: 470px;
    margin: 0 auto;
  }

  .it-up-about-text {
    margin: 0 auto;
    padding-left: 0;
    max-width: 630px;
    margin-top: 50px;
  }

  .it-up-featured-content .col-lg-4:nth-child(2) .it-up-featured-innerbox,
  .it-up-featured-content .col-lg-4:nth-child(5) .it-up-featured-innerbox {
    transform: translateY(0px);
  }

  .it-up-achivement-innerbox {
    margin-bottom: 40px;
  }

  .it-up-blog-innerbox {
    margin: 0 auto;
    max-width: 370px;
  }

  .it-up-footer-section:before {
    display: none;
  }

  .it-up-footer-section {
    background-color: #060913;
  }

  .it-up-footer-section {
    margin-top: 80px;
    padding: 85px 0px 0px;
  }

  .it-up-footer-widget {
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 767px) {
  .it-up-service-tab-text {
    max-width: 100%;
    float: none !important;
  }

  .it-up-service-img {
    margin-top: 30px;
    text-align: center;
    float: none !important;
  }
}
@media screen and (max-width: 480px) {
  .it-up-banner-text h1 {
    font-size: 40px;
    padding-bottom: 15px;
  }

  .it-up-banner-text span {
    font-size: 18px;
  }

  .it-up-banner-text .it-up-banner-btn .it-up-banner-cta-btn,
  .it-up-banner-text .it-up-banner-btn .it-up-banner-play-btn {
    height: 45px;
    line-height: 45px;
  }

  .it-up-banner-text .it-up-banner-btn .it-up-banner-play-btn {
    width: 45px;
  }

  .it-up-section-title h2, .it-up-section-title-2 h2 {
    font-size: 30px;
  }

  .it-up-about-ft-item {
    width: 100%;
    float: none;
    margin-bottom: 30px;
  }

  .it-up-about-feature {
    padding-bottom: 10px;
  }

  .it-up-footer-widget .it-up-footer-logo-widget .footer-logo-btn,
  .it-up-form-wrap button,
  .it-up-service-tab-text .it-up-ser-btn,
  .it-up-ft-btn a,
  .it-up-about-btn a {
    height: 48px;
    width: 130px;
    line-height: 48px;
  }

  .it-up-featured-section .it-up-ft-shape2 {
    display: none;
  }

  .it-up-service-tab-text h3 {
    font-size: 26px;
  }

  .it-up-about-section {
    padding: 50px 0px;
  }

  .it-up-featured-section,
  .it-up-service-section,
  .it-up-achivement-section,
  .it-up-testimonial-section,
  .it-up-contact-section,
  .it-up-blog-section {
    padding: 50px 0px;
  }

  .it-up-featured-content {
    padding-top: 40px;
  }

  .it-up-ft-btn {
    margin-top: 0px;
  }

  .it-up-service-tab-btn .nav-tabs .nav-item {
    padding-bottom: 40px;
  }

  .it-up-service-tab-wrap {
    padding: 30px 20px;
  }
  .it-up-about-btn span {
    font-size: 16px;
  }
  .it-up-banner-text {
    padding: 195px 0px 50px;
  }
}
@media screen and (max-width: 320px) {
  .it-up-header-cta-btn a {
    display: none;
  }

  .it-up-brand-logo {
    padding-top: 0;
  }

  .mobile_menu_button {
    top: -35px;
  }
}
/*---------------------------------------------------- */