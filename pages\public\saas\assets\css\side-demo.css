.demo-page-landing {
	position: fixed;
	top: 0;
	right: 0;
	width: 100%;
	height: 100%;
	z-index: 99999;
    overflow: auto;
    background: #fff;
    text-align: center;
    transform: translate(100%,0);
    transition: transform .6s ease;
}
.side-demo span {
  top: -20px;
  right: 0px;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  position: absolute;
  background-color: #f40000;
}
.demo-page-landing.active {
    transform: translate(0,0);
}
.demo-page-landing-wrap {
	list-style: none;
	margin: auto;
	padding: 20px 10px;
	display: flex;
	flex-wrap: wrap;
	max-width: 1230px;
}
.demo-title h2 {
	font-size: 30px;
	font-weight: 700;
	margin-bottom: 0;
	padding-bottom: 10px;
	font-family: "Poppins";
}
.demo-title {
	margin: 0 auto;
	max-width: 750px;
	padding: 40px 60px 0;
}
.sa-demo-bar .sa-demo-bar-item {
    width: 25%;
    padding: 0 15px;
    margin-bottom: 35px;
}
.sa-demo-bar .sa-demo-bar-item .sa-demo-bar-item-inner {
    overflow: hidden;
    position: relative;
    -webkit-box-shadow: 0 4px 10px rgb(0 0 0 / 12%);
    -khtml-box-shadow: 0 4px 10px rgba(0,0,0,.12);
    -moz-box-shadow: 0 4px 10px rgba(0,0,0,.12);
    -ms-box-shadow: 0 4px 10px rgba(0,0,0,.12);
    -o-box-shadow: 0 4px 10px rgba(0,0,0,.12);
    box-shadow: 0 4px 10px rgb(0 0 0 / 12%);
}
.sa-demo-bar .sa-demo-bar-item .sa-demo-bar-item-inner:before {
    z-index: 1;
    content: '';
    background-color: rgba(0,0,0,.78);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    -webkit-transition: all 300ms linear 0ms;
    -khtml-transition: all 300ms linear 0ms;
    -moz-transition: all 300ms linear 0ms;
    -ms-transition: all 300ms linear 0ms;
    -o-transition: all 300ms linear 0ms;
    transition: all 300ms linear 0ms;
    opacity: 0;
}
.sa-demo-bar .sa-demo-bar-item img {
    width: 100%;
}
.sa-demo-bar-holder {
    position: absolute;
    top: 60%;
    padding: 20px;
    left: 0;
    right: 0;
    -webkit-transform: translate(0,-50%);
    -khtml-transform: translate(0,-50%);
    -moz-transform: translate(0,-50%);
    -ms-transform: translate(0,-50%);
    -o-transform: translate(0,-50%);
    transform: translate(0,-50%);
    -webkit-transition: all 300ms linear 0ms;
    -khtml-transition: all 300ms linear 0ms;
    -moz-transition: all 300ms linear 0ms;
    -ms-transition: all 300ms linear 0ms;
    -o-transition: all 300ms linear 0ms;
    transition: all 300ms linear 0ms;
    opacity: 0;
    z-index: 2;
    justify-content: center;
    display: flex;
}
.sa-demo-bar .sa-demo-bar-item h6 {
    margin-bottom: 0;
    font-size: 16px;
    margin-top: 22px;
    font-weight: 700;
    font-family: "Poppins";
}
.sa-demo-bar .sa-demo-bar-item .sa-demo-bar-item-inner:hover:before, .sa-demo-bar .sa-demo-bar-item .sa-demo-bar-item-inner:hover .sa-demo-bar-holder {
    opacity: 1;
}
.sa-demo-bar .sa-demo-bar-item .sa-demo-bar-item-inner:hover .sa-demo-bar-holder {
    top:  50%;
}
.sa-demo-bar .sa-demo-bar-item .sa-demo-bar-holder .btn {
    color: #fff;
    line-height: 38px;
    font-size: 13px;
    padding: 0 15px;
    margin: 0 3px;
    font-weight: 600;
    font-family: "Poppins";
    background-color: #4ce7f3;
    -webkit-border-radius: 38px;
    -khtml-border-radius: 38px;
    -moz-border-radius: 38px;
    -ms-border-radius: 38px;
    -o-border-radius: 38px;
    border-radius: 38px;

}
.sa-demo-bar .demo-label{
    top: 0px;
    left: 0px;
    z-index: 5;
    color: #fff;
    font-size: 14px;
    padding: 5px 10px;
    font-weight: 700;
    font-family: "Poppins";
    background-color: #d40749;
}
.side-demo-close {
    top: 40px;
    right: 40px;
    font-size: 24px;
    cursor: pointer;
    position: absolute;
}