	/*
  	Flaticon icon font: Flaticon
  	Creation date: 01/04/2021 11:54
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("../fonts/Flaticon.eot");
  src: url("../fonts/Flaticon.eot?#iefix") format("embedded-opentype"),
       url("../fonts/Flaticon.woff2") format("woff2"),
       url("../fonts/Flaticon.woff") format("woff"),
       url("../fonts/Flaticon.ttf") format("truetype"),
       url("../fonts/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("../fonts/Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
  font-style: normal;
}

.flaticon-diamond:before { content: "\f100"; }
.flaticon-settings:before { content: "\f101"; }
.flaticon-user:before { content: "\f102"; }
.flaticon-pencil:before { content: "\f103"; }
.flaticon-link:before { content: "\f104"; }
.flaticon-check:before { content: "\f105"; }
.flaticon-right-arrow:before { content: "\f106"; }
.flaticon-left-arrow:before { content: "\f107"; }
.flaticon-analytics:before { content: "\f108"; }