//  =================
//      Imports
//  =================

@import '../../../base/base';    // Base Variables


/*
 * This combined file was created by the DataTables downloader builder:
 *   https://datatables.net/download
 *
 * To rebuild or modify this file with the latest versions of the included
 * software please visit:
 *   https://datatables.net/download/#bs4/dt-1.10.16
 *
 * Included libraries:
 *   DataTables 1.10.16
 */

table.dataTable {
  clear: both;
  width: 100% !important;
  margin-top: 6px !important;
  margin-bottom: 6px !important;
  max-width: none !important;
  border-collapse: separate !important;
  border: none;

  td, th {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    padding: 9px 7px;
  }

  td.dataTables_empty, th.dataTables_empty {
    text-align: center;
  }

  &.nowrap {
    th, td {
      white-space: nowrap;
    }
  }
}

div.dataTables_wrapper div {
  &.dataTables_length {
    label {
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      color: $m-color_6;
      font-weight: 600;
    }

    select {
      width: 75px;
      display: inline-block;
    }
  }

  &.dataTables_filter {
    text-align: right;

    label {
      font-weight: normal;
      white-space: nowrap;
      text-align: left;
      color: $m-color_6;
      font-weight: 600;
    }

    input {
      margin-left: 0.5em;
      display: inline-block;
      width: auto;
    }
  }
}

table.dataTable .form-control {
  color: $m-color_6;
  font-size: 0.875rem;

  &::-webkit-input-placeholder, &::-ms-input-placeholder, &::-moz-placeholder {
    color: $m-color_6;
    font-size: 0.875rem;
  }
}

div.dataTables_wrapper div {
  &.dataTables_info {
    padding-top: 0.85em;
    white-space: nowrap;
  }

  &.dataTables_paginate {
    margin: 0;
    white-space: nowrap;
    text-align: right;

    ul.pagination {
      margin: 2px 0;
      white-space: nowrap;
      justify-content: flex-end;
    }
  }

  &.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1em 0;
  }
}

table.dataTable thead {
  > tr > {
    th {
      &.sorting_asc, &.sorting_desc, &.sorting {
        padding-right: 30px;
      }
    }

    td {
      &.sorting_asc, &.sorting_desc, &.sorting {
        padding-right: 30px;
      }
    }

    th:active, td:active {
      outline: none;
    }
  }

  .sorting, .sorting_asc, .sorting_desc, .sorting_asc_disabled, .sorting_desc_disabled {
    cursor: pointer;
    position: relative;
  }

  .sorting {
    &:before, &:after {
      position: absolute;
      bottom: 0.9em;
      display: block;
      opacity: 0.4;
    }
  }

  .sorting_asc {
    &:before, &:after {
      position: absolute;
      bottom: 0.9em;
      display: block;
      opacity: 0.4;
    }
  }

  .sorting_desc {
    &:before, &:after {
      position: absolute;
      bottom: 0.9em;
      display: block;
      opacity: 0.4;
    }
  }

  .sorting_asc_disabled {
    &:before, &:after {
      position: absolute;
      bottom: 0.9em;
      display: block;
      opacity: 0.4;
    }
  }

  .sorting_desc_disabled {
    &:before, &:after {
      position: absolute;
      bottom: 0.9em;
      display: block;
      opacity: 0.4;
    }
  }

  .sorting:before, .sorting_asc:before, .sorting_desc:before, .sorting_asc_disabled:before, .sorting_desc_disabled:before {
    right: 1em;
    content: "\2191";
  }

  .sorting:after, .sorting_asc:after, .sorting_desc:after, .sorting_asc_disabled:after, .sorting_desc_disabled:after {
    right: 0.5em;
    content: "\2193";
  }

  .sorting_asc:before, .sorting_desc:after {
    opacity: 1;
  }

  .sorting_asc_disabled:before, .sorting_desc_disabled:after {
    opacity: 0;
  }
}

div {
  &.dataTables_scrollHead table.dataTable {
    margin-bottom: 0 !important;
  }

  &.dataTables_scrollBody table {
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important;

    thead {
      .sorting:after, .sorting_asc:after, .sorting_desc:after {
        display: none;
      }
    }

    tbody tr:first-child {
      th, td {
        border-top: none;
      }
    }
  }

  &.dataTables_scrollFoot > .dataTables_scrollFootInner {
    box-sizing: content-box;

    > table {
      margin-top: 0 !important;
      border-top: none;
    }
  }
}

@media screen and (max-width: 767px) {
  div.dataTables_wrapper div {
    &.dataTables_length, &.dataTables_filter, &.dataTables_info, &.dataTables_paginate {
      text-align: center;
    }
  }
}

table {
  &.dataTable.table-sm {
    > thead > tr > th {
      padding-right: 20px;
    }

    .sorting:before, .sorting_asc:before, .sorting_desc:before {
      top: 5px;
      right: 0.85em;
    }

    .sorting:after, .sorting_asc:after, .sorting_desc:after {
      top: 5px;
    }
  }

  &.table-bordered.dataTable {
    th, td {
      border-left-width: 0;
    }

    th:last-child, td:last-child {
      border-right-width: 0;
    }

    tbody {
      th, td {
        border-bottom-width: 0;
      }
    }
  }
}

div {
  &.dataTables_scrollHead table.table-bordered {
    border-bottom-width: 0;
  }

  &.table-responsive > div.dataTables_wrapper > div.row {
    margin: 0;

    > div[class^="col-"] {
      &:first-child {
        padding-left: 0;
      }

      &:last-child {
        padding-right: 0;
      }
    }
  }
}