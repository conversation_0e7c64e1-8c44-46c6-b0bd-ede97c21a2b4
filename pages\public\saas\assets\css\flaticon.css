	/*
  	Flaticon icon font: Flaticon
  	Creation date: 03/05/2020 09:20
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("../fonts/Flaticon.eot");
  src: url("../fonts/Flaticon.eot?#iefix") format("embedded-opentype"),
       url("../fonts/Flaticon.woff2") format("woff2"),
       url("../fonts/Flaticon.woff") format("woff"),
       url("../fonts/Flaticon.ttf") format("truetype"),
       url("../fonts/Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("../fonts/Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
font-style: normal;
}
.flaticon-power-button:before { content: "\f100"; }
.flaticon-corporate-identity:before { content: "\f101"; }
.flaticon-logo:before { content: "\f102"; }
.flaticon-computer:before { content: "\f103"; }
.flaticon-duplicate:before { content: "\f104"; }
.flaticon-layers:before { content: "\f105"; }
.flaticon-pen-tool:before { content: "\f106"; }
.flaticon-bug:before { content: "\f107"; }
.flaticon-crop:before { content: "\f108"; }
.flaticon-text:before { content: "\f109"; }
.flaticon-web-development:before { content: "\f10a"; }
.flaticon-medal:before { content: "\f10b"; }
.flaticon-verified-user:before { content: "\f10c"; }
.flaticon-download:before { content: "\f10d"; }
.flaticon-idea:before { content: "\f10e"; }
.flaticon-foward:before { content: "\f10f"; }
.flaticon-quote:before { content: "\f110"; }
.flaticon-diamond:before { content: "\f111"; }