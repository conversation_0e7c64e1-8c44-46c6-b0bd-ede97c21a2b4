/*
    ==================
        Revenue
    ==================
*/

.widget-chart-one {
  .widget-heading {
    display: flex;
    justify-content: space-between;
  }

  .apexcharts-legend-marker {
    left: -5px !important;
  }

  .apexcharts-yaxis-title, .apexcharts-xaxis-title {
    font-weight: 600;
    fill: $m-color_4;
  }

  .widget-heading .tabs {
    padding: 0;
    margin: 0;

    li {
      display: inline-block;
      list-style: none;
      padding: 0 0;
    }

    a {
      font-size: 14px;
      letter-spacing: 1px;
      font-weight: 600;
      padding: 5px 7px;
      background: $primary;
      color: $white;
      border-radius: 4px;
    }
  }
}