/*

Original highlight.js style (c) <PERSON> <<EMAIL>>

*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #242424;
}


/* Base color: saturation 0; */

.hljs,
.hljs-subst {
  color: #f1f2f3;
}

.hljs-comment {
  color: #888888;
}

.hljs-keyword,
.hljs-attribute,
.hljs-selector-tag,
.hljs-meta-keyword,
.hljs-doctag,
.hljs-name {
  font-weight: bold;
}

span.hljs-attr {
    color: #c2d5ff;
}
span.hljs-name {
    color: #2196f3;
}

/* User color: hue: 0 */

.hljs-type,
.hljs-string,
.hljs-number,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-deletion {
  color: #e2a03f;
}

.hljs-title,
.hljs-section {
  color: #25d5e4;
  font-weight: bold;
}

.hljs-regexp,
.hljs-symbol,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-selector-pseudo {
  color: #f1f2f3;
}


/* Language color: hue: 90; */

.hljs-literal {
  color: #f8538d;
}

.hljs-built_in,
.hljs-bullet,
.hljs-code,
.hljs-addition {
  color: #8dbf42;
}


/* Meta color: hue: 200 */

.hljs-meta {
  color: #1f7199;
}

.hljs-meta-string {
  color: #4d99bf;
}


/* Misc effects */

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
