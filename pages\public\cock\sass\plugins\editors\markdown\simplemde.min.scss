//  =================
//      Imports
//  =================

@import '../../../base/base';    // Base Variables

/**
 * simplemde v1.11.2
 * Copyright Next Step Webs, Inc.
 * @link https://github.com/NextStepWebs/simplemde-markdown-editor
 * @license MIT
 */

.CodeMirror {
  color: $black;
}

.CodeMirror-lines {
  padding: 4px 0;
}

.CodeMirror pre {
  padding: 0 4px;
}

.CodeMirror-gutter-filler, .CodeMirror-scrollbar-filler {
  background-color: $white;
}

.CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: $m-color_1;
  white-space: nowrap;
}

.CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: $m-color_6;
  white-space: nowrap;
}

.CodeMirror-guttermarker {
  color: $black;
}

.CodeMirror-guttermarker-subtle {
  color: $m-color_6;
}

.CodeMirror-cursor {
  border-left: 1px solid $black;
  border-right: none;
  width: 0;
}

.CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}

.cm-fat-cursor {
  .CodeMirror-cursor {
    width: auto;
    border: 0 !important;
    background: #7e7;
  }

  div.CodeMirror-cursors {
    z-index: 1;
  }
}

.cm-animate-fat-cursor {
  width: auto;
  border: 0;
  -webkit-animation: blink 1.06s steps(1) infinite;
  -moz-animation: blink 1.06s steps(1) infinite;
  animation: blink 1.06s steps(1) infinite;
  background-color: #7e7;
}

@-moz-keyframes blink {
  50% {
    background-color: transparent;
  }
}

@-webkit-keyframes blink {
  50% {
    background-color: transparent;
  }
}

@keyframes blink {
  50% {
    background-color: transparent;
  }
}

.cm-tab {
  display: inline-block;
  text-decoration: inherit;
}

.CodeMirror-ruler {
  border-left: 1px solid #ccc;
  position: absolute;
}

.cm-s-default {
  .cm-header {
    color: #00f;
  }

  .cm-quote {
    color: #090;
  }
}

.cm-negative {
  color: #d44;
}

.cm-positive {
  color: #292;
}

.cm-header, .cm-strong {
  font-weight: 700;
}

.cm-em {
  font-style: italic;
}

.cm-link {
  text-decoration: underline;
}

.cm-strikethrough {
  text-decoration: line-through;
}

.cm-s-default {
  .cm-keyword {
    color: #708;
  }

  .cm-atom {
    color: #219;
  }

  .cm-number {
    color: #164;
  }

  .cm-def {
    color: #00f;
  }

  .cm-variable-2 {
    color: #05a;
  }

  .cm-variable-3 {
    color: #085;
  }

  .cm-comment {
    color: #a50;
  }

  .cm-string {
    color: #a11;
  }

  .cm-string-2 {
    color: #f50;
  }

  .cm-meta, .cm-qualifier {
    color: #555;
  }

  .cm-builtin {
    color: #30a;
  }

  .cm-bracket {
    color: #997;
  }

  .cm-tag {
    color: #170;
  }

  .cm-attribute {
    color: #00c;
  }

  .cm-hr {
    color: $m-color_6;
  }

  .cm-link {
    color: #00c;
  }
}

.cm-invalidchar, .cm-s-default .cm-error {
  color: red;
}

.CodeMirror-composing {
  border-bottom: 2px solid;
}

div.CodeMirror span {
  &.CodeMirror-matchingbracket {
    color: #0f0;
  }

  &.CodeMirror-nonmatchingbracket {
    color: #f22;
  }
}

.CodeMirror-matchingtag {
  background: rgba(255, 150, 0, 0.3);
}

.CodeMirror-activeline-background {
  background: #e8f2ff;
}

.CodeMirror {
  position: relative;
  overflow: hidden;
  background: $white;
}

.CodeMirror-scroll {
  overflow: scroll !important;
  margin-bottom: -30px;
  margin-right: -30px;
  padding-bottom: 30px;
  height: 100%;
  outline: 0;
  position: relative;
}

.CodeMirror-sizer {
  position: relative;
  border-right: 30px solid transparent;
}

.CodeMirror-gutter-filler, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler {
  position: absolute;
  z-index: 6;
  display: none;
}

.CodeMirror-vscrollbar {
  position: absolute;
  z-index: 6;
  display: none;
  right: 0;
  top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}

.CodeMirror-hscrollbar {
  bottom: 0;
  left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}

.CodeMirror-scrollbar-filler {
  right: 0;
  bottom: 0;
}

.CodeMirror-gutter-filler {
  left: 0;
  bottom: 0;
}

.CodeMirror-gutters {
  position: absolute;
  left: 0;
  top: 0;
  min-height: 100%;
  z-index: 3;
}

.CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -30px;
}

.CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
  background: 0 0 !important;
  border: none !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.CodeMirror-gutter-background {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 4;
}

.CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}

.CodeMirror-lines {
  cursor: text;
  min-height: 1px;
}

.CodeMirror pre {
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  border-width: 0;
  background: 0 0;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
}

.CodeMirror-wrap pre {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}

.CodeMirror-linebackground {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
}

.CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  overflow: auto;
}

.CodeMirror-code {
  outline: 0;
}

.CodeMirror-gutter, .CodeMirror-gutters, .CodeMirror-linenumber, .CodeMirror-scroll, .CodeMirror-sizer {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

.CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.CodeMirror-cursor {
  position: absolute;
}

.CodeMirror-measure pre {
  position: static;
}

div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}

.CodeMirror-focused div.CodeMirror-cursors, div.CodeMirror-dragcursors {
  visibility: visible;
}

.CodeMirror-selected {
  background: #d9d9d9;
}

.CodeMirror-focused .CodeMirror-selected {
  background: #d7d4f0;
}

.CodeMirror-line {
  &::selection {
    background: #d7d4f0;
  }

  > span {
    &::selection, > span::selection {
      background: #d7d4f0;
    }
  }
}

.CodeMirror-crosshair {
  cursor: crosshair;
}

.CodeMirror-line {
  &::-moz-selection {
    background: #d7d4f0;
  }

  > span {
    &::-moz-selection, > span::-moz-selection {
      background: #d7d4f0;
    }
  }
}

.cm-searching {
  background: #ffa;
  background: rgba(255, 255, 0, 0.4);
}

.cm-force-border {
  padding-right: .1px;
}

@media print {
  .CodeMirror div.CodeMirror-cursors {
    visibility: hidden;
  }
}

.cm-tab-wrap-hack:after {
  content: '';
}

span.CodeMirror-selectedtext {
  background: 0 0;
}

.CodeMirror {
  height: auto;
  min-height: 300px;
  border: none;
  border-radius: 6px;
  padding: 10px;
  font: inherit;
  z-index: 1;
  border: 1px solid $m-color_4;
  margin-top: 28px;
}

.CodeMirror-scroll {
  min-height: 300px;
}

.CodeMirror-fullscreen {
  background: $white;
  position: fixed !important;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
  height: auto;
  z-index: 9;
}

.CodeMirror-sided {
  width: 50% !important;
}

.editor-toolbar {
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  padding: 0 10px;
  border-top: 1px solid $m-color_4;
  border-bottom: 1px solid $m-color_4;
  border-left: 1px solid $m-color_4;
  border-right: 1px solid $m-color_4;
  border-radius: 6px;

  &:after {
    display: block;
    content: ' ';
    height: 1px;
  }

  &:before {
    display: block;
    content: ' ';
    height: 1px;
    margin-bottom: 8px;
  }

  &:after {
    margin-top: 8px;
  }

  &:hover {
    opacity: .8;
  }
}

.editor-wrapper input.title {
  &:focus, &:hover {
    opacity: .8;
  }
}

.editor-toolbar {
  &.fullscreen {
    width: 100%;
    height: 50px;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    padding-top: 10px;
    padding-bottom: 10px;
    box-sizing: border-box;
    background: $white;
    border: 0;
    position: fixed;
    top: 0;
    left: 0;
    opacity: 1;
    z-index: 9;

    &::before {
      width: 20px;
      height: 50px;
      background: -moz-linear-gradient(left, rgba(255, 255, 255, 1) 0, rgba(255, 255, 255, 0) 100%);
      background: -webkit-gradient(linear, left top, right top, color-stop(0, rgba(255, 255, 255, 1)), color-stop(100%, rgba(255, 255, 255, 0)));
      background: -webkit-linear-gradient(left, rgba(255, 255, 255, 1) 0, rgba(255, 255, 255, 0) 100%);
      background: -o-linear-gradient(left, rgba(255, 255, 255, 1) 0, rgba(255, 255, 255, 0) 100%);
      background: -ms-linear-gradient(left, rgba(255, 255, 255, 1) 0, rgba(255, 255, 255, 0) 100%);
      background: linear-gradient(to right, rgba(255, 255, 255, 1) 0, rgba(255, 255, 255, 0) 100%);
      position: fixed;
      top: 0;
      left: 0;
      margin: 0;
      padding: 0;
    }

    &::after {
      width: 20px;
      height: 50px;
      background: -moz-linear-gradient(left, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 1) 100%);
      background: -webkit-gradient(linear, left top, right top, color-stop(0, rgba(255, 255, 255, 0)), color-stop(100%, rgba(255, 255, 255, 1)));
      background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 1) 100%);
      background: -o-linear-gradient(left, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 1) 100%);
      background: -ms-linear-gradient(left, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 1) 100%);
      background: linear-gradient(to right, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 1) 100%);
      position: fixed;
      top: 0;
      right: 0;
      margin: 0;
      padding: 0;
    }
  }

  a {
    display: inline-block;
    text-align: center;
    text-decoration: none !important;
    color: $primary !important;
    width: 30px;
    height: 30px;
    margin: 0;
    border: 1px solid transparent;
    border-radius: 3px;
    cursor: pointer;

    &.active, &:hover {
      background: #fcfcfc;
      border-color: #95a5a6;
    }

    &:before {
      line-height: 30px;
    }
  }

  i.separator {
    display: inline-block;
    width: 0;
    border-left: 1px solid #d9d9d9;
    border-right: 1px solid $white;
    color: transparent;
    text-indent: -10px;
    margin: 0 6px;
  }

  a {
    &.fa-header-x:after {
      font-family: Arial,"Helvetica Neue",Helvetica,sans-serif;
      font-size: 65%;
      vertical-align: text-bottom;
      position: relative;
      top: 2px;
    }

    &.fa-header-1:after {
      content: "1";
    }

    &.fa-header-2:after {
      content: "2";
    }

    &.fa-header-3:after {
      content: "3";
    }

    &.fa-header-bigger:after {
      content: "▲";
    }

    &.fa-header-smaller:after {
      content: "▼";
    }
  }

  &.disabled-for-preview a:not(.no-disable) {
    pointer-events: none;
    background: $white;
    border-color: transparent;
    text-shadow: inherit;
  }
}

@media only screen and (max-width: 700px) {
  .editor-toolbar a.no-mobile {
    display: none;
  }
}

.editor-statusbar {
  padding: 8px 10px;
  font-size: 12px;
  color: #959694;
  text-align: right;

  span {
    display: inline-block;
    min-width: 4em;
    margin-left: 1em;
  }
}

.editor-preview, .editor-preview-side {
  padding: 10px;
  background: $m-color_20;
  overflow: auto;
  display: none;
  box-sizing: border-box;
}

.editor-statusbar {
  .lines:before {
    content: 'lines: ';
  }

  .words:before {
    content: 'words: ';
  }

  .characters:before {
    content: 'characters: ';
  }
}

.editor-preview {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 7;
}

.editor-preview-side {
  position: fixed;
  bottom: 0;
  width: 50%;
  top: 50px;
  right: 0;
  z-index: 9;
  border: 1px solid #ddd;
}

.editor-preview-active, .editor-preview-active-side {
  display: block;
}

.editor-preview-side > p {
  margin-top: 0;
}

.editor-preview {
  > p {
    margin-top: 0;
  }

  pre {
    background: #eee;
    margin-bottom: 10px;
  }
}

.editor-preview-side pre {
  background: #eee;
  margin-bottom: 10px;
}

.editor-preview table {
  td, th {
    border: 1px solid #ddd;
    padding: 5px;
  }
}

.editor-preview-side table {
  td, th {
    border: 1px solid #ddd;
    padding: 5px;
  }
}

.CodeMirror {
  .CodeMirror-code {
    .cm-tag {
      color: #63a35c;
    }

    .cm-attribute {
      color: #795da3;
    }

    .cm-string {
      color: #183691;
    }
  }

  .CodeMirror-selected {
    background: #d9d9d9;
  }

  .CodeMirror-code {
    .cm-header-1 {
      font-size: 200%;
      line-height: 200%;
    }

    .cm-header-2 {
      font-size: 160%;
      line-height: 160%;
    }

    .cm-header-3 {
      font-size: 125%;
      line-height: 125%;
    }

    .cm-header-4 {
      font-size: 110%;
      line-height: 110%;
    }

    .cm-comment {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 2px;
    }

    .cm-link {
      color: #7f8c8d;
    }

    .cm-url {
      color: #aab2b3;
    }

    .cm-strikethrough {
      text-decoration: line-through;
    }
  }

  .CodeMirror-placeholder {
    opacity: .5;
  }

  .cm-spell-error:not(.cm-url):not(.cm-comment):not(.cm-tag):not(.cm-word) {
    background: rgba(255, 0, 0, 0.15);
  }
}