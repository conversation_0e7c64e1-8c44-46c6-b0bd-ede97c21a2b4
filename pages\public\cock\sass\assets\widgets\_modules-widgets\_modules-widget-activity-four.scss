/*
    ========================
        Recent Activities
    ========================
*/

.widget-activity-four {
  padding-right: 0;
  padding-left: 0;

  .mt-container {
    position: relative;
    height: 272px;
    overflow: auto;
    padding-right: 12px;
  }

  .widget-heading {
    padding: 0 20px;
  }

  .widget-content {
    padding: 0 8px 0 20px;
  }

  .timeline-line .item-timeline {
    display: flex;
    width: 100%;
    margin-bottom: 12px;

    .t-dot {
      position: relative;

      &:before {
        content: '';
        position: absolute;
        border-color: inherit;
        border-radius: 50%;
        width: 8px;
        height: 8px;
        top: 5px;
        left: 5px;
        transform: translateX(-50%);
        border-color: #e0e6ed;
        background: #bfc9d4;
        z-index: 1;
      }

      &:after {
        content: '';
        position: absolute;
        border-color: inherit;
        border-width: 1px;
        border-style: solid;
        border-radius: 50%;
        width: 10px;
        height: 10px;
        left: 5px;
        transform: translateX(-50%);
        border-color: #e0e6ed;
        width: 0;
        height: auto;
        top: 12px;
        bottom: -19px;
        border-right-width: 0;
        border-top-width: 0;
        border-bottom-width: 0;
        border-radius: 0;
      }
    }

    &.timeline-primary .t-dot:before {
      background-color: #1b55e2;
      border-color: rgb(164, 189, 247);
    }

    &.timeline-success .t-dot:before {
      background-color: #009688;
      border-color: rgb(154, 210, 205);
    }

    &.timeline-danger .t-dot:before {
      background-color: #e7515a;
      border-color: rgb(241, 172, 176);
    }

    &.timeline-dark .t-dot:before {
      background-color: #3b3f5c;
      border-color: rgb(159, 163, 187);
    }

    &.timeline-secondary .t-dot:before {
      background: #1b55e2;
      border-color: #c2d5ff;
    }

    &.timeline-warning .t-dot:before {
      background-color: #e2a03f;
      border-color: rgb(222, 199, 165);
    }

    &:last-child .t-dot:after {
      display: none;
    }

    .t-meta-time {
      margin: 0;
      min-width: 100px;
      max-width: 100px;
      font-size: 12px;
      font-weight: 700;
      color: #888ea8;
      align-self: center;
    }

    .t-text {
      align-self: center;
      margin-left: 20px;
      display: flex;
      width: 100%;
      justify-content: space-between;

      p {
        font-size: 12px;
        margin: 0;
        color: #888ea8;
        font-weight: 400;
      }

      span.badge {
        position: absolute;
        right: 11px;
        padding: 2px 4px;
        font-size: 11px;
        letter-spacing: 1px;
        opacity: 0;
        font-weight: 400;
        transform: none;
      }
    }

    &:hover .t-text span.badge {
      opacity: 1;
    }

    .t-text p.t-time {
      text-align: right;
      color: #888ea8;
      font-size: 10px;
    }

    .t-time {
      margin: 0;
      min-width: 80px;
      max-width: 80px;
      font-size: 13px;
      font-weight: 600;
      color: #acb0c3;
      letter-spacing: 1px;
    }
  }

  .tm-action-btn {
    text-align: center;
    padding-top: 20px;

    button {
      background: transparent;
      box-shadow: none;
      padding: 0;
      color: #1b55e2;
      font-weight: 600;
      letter-spacing: 0.5px;
      border: none;
      font-size: 15px;
    }

    svg {
      width: 15px;
      height: 15px;
      vertical-align: inherit;
    }
  }
}