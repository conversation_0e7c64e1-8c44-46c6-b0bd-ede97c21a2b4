//  =================
//      Imports
//  =================

@import '../../base/base';    // Base Variables


/*
==================
    Switches
==================
*/

/* The switch - the box around the slider */

.switch {
  position: relative;
  display: inline-block;
  width: 35px;
  height: 18px;

  input {
    display: none;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: $m-color_2;
    -webkit-transition: .4s;
    transition: .4s;

    &:before {
      position: absolute;
      content: "";
      background-color: white;
      -webkit-transition: .4s;
      -ms-transition: .4s;
      transition: .4s;
      height: 14px;
      width: 14px;
      left: 2px;
      bottom: 2px;
      box-shadow: 0 1px 15px 1px rgba(52, 40, 104, 0.34);
    }
  }

  input:checked + .slider:before {
    -webkit-transform: translateX(17px);
    -ms-transform: translateX(17px);
    transform: translateX(17px);
  }

  .slider.round {
    border-radius: 34px;

    &:before {
      border-radius: 50%;
    }
  }

  &.s-secondary input {
    &:checked + .slider {
      background-color: $secondary;
    }

    &:focus + .slider {
      box-shadow: 0 0 1px $secondary;
    }
  }
}

/* Hide default HTML checkbox */

/* The slider */

/* Rounded Slider Switches */

/*----------Theme checkbox---------*/

.new-control {
  position: relative;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding-left: 1.5rem;
  margin-right: 1rem;
}

.new-control-input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}

.new-control.new-checkbox {
  .new-control-indicator {
    position: absolute;
    top: .25rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: $m-color_3;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 50% 50%;
    border-radius: 2px;
  }

  cursor: pointer;
  color: $dark;
  margin-bottom: 0;

  > input:checked ~ span.new-control-indicator {
    background: $m-color_6;

    &:after {
      display: block;
    }
  }

  span.new-control-indicator:after {
    border: solid $white;
    top: 50%;
    left: 50%;
    margin-left: -2px;
    margin-top: -6px;
    width: 5px;
    height: 10px;
    border-width: 0 2px 2px 0 !important;
    transform: rotate(45deg);
    content: '';
    position: absolute;
    display: none;
  }

  &[class*="checkbox-outline-"] > input:checked ~ span.new-control-indicator {
    background-color: transparent;
  }

  &.new-checkbox-line-through.checkbox-outline-primary > input:checked ~ span.new-chk-content {
    color: $primary;
  }

  &.checkbox-outline-primary > input:checked ~ span.new-control-indicator {
    border: 1px solid $primary;

    &:after {
      border-color: $primary;
    }
  }

  &.checkbox-primary > input:checked ~ span.new-control-indicator {
    background: $primary;
  }
}

.list-group-item {
  border: 1px solid $m-color_3;
  padding: 10px 12px;

  &.active {
    color: $white;
    background-color: $m-color_6;
    border-color: transparent;
    box-shadow: 0 1px 15px 1px rgba(52, 40, 104, 0.15);
  }
}

.new-control-indicator {
  background-color: $m-color_1;
}

a.list-group-item.list-group-item-action.active i {
  color: #010156;
}

code {
  color: $danger;
}

.list-group-item-action:hover {
  color: $dark;
  background-color: $m-color_1;
  box-shadow: 0px 0px 12px 1px rgba(113, 106, 202, 0.08);
}

/*------list group-----*/

/*
    Icons Meta
*/

.list-group {
  &.list-group-icons-meta .list-group-item {
    &.active {
      background-color: $m-color_6;

      .media {
        svg {
          font-size: 27px;
          color: $white;
        }

        .media-body {
          h6, p {
            color: $white;
            font-weight: 500;
          }
        }
      }
    }

    .media {
      svg {
        width: 20px;
        color: $primary;
        height: 20px;
      }

      .media-body {
        h6 {
          color: $dark;
          font-weight: 700;
          margin-bottom: 0;
          font-size: 15px;
          letter-spacing: 1px;
        }

        p {
          color: $dark;
          margin-bottom: 0;
          font-size: 12px;
          font-weight: 600;
        }
      }
    }
  }

  &.list-group-media .list-group-item {
    &.active {
      background-color: $m-color_6;

      .media .media-body {
        h6, p {
          color: $white;
          font-weight: 500;
        }
      }
    }

    .media {
      img {
        color: $primary;
        width: 42px;
        height: 42px;
      }

      .media-body {
        align-self: center;

        h6 {
          color: $dark;
          font-weight: 700;
          margin-bottom: 0;
          font-size: 16px;
          letter-spacing: 1px;
        }

        p {
          color: $dark;
          margin-bottom: 0;
          font-size: 12px;
          font-weight: 600;
        }
      }
    }
  }

  &.task-list-group .list-group-item-action.active {
    background-color: $m-color_6;
    color: $white;

    .new-control.new-checkbox {
      color: $white;
      font-size: 14px;
    }
  }
}

/*
    Image Meta
*/

/*
    task-list-group
*/