@import "tailwindcss";

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Roboto:wght@500;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', sans-serif;
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #2563eb;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1d4ed8;
}

/* Utility classes */
.btn-slide {
  display: inline-flex;
  align-items: center;
  color: #2563eb;
  font-weight: 600;
  transition: color 0.3s ease;
}

.btn-slide:hover {
  color: #1d4ed8;
}

.btn-slide:hover .fa-arrow-right {
  transform: translateX(4px);
}

.text-gradient {
  background: linear-gradient(to right, #2563eb, #1e40af);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
