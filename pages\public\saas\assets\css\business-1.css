/*-------------------------------------------------------------
Template Name: Prysm
Template URL: 
Author Name: Themexriver
Author URL: https://themeforest.net/user/themexriver/portfolio
Version: 1.0
Description: 
-------------------------------------------------------------*/


/*------------------------------------------------------------- 
TABLE OF CONTENTS: 
---------------------------------------------------------------
>> Variables
>> Mixin
>> Preloader
--------------------------------------------------------------*/

@import url("https://fonts.googleapis.com/css2?family=Spartan:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap");
.pr1-body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-size: 16px;
  line-height: 1.556;
  color: #666666;
  font-family: "Roboto", sans-serif;
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

::-moz-selection {
  color: #ffffff;
  background-color: #2782f9;
}

::selection {
  color: #ffffff;
  background-color: #2782f9;
}

::-moz-selection {
  color: #ffffff;
  background-color: #2782f9;
}

.container {
  max-width: 1200px;
}

ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

ul li {
  list-style: none;
}

[data-background] {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

a {
  color: inherit;
  text-decoration: none;
  -webkit-transition: .3s all ease-in-out;
  -o-transition: .3s all ease-in-out;
  transition: .3s all ease-in-out;
}

a:hover,
a:focus {
  text-decoration: none;
}

img {
  width: 100%;
  height: auto;
}

section {
  overflow: hidden;
}

button {
  cursor: pointer;
}

.form-control:focus,
button:visited,
button.active,
button:hover,
button:focus,
input:visited,
input.active,
input:hover,
input:focus,
textarea:hover,
textarea:focus,
a:hover,
a:focus,
a:visited,
a.active,
select,
select:hover,
select:focus,
select:visited {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  text-decoration: none;
  color: inherit;
}

.form-control {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.relative-position {
  position: relative;
}

.no-padding {
  padding: 0;
}

.pr1-headline h1,
.pr1-headline h2,
.pr1-headline h3,
.pr1-headline h4,
.pr1-headline h5,
.pr1-headline h6 {
  font-family: "Spartan", sans-serif;
  font-weight: 700;
  text-transform: capitalize;
  line-height: 1.25;
  margin-bottom: 0;
  color: #010148;
}

.pr1-headline h1 {
  font-size: 60px;
}

@media (max-width: 767.98px) {
  .pr1-headline h1 {
    font-size: 50px;
  }
}

.pr1-headline h2 {
  font-size: 48px;
}

@media (max-width: 767.98px) {
  .pr1-headline h2 {
    font-size: 36px;
  }
}

@media (max-width: 575.98px) {
  .pr1-headline h2 {
    font-size: 30px;
  }
}

.pr1-headline h3 {
  font-size: 34px;
}

@media (max-width: 767.98px) {
  .pr1-headline h3 {
    font-size: 30px;
  }
}

.pr1-headline h4 {
  font-size: 24px;
}

@media (max-width: 767.98px) {
  .pr1-headline h4 {
    font-size: 20px;
  }
}

.pr1-headline h5 {
  font-size: 22px;
}

.pr1-headline h6 {
  font-size: 18px;
}

.pr1-pera-txt p {
  color: #666666;
  margin: 0;
}

[class^="flaticon-"]:before,
[class*=" flaticon-"]:before,
[class^="flaticon-"]:after,
[class*=" flaticon-"]:after {
  margin-left: 0;
}

.loading-preloader {
  background-color: white;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 900;
}

#loading-preloader {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  height: 50px;
  width: 150px;
  visibility: visible;
  z-index: 1000;
}

.line_shape {
  width: 8px;
  height: 50px;
  margin-right: 5px;
  background-color: #0067ff;
  -webkit-animation: animate24 1s infinite;
  animation: animate24 1s infinite;
  float: left;
  opacity: 1;
}

.line_shape:last-child {
  margin-right: 0px;
}

.line_shape:nth-child(10) {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
}

.line_shape:nth-child(9) {
  -webkit-animation-delay: 0.8s;
  animation-delay: 0.8s;
}

.line_shape:nth-child(8) {
  -webkit-animation-delay: 0.7s;
  animation-delay: 0.7s;
}

.line_shape:nth-child(7) {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
}

.line_shape:nth-child(6) {
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}

.line_shape:nth-child(5) {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

.line_shape:nth-child(4) {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.line_shape:nth-child(3) {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.line_shape:nth-child(2) {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

@-webkit-keyframes animate24 {
  50% {
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
  }
}

@keyframes animate24 {
  50% {
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
  }
}

.pr1-title-area {
  margin-bottom: 30px;
}

.pr1-title-area .pr1-pera-txt {
  margin-top: 15px;
}

.pr1-primary-btn a {
  background-color: #ff6c00;
  color: #ffffff;
  text-align: center;
  width: 156px;
  height: 50px;
  line-height: 46px;
  border-radius: 6px;
  display: inline-block;
  font-weight: 600;
  border: 2px solid transparent;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-primary-btn a:hover {
  background-color: transparent;
  border: 2px solid #ff6c00;
  color: #ff6c00;
}

.pr1-readmore-btn {
  margin-top: 20px;
}

.pr1-readmore-btn a {
  font-size: 15px;
  font-weight: 700;
  font-family: "Spartan", sans-serif;
  text-transform: capitalize;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-readmore-btn a i {
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-scroll-top {
  width: 50px;
  height: 50px;
  color: #ffffff !important;
  background-color: #2782f9;
  font-size: 24px;
  text-align: center;
  line-height: 50px;
  display: inline-block;
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 10;
  border-radius: 4px;
  display: none;
  -webkit-transition: initial;
  -o-transition: initial;
  transition: initial;
}

@-webkit-keyframes pr1_shape_ups_down {
  0% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  50% {
    -webkit-transform: translateY(10px);
    transform: translateY(10px);
  }
  100% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
}

@keyframes pr1_shape_ups_down {
  0% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  50% {
    -webkit-transform: translateY(10px);
    transform: translateY(10px);
  }
  100% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
}


/********* Layout **************/

.pr1-header-section {
  background: transparent;
}

.pr1-header-section .container {
  position: relative;
}

.pr1-header-wrapper {
  background-color: #fcfdfe;
  -webkit-box-shadow: 0px 10px 20px 0px rgba(135, 135, 135, 0.1);
  box-shadow: 0px 10px 20px 0px rgba(135, 135, 135, 0.1);
  position: relative;
  z-index: 10;
}

.pr1-header-logo {
  width: 120px;
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  z-index: 12;
}

.pr1-navigation-menu {
  padding-left: 15px;
}

.pr1-navigation-menu ul {
  display: block;
  text-align: right;
}

.pr1-navigation-menu ul li {
  display: inline-block;
  position: relative;
}

.pr1-navigation-menu ul li:nth-child(1),
.pr1-navigation-menu ul li:nth-child(2),
.pr1-navigation-menu ul li:nth-child(3) {
  float: left;
}

.pr1-navigation-menu ul li.has-submenu::after {
  content: '+';
  position: absolute;
  top: 50%;
  right: 12px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: #010148;
  font-weight: 500;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-navigation-menu ul li.has-submenu .has-submenu::after {
  color: #ffffff;
  right: 10px;
}

.pr1-navigation-menu ul li.has-submenu .has-submenu ul {
  top: 10px;
  left: auto;
  right: -100%;
}

.pr1-navigation-menu ul li.has-submenu .has-submenu:hover>ul {
  top: 0;
}

.pr1-navigation-menu ul li.has-submenu:hover::after {
  -webkit-transform: translateY(-50%) rotate(45deg);
  -ms-transform: translateY(-50%) rotate(45deg);
  transform: translateY(-50%) rotate(45deg);
}

.pr1-navigation-menu ul li ul {
  position: absolute;
  width: 220px;
  background-color: #010148;
  text-align: left;
  top: 110%;
  left: 0;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  visibility: hidden;
}

.pr1-navigation-menu ul li ul li {
  display: block;
}

.pr1-navigation-menu ul li ul li:nth-child(1),
.pr1-navigation-menu ul li ul li:nth-child(2),
.pr1-navigation-menu ul li ul li:nth-child(3) {
  float: none;
}

.pr1-navigation-menu ul li ul li a {
  color: #ffffff;
  padding: 12px;
}

.pr1-navigation-menu ul li ul li a:hover {
  background-color: #2782f9;
  color: #ffffff;
  padding-left: 15px;
}

.pr1-navigation-menu ul li a {
  color: #010148;
  padding: 20px 25px;
  font-weight: 500;
  display: block;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  text-transform: capitalize;
}
.pr1-navigation-menu .side-demo span {
  top: 0;
}
.pr1-navigation-menu ul li a:hover,
.pr1-navigation-menu ul li a.active {
  color: #2782f9;
}

.pr1-navigation-menu ul li:hover>ul {
  opacity: 1;
  visibility: visible;
  top: 100%;
}

@media (max-width: 1024.98px) {
  .desktop-menu {
    display: none;
  }
}

@media (max-width: 1024.98px) {
  .pr1-header-section {
    background-color: #ffffff;
    position: relative;
    z-index: 20;
  }
}

@media (max-width: 1024.98px) {
  .pr1-header-wrapper {
    padding: 12px 0;
  }
}

@media (max-width: 1024.98px) {
  .pr1-header-logo {
    position: initial;
    -webkit-transform: translate(0);
    -ms-transform: translate(0);
    transform: translate(0);
  }
}

.pr1-mobile-menu-open {
  position: absolute;
  top: 50%;
  right: 0px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: #2782f9;
  color: #ffffff;
  font-size: 24px;
  text-align: center;
  line-height: 40px;
  border-radius: 4px;
  display: none;
}

@media (max-width: 1024.98px) {
  .pr1-mobile-menu-open {
    display: block;
  }
}

.pr1-mobile-menu {
  display: none;
  position: fixed;
  top: 0;
  right: 0;
  background-color: #010148;
  width: 300px;
  height: 100vh;
  padding-top: 20px;
  text-align: center;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  z-index: 100;
}

.pr1-mobile-menu .pr1-mobile-menu-close {
  position: absolute;
  top: 10px;
  left: 20px;
  color: #ffffff;
  cursor: pointer;
}

.pr1-mobile-menu .pr1-mobile-logo {
  width: 120px;
  display: inline-block;
}

.pr1-mobile-menu ul {
  text-align: left;
  padding-top: 40px;
}

.pr1-mobile-menu ul li a {
  padding: 12px 18px;
  display: block;
  color: #ffffff;
  text-transform: capitalize;
  font-weight: 500;
  border-bottom: 1px solid #ffffff38;
}

.pr1-mobile-menu ul li ul {
  padding-top: 0;
  display: none;
}

.pr1-mobile-menu ul li.has-submenu {
  position: relative;
}

.pr1-mobile-menu ul li.has-submenu ul li a {
  padding-left: 30px;
}

.pr1-mobile-menu ul li.has-submenu ul li ul li a {
  padding-left: 45px;
}

.pr1-mobile-menu ul li.has-submenu::after {
  content: '\f107';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: #ffffff;
  position: absolute;
  top: 10px;
  right: 25px;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-mobile-menu ul li.submenu-icon-rotate::after {
  -webkit-transform: rotate(176deg);
  -ms-transform: rotate(176deg);
  transform: rotate(176deg);
}

@media (max-width: 1024.98px) {
  .pr1-mobile-menu {
    display: block;
    width: 0;
    overflow: hidden;
  }
}

.pr1-visible-menu {
  width: 300px;
}

.pr1-visible-menu .pr1-mobile-menu-close {
  -webkit-animation: 1s fadeInLeft;
  animation: 1s fadeInLeft;
}

.pr1-visible-menu .pr1-mobile-logo {
  -webkit-animation: 1s fadeInDown;
  animation: 1s fadeInDown;
}

.pr1-visible-menu ul li {
  -webkit-animation: 1s fadeInUp;
  animation: 1s fadeInUp;
}

.pr1-sticky-on {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: auto;
  z-index: 10;
  -webkit-animation: 0.3s linear fadeInDown;
  animation: 0.3s linear fadeInDown;
  background-color: #fcfdfe;
  -webkit-box-shadow: 0px 10px 20px 0px rgba(135, 135, 135, 0.1);
  box-shadow: 0px 10px 20px 0px rgba(135, 135, 135, 0.1);
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-sticky-on .pr1-header-wrapper {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.pr1-sidebar-btn {
  width: 40px;
  height: 40px;
  background-color: #2782f9;
  color: #ffffff;
  display: inline-block;
  line-height: 40px;
  text-align: center;
  border-radius: 4px;
  font-size: 24px;
  cursor: pointer;
}

.pr1-sidebar-info .pr1-overlay-bg {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.75);
  z-index: 99999;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-sidebar-info .pr1-overlay-on {
  opacity: 1;
  visibility: visible;
}

.pr1-sidebar-info .pr1_sidebar_info_content {
  width: 380px;
  height: 100%;
  position: fixed;
  right: -380px;
  top: 0;
  background-color: #ffffff;
  z-index: 9999999;
  padding: 30px 40px;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-sidebar-info .pr1_sidebar_info_content .close-menu {
  cursor: pointer;
}

.pr1-sidebar-info .pr1_sidebar_info_content .pr1_sidebar_logo {
  text-align: center;
  margin-bottom: 60px;
}

.pr1-sidebar-info .pr1_sidebar_info_content .pr1_sidebar_logo img {
  width: 180px;
}

.pr1-sidebar-info .pr1_sidebar_info_content .pr1-pera-txt {
  line-height: 1.8em;
}

.pr1-sidebar-info .pr1_sidebar_info_content .pr1-sidebar-gallery {
  margin-top: 30px;
}

.pr1-sidebar-info .pr1_sidebar_info_content .pr1-sidebar-gallery ul li {
  display: inline-block;
  margin: 5px 5px;
}

.pr1-sidebar-info .pr1_sidebar_info_content .pr1-sidebar-social {
  margin-top: 30px;
}

.pr1-sidebar-info .pr1_sidebar_info_content .pr1-sidebar-social h5 {
  margin-bottom: 15px;
}

.pr1-sidebar-info .pr1_sidebar_info_content .pr1-sidebar-social a+a {
  margin-left: 10px;
}

.pr1-sidebar-info .pr1_sidebar_info_content .pr1-sidebar-copyright {
  text-align: center;
  margin-top: 40px;
}

.pr1-sidebar-info .pr1-sidebar-on {
  right: 0;
}

.pr1-hero-section {
  margin-top: -80px;
}

.pr1-hero-section rs-layer#slider-26-slide-85-layer-10 img.tp-rs-img {
  -webkit-animation: 3.5s pr1_shape_ups_down infinite;
  animation: 3.5s pr1_shape_ups_down infinite;
}

@media (max-width: 1850px) {
  .pr1-hero-section #slider-26-slide-85-layer-10 img {
    width: 70% !important;
    height: auto !important;
    left: 0px;
  }
}

@media (max-width: 1500px) {
  .pr1-hero-section #slider-26-slide-85-layer-10 img {
    left: -60px;
  }
}

@media (max-width: 1280px) {
  .pr1-hero-section #slider-26-slide-85-layer-10 img {
    width: 50% !important;
    top: 60px;
  }
}

.pr1-work-section {
  padding: 100px 0;
  background-color: #ffffff;
  position: relative;
}

.pr1-work-section .pr1-work-shape-1 {
  position: absolute;
  width: 180px;
  display: inline-block;
  top: 150px;
  left: 0;
}

.pr1-work-content {
  position: relative;
}

.pr1-work-content .pr1-work-shape-2 {
  position: absolute;
  width: 180px;
  display: inline-block;
  right: 0;
  bottom: 0px;
  right: -120px;
}

.pr1-work-column {
  background-color: #fff;
  padding: 20px;
  -webkit-box-shadow: 0px 17px 40px 0px rgba(135, 135, 135, 0.15);
  box-shadow: 0px 17px 40px 0px rgba(135, 135, 135, 0.15);
  margin-bottom: 30px;
}

.pr1-work-column:hover .pr1-icon-container i {
  background-color: #2782f9;
  color: #ffffff;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-work-column:hover .pr1-readmore-btn a {
  color: #2782f9;
}

.pr1-work-column:hover .pr1-readmore-btn i {
  -webkit-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.pr1-work-column .pr1-icon-container {
  margin-bottom: 20px;
}

.pr1-work-column .pr1-icon-container i {
  width: 60px;
  height: 60px;
  background-color: #ffe1ca;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 36px;
  border-radius: 3px;
  color: #ff6c00;
}

.pr1-work-column .pr1-headline {
  margin-bottom: 15px;
}

.pr1-work-column .pr1-headline h5 {
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-work-column .pr1-headline a:hover h5 {
  color: #2782f9;
}

.pr1-business-section {
  padding: 100px 0;
}

@media (max-width: 575.98px) {
  .pr1-business-section {
    padding-bottom: 70px;
  }
}

.pr1-business-left {
  position: relative;
  height: 100%;
}

.pr1-business-left .pr1-business-shape-1 {
  width: 120px;
  position: absolute;
  bottom: -50px;
  left: 30%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}

@media (max-width: 991.98px) {
  .pr1-business-left .pr1-img-bg {
    display: block;
  }
}

.pr1-business-left .pr1-img-container {
  display: inline-block;
  padding: 3px;
  border-radius: 6px;
  overflow: hidden;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#2782f9));
  background-image: -webkit-linear-gradient(top, #ffffff, #2782f9);
  background-image: -o-linear-gradient(top, #ffffff, #2782f9);
  background-image: linear-gradient(to bottom, #ffffff, #2782f9);
}

@media (max-width: 991.98px) {
  .pr1-business-left .pr1-img-container {
    position: relative;
    overflow: visible;
  }
}

@media (max-width: 575.98px) {
  .pr1-business-left .pr1-img-container {
    width: 50%;
  }
}

.pr1-business-left .pr1-img-container img {
  width: inherit;
}

@media (max-width: 767.98px) {
  .pr1-business-left .pr1-img-container img {
    width: auto;
    max-width: 100%;
  }
}

.pr1-business-left .pr1-img-container.pr1-img-2 {
  position: absolute;
  bottom: 0;
  right: 0;
}

@media (max-width: 991.98px) {
  .pr1-business-left .pr1-img-container.pr1-img-2 {
    bottom: -190px;
    right: -190px;
  }
}

@media (max-width: 575.98px) {
  .pr1-business-left .pr1-img-container.pr1-img-2 {
    width: 100%;
    right: -80%;
    bottom: -70%;
  }
}

@media (max-width: 991.98px) {
  .pr1-business-right {
    margin-top: 250px;
  }
}

@media (max-width: 460px) {
  .pr1-business-right {
    margin-top: 210px;
  }
}

.pr1-business-right .pr1-business-txt {
  margin-bottom: 30px;
}

.pr1-business-right .pr1-business-txt p {
  font-size: 18px;
  font-weight: 700;
  color: #010148;
}

.pr1-business-right .pr1-business-column {
  background-color: #fff;
  padding: 20px;
  -webkit-box-shadow: 0px 17px 40px 0px rgba(135, 135, 135, 0.15);
  box-shadow: 0px 17px 40px 0px rgba(135, 135, 135, 0.15);
}

@media (max-width: 575.98px) {
  .pr1-business-right .pr1-business-column {
    margin-bottom: 30px;
  }
}

.pr1-business-right .pr1-business-column:hover .pr1-icon-container i {
  background-color: #2782f9;
  color: #ffffff;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-business-right .pr1-business-column:hover .pr1-readmore-btn a {
  color: #2782f9;
}

.pr1-business-right .pr1-business-column:hover .pr1-readmore-btn i {
  -webkit-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.pr1-business-right .pr1-business-column .pr1-icon-container {
  margin-bottom: 20px;
}

.pr1-business-right .pr1-business-column .pr1-icon-container i {
  width: 60px;
  height: 60px;
  background-color: #ffe1ca;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 36px;
  border-radius: 3px;
  color: #ff6c00;
}

.pr1-business-right .pr1-business-column .pr1-headline {
  margin-bottom: 15px;
}

.pr1-business-right .pr1-business-column .pr1-headline h5 {
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-business-right .pr1-business-column .pr1-headline a:hover h5 {
  color: #2782f9;
}

.pr1-team-section {
  padding: 100px 0;
  background-color: #ffffff;
}

.pr1-title-area .p-70 {
  padding: 0 70px;
}

@media (max-width: 575.98px) {
  .pr1-title-area .p-70 {
    padding: 0 10px;
  }
}

.pr1-team-content {
  position: relative;
}

.pr1-team-content .pr1-team-shape-1 {
  width: 140px;
  position: absolute;
  left: -45px;
  top: -30px;
}

.pr1-team-content .pr1-team-shape-2 {
  width: 120px;
  position: absolute;
  right: -70px;
  bottom: -30px;
}

.pr1-team-slider .slick-list {
  margin: 0 -15px;
  padding-top: 20px;
  padding-bottom: 30px;
}

.pr1-team-slider .slick-slide {
  margin: 0 15px;
}

.pr1-team-slider .slick-dots {
  position: absolute;
  bottom: -40px;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.pr1-team-slider .slick-dots li {
  display: inline-block;
}

.pr1-team-slider .slick-dots li button {
  width: 35px;
  height: 8px;
  background-color: #010148;
  border-radius: 3px;
  font-size: 0;
  border: 0;
}

.pr1-team-slider .slick-dots li+li {
  margin-left: 10px;
}

.pr1-team-slider .slick-dots li.slick-active button {
  background-color: #ff6c00;
}

.pr1-team-slider .pr1-team-single {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 20px;
  background-color: #ffffff;
  -webkit-box-shadow: 0px 0px 15px 0px rgba(135, 135, 135, 0.15);
  box-shadow: 0px 0px 15px 0px rgba(135, 135, 135, 0.15);
  border-radius: 5px;
}

.pr1-team-slider .pr1-team-single:hover .pr1-team-slider-left .pr1-team-thumb .pr1-team-social {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-left {
  margin-right: 20px;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-left .pr1-team-thumb {
  width: 115px;
  height: 115px;
  border-radius: 50%;
  overflow: hidden;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#2782f9));
  background-image: -webkit-linear-gradient(top, #ffffff, #2782f9);
  background-image: -o-linear-gradient(top, #ffffff, #2782f9);
  background-image: linear-gradient(to bottom, #ffffff, #2782f9);
  padding: 2px;
  position: relative;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-left .pr1-team-thumb .pr1-team-social {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  -webkit-transform: scale(0.7);
  -ms-transform: scale(0.7);
  transform: scale(0.7);
  opacity: 0;
  text-align: center;
  background-color: rgba(39, 130, 249, 0.9);
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-left .pr1-team-thumb .pr1-team-social a {
  color: #ffffff;
  line-height: 160px;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-left .pr1-team-thumb .pr1-team-social a:hover {
  color: #ff6c00;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-left .pr1-team-thumb .pr1-team-social a+a {
  margin-left: 8px;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-left .pr1-team-thumb img {
  border-radius: 50%;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-right span {
  font-weight: 500;
  color: #010148;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-right .pr1-team-btn {
  margin-top: 10px;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-right .pr1-team-btn a {
  background-color: transparent;
  padding: 6px;
  border-radius: 5px;
  border: 2px solid #ff6c00;
  color: #010148;
  font-family: "Spartan", sans-serif;
  font-weight: 600;
  text-transform: capitalize;
  display: inline-block;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-team-slider .pr1-team-single .pr1-team-slider-right .pr1-team-btn a:hover {
  background-color: #2782f9;
  border: 2px solid transparent;
  color: #ffffff;
}

.pr1-portfolio-section {
  padding: 100px 0 70px 0;
  position: relative;
}

.pr1-portfolio-section .pr1-pf-shape-1 {
  position: absolute;
  width: 50px;
  bottom: 100px;
  left: 50px;
  -webkit-animation: 3s pr1_shape_ups_down linear infinite;
  animation: 3s pr1_shape_ups_down linear infinite;
}

.pr1-portfolio-section .pr1-primary-btn {
  margin-top: 30px;
}

.pr1-pf-column {
  position: relative;
  margin-bottom: 30px;
}

.pr1-pf-column:hover .pr1-img-container img {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.pr1-pf-column:hover .pr1-pf-overlay {
  -webkit-transform: translate(-50%, -50%) scale(1);
  -ms-transform: translate(-50%, -50%) scale(1);
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
  visibility: visible;
}

.pr1-pf-column .pr1-img-container {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#2782f9));
  background-image: -webkit-linear-gradient(top, #ffffff, #2782f9);
  background-image: -o-linear-gradient(top, #ffffff, #2782f9);
  background-image: linear-gradient(to bottom, #ffffff, #2782f9);
  padding: 2px;
  border-radius: 6px;
  overflow: hidden;
}

.pr1-pf-column .pr1-img-container .pr1-img-wrapper {
  overflow: hidden;
  border-radius: 6px;
}

.pr1-pf-column .pr1-img-container img {
  border-radius: 6px;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}

.pr1-pf-column .pr1-pf-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) scale(0.7);
  -ms-transform: translate(-50%, -50%) scale(0.7);
  transform: translate(-50%, -50%) scale(0.7);
  width: 80%;
  height: 80%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(39, 130, 249, 0.9);
  border-radius: 6px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-pf-column .pr1-pf-overlay a {
  color: #ffffff;
  text-align: center;
}

.pr1-pf-column .pr1-pf-overlay a i {
  display: block;
  margin-bottom: 10px;
}

.pr1-pf-column .pr1-pf-overlay a span {
  font-family: "Spartan", sans-serif;
  font-weight: 700;
  text-transform: capitalize;
}

.pr1-testimonial-section {
  padding: 0 0 100px 0;
  background-color: #fff;
}

.pr1-clients-slider-wrapper .slick-list {
  margin: 0 -15px;
  padding-top: 30px;
}

.pr1-clients-slider-wrapper .slick-slide {
  margin: 0 15px;
}

.pr1-clients-slider-wrapper .slick-dots {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  bottom: -70px;
}

.pr1-clients-slider-wrapper .slick-dots li {
  display: inline-block;
}

.pr1-clients-slider-wrapper .slick-dots li button {
  width: 30px;
  height: 6px;
  border-radius: 2px;
  background-color: #010148;
  font-size: 0;
  border: 0;
}

.pr1-clients-slider-wrapper .slick-dots li+li {
  margin-left: 8px;
}

.pr1-clients-slider-wrapper .slick-dots li.slick-active button {
  background-color: #ff6c00;
}

.pr1-clients-slider .pr1-client-single {
  position: relative;
}

.pr1-clients-slider .pr1-client-single .pr1-clients-quote {
  width: 87%;
}

@media (max-width: 991.98px) {
  .pr1-clients-slider .pr1-client-single .pr1-clients-quote {
    width: 100%;
  }
}

.pr1-clients-slider .pr1-client-single span.quote-icon {
  width: 50px;
  height: 50px;
  border: 2px solid #2782f9;
  border-radius: 50%;
  background-color: #ffffff;
  color: #010148;
  position: absolute;
  top: -25px;
  left: 25px;
  font-size: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.pr1-clients-slider .pr1-client-single .pr1-pera-txt {
  padding: 30px 30px 30px 20px;
  background-color: #ffffff;
  border: 2px solid #2782f9;
  border-radius: 5px;
}

.pr1-clients-slider .pr1-client-single .pr1-pera-txt p {
  display: inline-block;
}

.pr1-clients-slider .pr1-client-single .pr1-pera-txt span {
  float: right;
  color: #010148;
  font-weight: 700;
  text-transform: capitalize;
  font-family: "Spartan", sans-serif;
}

.pr1-clients-slider .pr1-client-single .pr1-clients-thumb {
  width: 100px;
  height: 100px;
  background-color: #2782f9;
  padding: 2px;
  overflow: hidden;
  border-radius: 5px;
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  z-index: 10;
}

.pr1-clients-slider .pr1-client-single .pr1-clients-thumb img {
  border-radius: 5px;
}

@media (max-width: 991.98px) {
  .pr1-clients-slider .pr1-client-single .pr1-clients-thumb {
    display: none;
  }
}

.pr1-blog-section {
  padding: 65px 0 40px 0;
}

.pr1-blog-section .pr1-title-area .pr1-pera-txt {
  width: 80%;
  display: inline-block;
}

.pr1-blog-content {
  margin-top: 30px;
}

.pr1-blog-column {
  position: relative;
  margin-bottom: 60px;
}

.pr1-blog-column:hover .pr1-img-wrapper {
  border-radius: 6px;
  overflow: hidden;
}

.pr1-blog-column:hover .pr1-img-wrapper img {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}

.pr1-blog-column .pr1-img-wrapper {
  border-radius: 6px;
  overflow: hidden;
}

.pr1-blog-column .pr1-img-wrapper img {
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  transform: scale(1.1);
}

.pr1-blog-column .pr1-blog-author {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 12px 18px;
  background-color: #ffffff;
  -webkit-box-shadow: 0px 23px 40px 0px rgba(135, 135, 135, 0.15);
  box-shadow: 0px 23px 40px 0px rgba(135, 135, 135, 0.15);
  border-radius: 6px;
  -webkit-transform: translate(20px, -30px);
  -ms-transform: translate(20px, -30px);
  transform: translate(20px, -30px);
}

.pr1-blog-column .pr1-blog-author .author-img {
  margin-right: 10px;
}

.pr1-blog-column .pr1-blog-author .author-info h6 {
  margin: 0;
}

.pr1-blog-column .pr1-blog-author .author-info span {
  color: #010148;
}

.pr1-blog-column .pr1-blog-title {
  margin-bottom: 20px;
}

.pr1-blog-column .pr1-blog-title h5 {
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-blog-column .pr1-blog-title h5:hover {
  color: #2782f9;
}

.pr1-blog-column .pr1-readmore-btn a {
  padding: 8px 24px;
  background-color: transparent;
  border: 2px solid #ff6c00;
  border-radius: 5px;
  display: inline-block;
}

.pr1-blog-column .pr1-readmore-btn a:hover {
  background-color: #2782f9;
  border: 2px solid transparent;
  color: #ffffff;
}

.pr1-footer-section {
  padding: 100px 0 0 0;
}

.pr1-footer-section .pr1-footer-top .pr1-title-area {
  margin: 0;
}

@media (max-width: 991.98px) {
  .pr1-footer-section .pr1-footer-top .pr1-title-area {
    margin-bottom: 30px;
  }
}

.pr1-footer-section .spacer {
  margin-top: 60px;
}

.pr1-footer-section .spacer hr {
  border: 0;
  border-top: 2px solid #c9e2fa;
  margin: 0;
}

.pr1-footer-contents {
  margin-top: 60px;
}

.pr1-footer-newsletter form {
  width: 100%;
  position: relative;
  -webkit-box-shadow: 0px 23px 40px 0px rgba(135, 135, 135, 0.15);
  box-shadow: 0px 23px 40px 0px rgba(135, 135, 135, 0.15);
}

.pr1-footer-newsletter form input[type="email"] {
  width: 100%;
  padding: 12px 30px 12px 18px;
  background-color: #ffffff;
  border: 0;
}

.pr1-footer-newsletter form button {
  position: absolute;
  top: 0;
  right: 0;
  border: 0;
  width: 65px;
  height: 100%;
  background-color: #2782f9;
  color: #fff;
  font-size: 25px;
  text-align: center;
}

.pr1-footer-contents .pr1-footer-widget {
  margin-bottom: 40px;
}

.pr1-footer-contents .pr1-footer-widget .logo {
  width: 120px;
  display: inline-block;
  margin-bottom: 25px;
}

.pr1-footer-contents .pr1-footer-widget .pr1-pera-txt {
  margin-bottom: 25px;
}

.pr1-footer-contents .pr1-footer-widget .pr1-footer-btn a {
  width: 150px;
  height: 45px;
  background: #ff6c00;
  display: inline-block;
  text-align: center;
  line-height: 45px;
  border-radius: 3px;
  color: #ffffff;
  font-weight: 600;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-footer-contents .pr1-footer-widget .pr1-footer-btn a:hover {
  background-color: #2782f9;
}

.pr1-footer-contents .pr1-footer-widget .pr1-headline {
  margin-bottom: 30px;
}

@media (max-width: 991.98px) {
  .pr1-footer-contents .pr1-footer-widget .pr1-headline {
    margin-bottom: 20px;
  }
}

.pr1-footer-contents .pr1-footer-widget .pr1-footer-links li+li {
  margin-top: 6px;
}

.pr1-footer-contents .pr1-footer-widget .pr1-footer-links li a {
  color: #010148;
  font-weight: 500;
  text-transform: capitalize;
  position: relative;
  padding-left: 15px;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-footer-contents .pr1-footer-widget .pr1-footer-links li a:hover {
  margin-left: 5px;
}

.pr1-footer-contents .pr1-footer-widget .pr1-footer-links li a::before {
  content: '\f105';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: 0;
  top: -2px;
}

.pr1-footer-contents .pr1-footer-widget .pr1-ft-contact ul li {
  font-weight: 500;
  color: #010148;
}

.pr1-footer-contents .pr1-footer-widget .pr1-ft-contact ul li+li {
  margin-top: 8px;
}

.pr1-footer-contents .pr1-footer-widget .pr1-ft-contact ul li i {
  margin-right: 8px;
}

.pr1-footer-copyright {
  padding: 30px 0;
}

.pr1-footer-copyright p {
  color: #010148;
  font-weight: 500;
  font-size: 15px;
}

.pr1-pricing-section {
  padding: 100px 0 70px 0;
  background-color: #ffffff;
  position: relative;
}

.pr1-pricing-section .pr1-pricing-shape-1 {
  position: absolute;
  top: 250px;
  left: 70px;
  width: 150px;
  display: inline-block;
}

.pr1-pricing-tab {
  position: relative;
  z-index: 2;
}

.pr1-pricing-tab .pr1-pricing-shape-2 {
  position: absolute;
  bottom: -50px;
  right: -100px;
  width: 170px;
  display: inline-block;
  z-index: -1;
}

.pr1-pricing-tab .nav {
  background-color: #f1fafa;
  display: inline-block;
  padding: 12px;
  border: 2px solid #010148;
  border-radius: 5px;
  position: absolute;
  top: -70px;
  right: 10px;
  z-index: 2;
}

@media (max-width: 991.98px) {
  .pr1-pricing-tab .nav {
    position: initial;
    margin: 20px 0;
  }
}

.pr1-pricing-tab .nav li {
  display: inline-block;
}

.pr1-pricing-tab .nav li+li {
  margin-left: 5px;
}

.pr1-pricing-tab .nav li a {
  padding: 8px 12px;
  background-color: transparent;
  font-family: "Spartan", sans-serif;
  font-weight: 600;
  color: #010148;
  text-transform: capitalize;
  border-radius: 5px;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-pricing-tab .nav li a.active {
  background-color: #ff6c00;
  color: #ffffff;
}

.pr1-pricing-tab .tab-content {
  margin-top: 20px;
}

.pr1-pricing-tab .tab-content .pr1-tab-column {
  padding: 30px;
  background-color: #ffffff;
  -webkit-box-shadow: 0px 23px 40px 0px rgba(135, 135, 135, 0.15);
  box-shadow: 0px 23px 40px 0px rgba(135, 135, 135, 0.15);
  margin-bottom: 30px;
}

.pr1-pricing-tab .tab-content .pr1-tab-column:hover .pr1-tab-icon i {
  background-color: #2782f9;
  color: #ffffff;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-icon {
  margin-bottom: 20px;
  display: inline-block;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-icon i {
  width: 60px;
  height: 60px;
  background-color: #ffe8d7;
  display: inline-block;
  text-align: center;
  line-height: 60px;
  font-size: 30px;
  border-radius: 3px;
  color: #ff6c00;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-price {
  margin-top: 20px;
}

@media (max-width: 1199.98px) {
  .pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-price h2 {
    font-size: 36px;
  }
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-price span {
  font-size: 16px;
  margin-left: -10px;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-features {
  margin-top: 20px;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-features li {
  font-weight: 500;
  color: #010148;
  text-transform: capitalize;
  position: relative;
  padding-left: 20px;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-features li+li {
  margin-top: 10px;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-features li::before {
  content: '\f058';
  position: absolute;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: #ff6c00;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-btn {
  margin-top: 30px;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-btn a {
  padding: 8px 18px;
  background-color: transparent;
  border: 2px solid #ff6c00;
  border-radius: 5px;
  display: inline-block;
  font-weight: 700;
  font-family: "Spartan", sans-serif;
  -webkit-transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}

.pr1-pricing-tab .tab-content .pr1-tab-column .pr1-tab-btn a:hover {
  background-color: #2782f9;
  border: 2px solid #2782f9;
  color: #ffffff;
}