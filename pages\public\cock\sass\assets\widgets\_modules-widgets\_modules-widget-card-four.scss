/*
    ==================
        Notification
    ==================
*/
body.minimal .widget-card-four {
    padding: 25px 32px;
}
.widget-card-four {
  padding: 25px 32px;
  background: $white;

  .w-content {
    display: flex;
    justify-content: space-between;
  }

  .w-info {
    h6 {
      font-weight: 600;
      margin-bottom: 0;
      color: $m-color_10;
      font-size: 23px;
      letter-spacing: 0;
    }

    p {
      font-weight: 600;
      margin-bottom: 0;
      color: $secondary;
      font-size: 16px;
    }
  }

  .w-icon {
    color: $secondary;
    background-color: $l-secondary;
    height: 45px;
    display: inline-flex;
    width: 45px;
    align-self: center;
    justify-content: center;
    border-radius: 50%;
    padding: 10px;
  }

  .progress {
    height: 8px;
    margin-bottom: 0;
    margin-top: 62px;
    margin-bottom: 0;
    height: 22px;
    padding: 4px;
    border-radius: 20px;
    box-shadow: (0 2px 2px rgba(224, 230, 237, 0.4588235294),);
  }

  .progress-bar {
    position: relative;

    &:before {
      content: '';
      height: 7px;
      width: 7px;
      background: $white;
      position: absolute;
      right: 3px;
      border-radius: 50%;
      top: 3.4px;
    }
  }
}