[{"name": "hot-reloader", "duration": 100, "timestamp": 432613284953, "id": 3, "tags": {"version": "15.4.5"}, "startTime": 1754232008002, "traceId": "98b8963405234bf8"}, {"name": "setup-dev-bundler", "duration": 505774, "timestamp": 432613266382, "id": 2, "parentId": 1, "tags": {}, "startTime": 1754232007984, "traceId": "98b8963405234bf8"}, {"name": "start-dev-server", "duration": 2356399, "timestamp": 432611858908, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "8185974784", "memory.totalMem": "17033998336", "memory.heapSizeLimit": "8566865920", "memory.rss": "168972288", "memory.heapTotal": "102821888", "memory.heapUsed": "73720352"}, "startTime": 1754232006577, "traceId": "98b8963405234bf8"}, {"name": "handle-request", "duration": 490458, "timestamp": 433039603300, "id": 4, "tags": {"url": "/_next/image?url=%2Fimages%2Fcarousel-1.jpg&w=1920&q=75"}, "startTime": 1754232434321, "traceId": "98b8963405234bf8"}, {"name": "memory-usage", "duration": 11, "timestamp": 433040093919, "id": 8, "parentId": 4, "tags": {"url": "/_next/image?url=%2Fimages%2Fcarousel-1.jpg&w=1920&q=75", "memory.rss": "165789696", "memory.heapUsed": "68369360", "memory.heapTotal": "72241152"}, "startTime": 1754232434812, "traceId": "98b8963405234bf8"}, {"name": "compile-path", "duration": 8284348, "timestamp": 433039972108, "id": 7, "tags": {"trigger": "/"}, "startTime": 1754232434690, "traceId": "98b8963405234bf8"}, {"name": "ensure-page", "duration": 8304922, "timestamp": 433039970731, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1754232434689, "traceId": "98b8963405234bf8"}]