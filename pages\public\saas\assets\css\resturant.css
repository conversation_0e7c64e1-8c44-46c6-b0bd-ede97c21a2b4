/*----------------------------------------------------
@File: Default Styles
@Author: 
@URL: 

This file contains the styling for the actual theme, this
is the file you need to edit to change the look of the
theme.
---------------------------------------------------- */
/*=====================================================================
@Template Name: 
@Author:


=====================================================================*/
@import url("https://fonts.googleapis.com/css2?family=Miniver&family=Roboto:ital,wght@0,400;0,500;0,700;1,400&display=swap");
@font-face {
  font-family: "Caviar-Dreams";
  src: url("../fonts/Caviar-Dreams.ttf.woff") format("woff"), url("../fonts/Caviar-Dreams.ttf.svg#Caviar-Dreams") format("svg"), url("../fonts/Caviar-Dreams.ttf.eot"), url("../fonts/Caviar-Dreams.eot?#iefix") format("embedded-opentype");
  font-weight: normal;
  font-style: normal;
}
@keyframes toLeftFromRight {
  49% {
    transform: translateX(-100%);
  }
  50% {
    opacity: 0;
    transform: translateX(100%);
  }
  51% {
    opacity: 1;
  }
}
@keyframes fadeFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeFromUp {
  animation-name: fadeFromUp;
}

.fadeFromRight {
  animation-name: fadeFromRight;
}

.fadeFromLeft {
  animation-name: fadeFromLeft;
}

/*global area*/
/*----------------------------------------------------*/
.app-res {
  margin: 0;
  padding: 0;
  color: #acb8bb;
  font-size: 16px;
  overflow-x: hidden;
  line-height: 1.625;
  background-color: #090d0e;
  font-family: "Roboto";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

.app-res::selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.app-res::-moz-selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.container {
  max-width: 1200px;
}

.ul-li ul {
  margin: 0;
  padding: 0;
}
.ul-li ul li {
  list-style: none;
  display: inline-block;
}

.ul-li-block ul {
  margin: 0;
  padding: 0;
}
.ul-li-block ul li {
  list-style: none;
  display: block;
}

div#app-res-preloader {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999;
  width: 100%;
  height: 100%;
  overflow: visible;
  background-color: #fff;
  background: #fff url("../img/pre.svg") no-repeat center center;
}

[data-background] {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

a {
  color: inherit;
  text-decoration: none;
}
a:hover, a:focus {
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

section {
  overflow: hidden;
}

button {
  cursor: pointer;
}

.form-control:focus,
button:visited,
button.active,
button:hover,
button:focus,
input:visited,
input.active,
input:hover,
input:focus,
textarea:hover,
textarea:focus,
a:hover,
a:focus,
a:visited,
a.active,
select,
select:hover,
select:focus,
select:visited {
  outline: none;
  box-shadow: none;
  text-decoration: none;
  color: inherit;
}

.form-control {
  box-shadow: none;
}

.pera-content p {
  margin-bottom: 0;
}
@keyframes zooming {
  0% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.05, 1.05);
  }
  100% {
    transform: scale(1, 1);
  }
}
.zooming {
  animation: zooming 18s infinite both;
}

.app-res-headline h1,
.app-res-headline h2,
.app-res-headline h3,
.app-res-headline h4,
.app-res-headline h5,
.app-res-headline h6 {
  margin: 0;
  font-family: "Caviar-Dreams";
}

.app-res-scrollup {
  width: 55px;
  right: 20px;
  z-index: 5;
  height: 55px;
  bottom: 20px;
  display: none;
  position: fixed;
  line-height: 55px;
  border-radius: 100%;
  background-color: #c9ab81;
}
.app-res-scrollup i {
  color: #fff;
  font-size: 20px;
}

.app-res-btn {
  height: 60px;
  width: 210px;
  line-height: 55px;
  letter-spacing: 2px;
  font-family: "Caviar-Dreams";
  border: 2px solid #c9ab81;
  background-color: #c9ab81;
  transition: 0.4s all ease-in-out;
}
.app-res-btn a {
  color: #fff;
  width: 100%;
  display: block;
  font-weight: 700;
}
.app-res-btn:hover {
  background-color: transparent;
}

.app-res-section-title .app-res-title-tag {
  font-size: 18px;
  color: #c9ab81;
  font-family: "Miniver", cursive;
}
.app-res-section-title h2 {
  color: #fff;
  font-size: 60px;
  font-weight: 700;
  line-height: 1.167;
  text-transform: uppercase;
  padding: 5px 0px 15px;
}
.app-res-section-title p {
  line-height: 1.75;
}

/*---------------------------------------------------- */
/*Header area*/
/*----------------------------------------------------*/
.app-res-main-header {
  top: 0;
  z-index: 10;
  width: 100%;
  position: absolute;
}

.app-res-main-header {
  padding-top: 40px;
  position: absolute;
}
.app-res-main-header .container {
  max-width: 1450px;
}
.app-res-main-header .app-res-brand-logo {
  padding-top: 10px;
}

.app-res-main-navigation {
  padding-top: 15px;
}
.app-res-main-navigation .navbar-nav {
  display: inherit;
}
.app-res-main-navigation li {
  margin-left: 55px;
}
.app-res-main-navigation li a {
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  display: inline;
  padding-bottom: 30px;
  font-family: "Caviar-Dreams";
}
.app-res-main-navigation li a.active {
  color: #c9ab81;
}
.app-res-main-navigation .dropdown {
  position: relative;
}
.app-res-main-navigation .dropdown:after {
  top: -2px;
  color: #c9ab81;
  right: -14px;
  content: "+";
  font-size: 18px;
  font-weight: 700;
  position: absolute;
  transition: 0.3s all ease-in-out;
}
.app-res-main-navigation .dropdown .dropdown-menu {
  top: 65px;
  left: 0;
  opacity: 0;
  z-index: 2;
  margin: 0px;
  padding: 0px;
  height: auto;
  width: 200px;
  border: none;
  display: block;
  border-radius: 0;
  overflow: hidden;
  visibility: hidden;
  position: absolute;
  background-color: #fff;
  transition: all 0.4s ease-in-out;
  border-bottom: 2px solid #c9ab81;
  box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
}
.app-res-main-navigation .dropdown .dropdown-menu li {
  width: 100%;
  margin-left: 0;
  border-bottom: 1px solid #e5e5e5;
}
.app-res-main-navigation .dropdown .dropdown-menu li a {
  width: 100%;
  color: #343434;
  display: block;
  font-size: 14px;
  padding: 10px 25px;
  position: relative;
  transition: 0.3s all ease-in-out;
}
.app-res-main-navigation .dropdown .dropdown-menu li a:before {
  display: none;
}
.app-res-main-navigation .dropdown .dropdown-menu li a:after {
  left: 10px;
  top: 16px;
  width: 8px;
  height: 8px;
  content: "";
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}
.app-res-main-navigation .dropdown .dropdown-menu li a:hover {
  background-color: #c9ab81;
  color: #fff;
}
.app-res-main-navigation .dropdown .dropdown-menu li a:hover:after {
  transform: scale(1);
}
.app-res-main-navigation .dropdown .dropdown-menu li:last-child {
  border-bottom: none;
}
.app-res-main-navigation .dropdown:hover .dropdown-menu {
  top: 48px;
  opacity: 1;
  visibility: visible;
}

.app-res-main-header.app-res-sticky-menu {
  top: 0px;
  z-index: 9;
  position: fixed;
  padding: 10px 0px;
  background-color: #010101;
  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
}
.app-res-main-header.app-res-sticky-menu .app-res-brand-logo {
  padding-top: 15px;
}
.app-res-main-header.app-res-sticky-menu .app-res-main-navigation {
  padding-top: 20px;
}

.app-res-menu-item .app-res-btn {
  margin-left: 75px;
}

.app-res-main-header .app-res-mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 280px;
  overflow-y: scroll;
  background-color: #1b0234;
  padding: 40px 0px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s ease-in;
}
.app-res-main-header .app-res-mobile_menu_content .app-res-mobile-main-navigation {
  width: 100%;
}
.app-res-main-header .app-res-mobile_menu_content .app-res-mobile-main-navigation .navbar-nav {
  width: 100%;
}
.app-res-main-header .app-res-mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}
.app-res-main-header .app-res-mobile_menu_content .app-res-mobile-main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid #36125a;
}
.app-res-main-header .app-res-mobile_menu_content .app-res-mobile-main-navigation .navbar-nav li:first-child {
  border-bottom: 1px solid #36125a;
}
.app-res-main-header .app-res-mobile_menu_content .app-res-mobile-main-navigation .navbar-nav li a {
  color: #afafaf;
  padding: 0;
  width: 100%;
  display: block;
  font-weight: 700;
  font-size: 14px;
  padding: 10px 30px;
  font-family: "Caviar-Dreams";
  text-transform: uppercase;
}
.app-res-main-header .app-res-mobile_menu_content .m-brand-logo {
  width: 160px;
  margin: 0 auto;
  margin-bottom: 30px;
}
.app-res-main-header .app-res-mobile_menu_wrap.mobile_menu_on .app-res-mobile_menu_content {
  right: 0px;
  transition: all 0.7s ease-out;
}
.app-res-main-header .mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: 0%;
  height: 120vh;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.5s ease-in-out;
}
.app-res-main-header .mobile_menu_overlay_on {
  overflow: hidden;
}
.app-res-main-header .app-res-mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  opacity: 1;
  visibility: visible;
}
.app-res-main-header .app-res-mobile_menu_button {
  right: 0px;
  top: -35px;
  z-index: 5;
  display: none;
  cursor: pointer;
  font-size: 24px;
  line-height: 40px;
  position: absolute;
  color: #c9ab81;
  text-align: center;
}
.app-res-main-header .app-res-mobile_menu .app-res-mobile-main-navigation .navbar-nav li a:after {
  display: none;
}
.app-res-main-header .app-res-mobile_menu .app-res-mobile-main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.app-res-main-header .app-res-mobile_menu .app-res-mobile_menu_content .app-res-mobile-main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
  width: 100%;
  background-color: transparent;
}
.app-res-main-header .app-res-mobile_menu .app-res-mobile_menu_content .app-res-mobile-main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 0 20px;
  line-height: 1;
}
.app-res-main-header .app-res-mobile_menu .dropdown {
  position: relative;
}
.app-res-main-header .app-res-mobile_menu .dropdown .dropdown-btn {
  position: absolute;
  top: 6px;
  right: 10px;
  height: 30px;
  color: #afafaf;
  line-height: 22px;
  padding: 5px 10px;
  border: 1px solid #480b86;
}
.app-res-main-header .app-res-mobile_menu .dropdown:after {
  display: none;
}
.app-res-main-header .app-res-mobile_menu .app-res-mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

/*---------------------------------------------------- */
/*slider area*/
/*----------------------------------------------------*/
.app-res-slider-wrapper .app-res-slider-img {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
}

.app-res-slider-text {
  z-index: 1;
  max-width: 420px;
  padding: 250px 0px;
  position: relative;
}
.app-res-slider-text span {
  font-size: 18px;
  display: inline-block;
  color: #c9ab81;
  font-family: "Miniver", cursive;
  opacity: 0;
  transform: scaleY(0);
  transform-origin: top;
  transition: all 1000ms ease;
}
.app-res-slider-text h1 {
  color: #fff;
  font-size: 72px;
  font-weight: 700;
  line-height: 1.139;
  padding: 10px 0px 30px;
  text-transform: uppercase;
  transform: scaleY(0);
  transform-origin: top;
  transition: all 1000ms ease;
}
.app-res-slider-text .app-res-slider-btn {
  transform: scaleY(0);
  transform-origin: top;
  opacity: 0;
  transition: all 1000ms ease;
}
.app-res-slider-text .app-res-slider-btn .app-res-btn {
  margin-right: 30px;
}

.app-res-slider-area .owl-nav .owl-next,
.app-res-slider-area .owl-nav .owl-prev {
  top: 50%;
  opacity: 0;
  color: #000;
  width: 60px;
  height: 60px;
  font-size: 20px;
  cursor: pointer;
  line-height: 60px;
  text-align: center;
  position: absolute;
  border-radius: 100%;
  transform: translateY(-50%);
  background-color: #fff;
  transition: 0.5s all ease-in-out;
}
.app-res-slider-area .owl-nav .owl-next:hover,
.app-res-slider-area .owl-nav .owl-prev:hover {
  color: #fff;
  background-color: #c9ab81;
}
.app-res-slider-area .owl-nav .owl-prev {
  left: -200px;
}
.app-res-slider-area .owl-nav .owl-next {
  right: -200px;
}
.app-res-slider-area:hover .owl-prev {
  opacity: 1;
  left: 30px;
}
.app-res-slider-area:hover .owl-next {
  opacity: 1;
  right: 30px;
}
.app-res-slider-area .owl-item.active span {
  opacity: 1;
  transform: scaleY(1);
  transition-delay: 300ms;
}
.app-res-slider-area .owl-item.active h1 {
  opacity: 1;
  transform: scaleY(1);
  transition-delay: 600ms;
}
.app-res-slider-area .owl-item.active .app-res-slider-btn {
  opacity: 1;
  transform: scaleY(1);
  transition-delay: 900ms;
}

/*---------------------------------------------------- */
/*Feature area*/
/*----------------------------------------------------*/
.app-res-feature-section {
  top: -80px;
  z-index: 2;
  overflow: visible;
  position: relative;
}

.app-res-feature-innerbox {
  padding: 36px 40px;
  background-color: #0e1415;
}
.app-res-feature-innerbox .app-res-feature-icon {
  margin-right: 25px;
  transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
}
.app-res-feature-innerbox .app-res-feature-icon svg {
  height: 60px;
  fill: #c9ab81;
}
.app-res-feature-innerbox .app-res-feature-text {
  overflow: hidden;
}
.app-res-feature-innerbox .app-res-feature-text h3 {
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  padding-bottom: 12px;
  text-transform: uppercase;
}
.app-res-feature-innerbox .app-res-feature-text p {
  color: #7e888a;
  font-size: 14px;
}
.app-res-feature-innerbox:hover .app-res-feature-icon {
  transform: rotateY(360deg);
}

/*---------------------------------------------------- */
/*About area*/
/*----------------------------------------------------*/
@keyframes shine {
  100% {
    left: 125%;
  }
}
.app-res-about-section {
  padding: 30px 0px 90px;
}

.app-res-about-img-wrap-2 {
  margin-top: 30px;
}

.app-res-about-img-item {
  overflow: hidden;
  margin-bottom: 30px;
}
.app-res-about-img-item:after {
  position: absolute;
  top: 0;
  left: -75%;
  z-index: 2;
  display: block;
  content: "";
  width: 50%;
  opacity: 0;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  transform: skewX(-25deg);
}
.app-res-about-img-item:hover:after {
  opacity: 1;
  animation: shine 0.75s;
}

.app-res-about-text-wrappper {
  padding: 100px 0px 0px 40px;
}
.app-res-about-text-wrappper .app-res-section-title {
  margin-bottom: 50px;
}
.app-res-about-text-wrappper .app-res-btn {
  width: 190px;
}

.app-res-about-section-2 {
  padding: 120px 0px;
  background-color: #06090a;
}

.app-res-about-content-2 .app-res-about-text-wrappper {
  margin: 0 auto;
  max-width: 470px;
  padding: 40px 0px 0px;
}
.app-res-about-content-2 .app-res-btn {
  margin: 0 auto;
}

.app-res-about-img-2 {
  overflow: hidden;
}
.app-res-about-img-2:after {
  position: absolute;
  top: 0;
  left: -75%;
  z-index: 2;
  display: block;
  content: "";
  width: 50%;
  opacity: 0;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  transform: skewX(-25deg);
}
.app-res-about-img-2:hover:after {
  opacity: 1;
  animation: shine 0.75s;
}

/*---------------------------------------------------- */
/*Special Menu area*/
/*----------------------------------------------------*/
.app-res-special-menu-section {
  padding: 110px 0px;
}

.app-res-special-menu-content {
  margin-top: 30px;
}

.special-menu-innerbox .special-menu-top {
  padding-bottom: 25px;
  margin-bottom: 35px;
  border-bottom: 2px dashed #1d2628;
}
.special-menu-innerbox .special-menu-top .special-menu-top-img {
  width: 80px;
  height: 80px;
  margin-right: 20px;
}
.special-menu-innerbox .special-menu-top .special-menu-top-text {
  padding-top: 30px;
}
.special-menu-innerbox .special-menu-top .special-menu-top-text h3 {
  color: #fff;
  float: left;
  font-size: 24px;
  font-weight: 700;
  text-transform: uppercase;
}
.special-menu-innerbox .special-menu-top .special-menu-top-text .sfm-item-count {
  height: 50px;
  width: 50px;
  float: right;
  font-weight: 700;
  color: #c9ab81;
  line-height: 50px;
  border-radius: 100%;
  font-family: "Caviar-Dreams";
  background-color: #131a1c;
}
.special-menu-innerbox .special-menu-food-item {
  max-width: 360px;
  margin-bottom: 25px;
  padding-bottom: 25px;
  border-bottom: 2px solid #1d2628;
}
.special-menu-innerbox .special-menu-food-item .special-menu-title-price h3 {
  float: left;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 8px;
  display: inline-block;
}
.special-menu-innerbox .special-menu-food-item .special-menu-title-price .sfm-price {
  float: right;
  color: #c9ab81;
  font-weight: 700;
  font-family: "Caviar-Dreams";
}
.special-menu-innerbox .special-menu-food-item p {
  font-size: 13px;
}

/*---------------------------------------------------- */
/*Video area*/
/*----------------------------------------------------*/
.app-res-video-section {
  z-index: 1;
  padding: 290px 0px;
}
.app-res-video-section .background-overlay {
  top: 0;
  left: 0;
  width: 100%;
  z-index: -1;
  height: 100%;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.4);
}

.location-open-time-section {
  padding: 120px 0px;
}

.app-res-location-inner {
  padding: 55px;
  margin-top: 80px;
  background-color: #06090a;
}
.app-res-location-inner .app-res-location-icon {
  margin-bottom: 20px;
}
.app-res-location-inner .app-res-location-icon svg {
  height: 70px;
  fill: #c9ab81;
}
.app-res-location-inner .app-res-location-text h3 {
  color: #fff;
  font-size: 24px;
  font-weight: 700;
  padding-bottom: 20px;
}
.app-res-location-inner .app-res-location-text p {
  color: #839095;
  padding-bottom: 5px;
}

.app-res-opening-time {
  margin-top: 80px;
  padding: 55px 55px 30px;
  background-color: #c9ab81;
}
.app-res-opening-time h3 {
  color: #fff;
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  padding-bottom: 24px;
  text-transform: uppercase;
}
.app-res-opening-time li {
  width: 100%;
  color: #fff;
  margin-bottom: 8px;
}
.app-res-opening-time li .app-res-ot-day {
  text-transform: uppercase;
}

.app-res-cta-section {
  padding-bottom: 120px;
}

.app-res-cta-content {
  padding: 60px 55px;
  background-color: #0d1213;
}
.app-res-cta-content .app-res-cta-icon {
  margin-right: 40px;
}
.app-res-cta-content .app-res-cta-icon svg {
  height: 80px;
  fill: #c9ab81;
}
.app-res-cta-content .app-res-cta-text .app-res-section-title h2 {
  font-size: 36px;
}
.app-res-cta-content .app-res-cta-text .app-res-section-title h2 a {
  color: #c9ab81;
}

/*---------------------------------------------------- */
/*Footer area*/
/*----------------------------------------------------*/
.app-res-footer-section {
  background-color: #050708;
}

.app-res-footer-logo {
  padding: 120px 0px 55px;
  border-bottom: 1px solid #0e1315;
}

.app-res-footer-widget-wrapper {
  padding: 55px 0px;
}

.app-res-footer-widget .app-res-widget-title {
  color: #fff;
  font-size: 18px;
  font-weight: 700;
}
.app-res-footer-widget .app-res-menu-social .app-res-social-text {
  max-width: 250px;
}
.app-res-footer-widget .app-res-menu-social .app-res-social-text p {
  color: #7b888b;
  padding-bottom: 30px;
}
.app-res-footer-widget .app-res-menu-social .app-res-social-text .app-res-widget-title {
  padding-bottom: 22px;
}
.app-res-footer-widget .app-res-menu-social .app-res-social-text a {
  color: #fff;
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-left: 5px;
  text-align: center;
  display: inline-block;
}
.app-res-footer-widget .app-res-menu-social .app-res-social-text a:nth-child(1) {
  background-color: #3b5999;
}
.app-res-footer-widget .app-res-menu-social .app-res-social-text a:nth-child(2) {
  background-color: #55acee;
}
.app-res-footer-widget .app-res-menu-social .app-res-social-text a:nth-child(3) {
  background-color: #cd201f;
}
.app-res-footer-widget .app-res-footer-menu li {
  margin-bottom: 18px;
}
.app-res-footer-widget .app-res-footer-menu li a {
  color: #99a6ad;
  font-size: 14px;
  font-weight: 700;
  font-family: "Caviar-Dreams";
}
.app-res-footer-widget #app-res-map {
  height: 300px;
  margin: 0 auto;
  max-width: 340px;
  filter: grayscale(1);
}
.app-res-footer-widget .app-res-blog-widget .app-res-widget-title {
  padding-bottom: 35px;
}
.app-res-footer-widget .app-res-blog-widget .app-res-footer-blog {
  margin-bottom: 22px;
}
.app-res-footer-widget .app-res-blog-widget .app-res-footer-blog h4 {
  color: #c3cacd;
  font-size: 16px;
  font-weight: 700;
  line-height: 1.625;
  padding-bottom: 5px;
}
.app-res-footer-widget .app-res-blog-widget .app-res-footer-blog span {
  font-size: 12px;
  font-weight: 700;
  color: #c9ab81;
  font-family: "Caviar-Dreams";
}

.app-res-copyright {
  padding: 30px 0px;
  background-color: #020303;
}
.app-res-copyright h4 {
  color: #7b888b;
  font-size: 16px;
}
.app-res-copyright h4 a {
  color: #c9ab81;
}

/*---------------------------------------------------- */
/*Respondsive area*/
/*----------------------------------------------------*/
@media screen and (max-width: 1440px) {
  .app-res-slider-area .owl-nav .owl-next,
.app-res-slider-area .owl-nav .owl-prev {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }

  .app-res-slider-area:hover .owl-prev {
    left: 15px;
  }

  .app-res-slider-area:hover .owl-next {
    right: 15px;
  }
}
@media screen and (max-width: 1280px) {
  .app-res-slider-text {
    margin: 0 auto;
    text-align: center;
  }

  .app-res-slider-text .app-res-slider-btn {
    justify-content: center;
  }
}
@media screen and (max-width: 1110px) {
  .app-res-btn {
    width: 160px;
  }
  .app-res-btn a {
    font-size: 14px;
  }
  .app-res-btn a i {
    display: none;
  }

  .app-res-main-navigation li {
    margin-left: 40px;
  }

  .app-res-menu-item .app-res-btn {
    margin-left: 50px;
  }
}
@media screen and (max-width: 1024px) {
  .app-res-feature-innerbox {
    padding: 36px 20px;
  }

  .app-res-feature-innerbox .app-res-feature-icon svg {
    height: 50px;
  }

  .app-res-feature-innerbox .app-res-feature-icon {
    margin-right: 15px;
  }

  .app-res-location-inner,
.app-res-opening-time {
    margin-top: 40px;
  }

  .app-res-footer-widget .app-res-menu-social .app-res-social-text {
    max-width: 195px;
  }
}
@media screen and (max-width: 991px) {
  .app-res-about-img {
    margin: 0 auto;
    max-width: 570px;
  }

  .app-res-about-img-2 {
    margin: 0 auto;
    max-width: 270px;
    margin-top: 40px;
  }

  .special-menu-innerbox {
    margin-bottom: 40px;
  }

  .special-menu-innerbox .special-menu-food-item {
    max-width: 100%;
  }

  .location-open-time-img {
    text-align: center;
  }

  .app-res-footer-widget {
    margin-bottom: 30px;
  }

  .app-res-main-header {
    padding-top: 20px;
  }

  .app-res-footer-widget .app-res-menu-social .app-res-social-text {
    max-width: 250px;
  }

  .app-res-menu-social {
    margin: 0 auto;
    max-width: 370px;
  }

  .app-res-main-navigation {
    display: none;
  }

  .app-res-btn {
    width: 140px;
    height: 45px;
    line-height: 40px;
    letter-spacing: 1px;
  }
  .app-res-btn a {
    font-size: 12px;
  }

  .app-res-menu-item .app-res-btn {
    margin-left: 0;
    margin-right: 50px;
  }

  .app-res-main-header .app-res-mobile_menu_button {
    display: block;
  }
}
@media screen and (max-width: 767px) {
  .app-res-about-img-item {
    margin: 0 auto;
    max-width: 270px;
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 580px) {
  .app-res-cta-content .app-res-cta-text .app-res-section-title h2 {
    font-size: 30px;
  }
}
@media screen and (max-width: 480px) {
  .app-res-main-header .app-res-brand-logo {
    width: 130px;
  }

  .app-res-menu-item .app-res-btn {
    width: 120px;
    height: 40px;
    line-height: 36px;
  }

  .app-res-slider-text {
    padding: 180px 0px;
  }

  .app-res-feature-section {
    top: 0;
  }

  .app-res-slider-text h1 {
    font-size: 50px;
  }

  .app-res-slider-text .app-res-slider-btn .app-res-btn {
    margin-right: 15px;
  }

  .app-res-main-header .app-res-brand-logo {
    padding-top: 0px;
  }

  .app-res-main-header.app-res-sticky-menu .app-res-brand-logo {
    padding-top: 5px;
  }

  .app-res-section-title h2 {
    font-size: 36px;
  }

  .app-res-cta-content .app-res-cta-icon {
    margin-right: 0;
    text-align: center;
    float: none !important;
  }

  .app-res-cta-text {
    text-align: center;
  }

  .app-res-cta-content {
    padding: 40px 30px 30px;
  }

  .app-res-about-text-wrappper {
    padding: 50px 0px 0px 0px;
  }

  .app-res-about-section-2 {
    padding: 50px 0px;
  }

  .app-res-special-menu-section {
    padding: 50px 0px;
  }

  .location-open-time-section {
    padding: 10px 0px 50px;
  }

  .app-res-cta-content .app-res-cta-text .app-res-section-title h2 {
    font-size: 26px;
  }

  .app-res-cta-section {
    padding-bottom: 60px;
  }

  .app-res-footer-logo {
    padding: 50px 0px 35px;
  }

  .app-res-footer-widget-wrapper {
    padding: 55px 0px 10px;
  }

  .app-res-video-section {
    padding: 150px 0px;
  }

  .app-res-slider-area .owl-nav .owl-next,
.app-res-slider-area .owl-nav .owl-prev {
    opacity: 1;
    margin: 0 5px;
    position: static;
    display: inline-block;
    transform: translateY(0);
  }

  .app-res-slider-area .owl-nav {
    left: 0;
    right: 0;
    bottom: 50px;
    text-align: center;
    position: absolute;
  }
}
@media screen and (max-width: 380px) {
  .app-res-footer-widget .app-res-menu-social .app-res-social-text {
    max-width: 225px;
  }
}
@media screen and (max-width: 320px) {
  .app-res-menu-item .app-res-btn {
    display: none;
  }

  .app-res-footer-widget .app-res-menu-social .app-res-social-text {
    max-width: 180px;
  }
}
/*---------------------------------------------------- */