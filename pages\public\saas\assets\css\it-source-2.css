@charset "UTF-8";
/*----------------------------------------------------
@File: Default Styles
@Author:  themexriver
@URL: https://themexriver.com/

This file contains the styling for the actual theme, this
is the file you need to edit to change the look of the
theme.
---------------------------------------------------- */
/*=====================================================================
@Template Name: NioBis - Corporate Business HTML Template
@Author: themexriver

CSS Table of content:-

1. Global Area 
2. Header Section
=====================================================================*/
/*=========
Font load
===========*/
@import url("https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,400;0,500;0,600;0,700;1,400&family=Poppins:wght@400;500;600;700&display=swap");
/*=========
Color Code
===========*/
/*(1)- global area*/
/*----------------------------------------------------*/
.it-nw {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-size: 16px;
  line-height: 1.5;
  color: #666666;
  font-family: "Barlow";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

::-moz-selection {
  color: #ffffff;
  background-color: #4c6df3;
}

::selection {
  color: #ffffff;
  background-color: #4c6df3;
}

::-moz-selection {
  color: #ffffff;
  background-color: #4c6df3;
}

.container {
  max-width: 1200px;
}

.ul-li ul {
  margin: 0;
  padding: 0;
}
.ul-li ul li {
  list-style: none;
  display: inline-block;
}

.ul-li-block ul {
  margin: 0;
  padding: 0;
}
.ul-li-block ul li {
  display: block;
  list-style: none;
}

div#preloader {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999;
  width: 100%;
  height: 100%;
  overflow: visible;
  background: #fff url("../img/pre.svg") no-repeat center center;
}

[data-background] {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

a {
  color: inherit;
  text-decoration: none;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
}
a:hover, a:focus {
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

section {
  overflow: hidden;
}

button {
  cursor: pointer;
}

.form-control:focus,
button:visited,
button.active,
button:hover,
button:focus,
input:visited,
input.active,
input:hover,
input:focus,
textarea:hover,
textarea:focus,
a:hover,
a:focus,
a:visited,
a.active,
select,
select:hover,
select:focus,
select:visited {
  outline: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  text-decoration: none;
  color: inherit;
}

.form-control {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.relative-position {
  position: relative;
}

.pera-content p {
  margin-bottom: 0;
}

.no-paading {
  padding: 0;
}

.it-nw .headline h1,
.it-nw .headline h2,
.it-nw .headline h3,
.it-nw .headline h4,
.it-nw .headline h5,
.it-nw .headline h6 {
  margin: 0;
  font-family: "Poppins";
}

.block-display {
  width: 100%;
  display: block;
}

.background_overlay {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
}

@-webkit-keyframes toLeftFromRight {
  49% {
    -webkit-transform: translateX(20%);
  }
  50% {
    opacity: 0;
    -webkit-transform: translateX(-20%);
  }
  51% {
    opacity: 1;
  }
}
@keyframes toLeftFromRight {
  49% {
    -webkit-transform: translateX(20%);
            transform: translateX(20%);
  }
  50% {
    opacity: 0;
    -webkit-transform: translateX(-20%);
            transform: translateX(-20%);
  }
  51% {
    opacity: 1;
  }
}
.scrollup {
  width: 55px;
  right: 20px;
  z-index: 5;
  height: 55px;
  bottom: 20px;
  display: none;
  position: fixed;
  border-radius: 100%;
  line-height: 55px;
  background-color: #4c6df3;
}
.scrollup i {
  color: #fff;
  font-size: 20px;
}

.it-nw-btn a, .it-nw-btn button {
  color: #fff;
  height: 55px;
  width: 165px;
  font-weight: 600;
  border-radius: 5px;
  font-family: "Poppins";
  border-top-right-radius: 0;
  display: inline-block;
  background-size: 200%, 1px;
  background: linear-gradient(30deg, #7f31ff 0%, #4279f1 100%);
}
.it-nw-btn a i, .it-nw-btn button i {
  width: 30px;
  height: 30px;
  line-height: 30px;
  margin-left: 10px;
  text-align: center;
  border-radius: 4px;
  background-color: #7683f7;
  border-top-right-radius: 0;
}
.it-nw-btn a:hover, .it-nw-btn button:hover {
  background-position: 120%;
}
.it-nw-btn a:hover i, .it-nw-btn button:hover i {
  -webkit-animation: toLeftFromRight 0.5s forwards;
          animation: toLeftFromRight 0.5s forwards;
}

.it-nw-section-title.middle-align {
  margin: 0 auto;
  max-width: 580px;
}
.it-nw-section-title.middle-align .it-nw-title-tag:before {
  display: block;
}
.it-nw-section-title.middle-align .it-nw-title-tag:after {
  width: 35px;
  right: -40px;
}
.it-nw-section-title .it-nw-title-tag {
  font-size: 20px;
  font-weight: 700;
  color: #4c6df3;
  position: relative;
}
.it-nw-section-title .it-nw-title-tag:before, .it-nw-section-title .it-nw-title-tag:after {
  top: 18px;
  content: "";
  height: 2px;
  width: 35px;
  position: absolute;
  background-color: #4c6df3;
}
.it-nw-section-title .it-nw-title-tag:before {
  left: -40px;
  display: none;
}
.it-nw-section-title .it-nw-title-tag:after {
  width: 120px;
  right: -125px;
}
.it-nw-section-title h2 {
  font-size: 36px;
  font-weight: 700;
  color: #00133e;
  padding: 15px 0px 14px;
}

.it-nw-fun-fact-innerbox .it-nw-fun-fact-icon i, .it-nw-why-choose-feature-list .it-nw-why-choose-feature-icon i, .it-nw-service-innerbox .it-nw-service-inner-icon i, .it-nw-feature-innerbox .it-nw-feature-inner-icon i, .it-nw-banner-btn .it-nw-banner-video a i {
  background: -webkit-gradient(linear, left top, left bottom, from(#7f31ff), to(#4279f1));
  background: linear-gradient(to bottom, #7f31ff 0%, #4279f1 100%);
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.it-nw-fooer-widget-area .widget-title:after, .it-nw-team-innerbox .it-nw-team-text .it-nw-tm-link a, .it-nw-service-innerbox .it-nw-service-inner-icon:after, .it-nw-about-cta .it-nw-ab-cta-icon, .it-nw-feature-innerbox .it-nw-feature-inner-icon:after {
  background: linear-gradient(157deg, #7f31ff 0%, #4279f1 100%);
}

.it-nw-copyright-wrap, .it-nw-testimonial-innerbox .it-nw-testimonial-img, .it-nw-testimonial-innerbox, .it-nw-fun-fact-content, .it-nw-why-choose-feature-list, .it-nw-service-innerbox, .it-nw-about-cta .it-nw-ab-cta-icon, .it-nw-feature-innerbox .it-nw-feature-inner-icon {
  border-radius: 7px;
  border-top-right-radius: 0;
}

/*---------------------------------------------------- */
/*(1)- Header area*/
/*----------------------------------------------------*/
.it-nw-header-area {
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  position: absolute;
}
.it-nw-header-area .it-nw-header-top-content {
  margin: 0px 30px;
  border-radius: 7px;
  padding: 12px 15px 15px;
  border-top-right-radius: 0;
  background: linear-gradient(8deg, #7f31ff 0%, #4279f1 100%);
}
.it-nw-header-area .it-nw-header-top-content .it-nw-header-cta {
  margin-right: 50px;
}
.it-nw-header-area .it-nw-header-top-content .it-nw-header-cta li {
  color: #fff;
  font-weight: 600;
  margin-right: 20px;
}
.it-nw-header-area .it-nw-header-top-content .it-nw-header-cta li img {
  margin-right: 8px;
}
.it-nw-header-area .it-nw-header-top-content .it-nw-header-social a {
  color: #fff;
  width: 30px;
  height: 30px;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  margin-left: 10px;
  border-radius: 100%;
  display: inline-block;
  border: 1px solid #fff;
}
.it-nw-header-area .it-nw-header-top-content .it-nw-header-social a:hover {
  color: #4c6df3;
  background-color: #fff;
}
.it-nw-header-area .it-nw-header-top-content .it-nw-header-login li:last-child a:before {
  display: none;
}
.it-nw-header-area .it-nw-header-top-content .it-nw-header-login li a {
  color: #fff;
  font-weight: 600;
  margin-left: 25px;
  position: relative;
}
.it-nw-header-area .it-nw-header-top-content .it-nw-header-login li a:before {
  top: 6px;
  right: -15px;
  width: 2px;
  content: "";
  height: 10px;
  position: absolute;
  background-color: #fff;
}

.it-nw-header-main {
  padding: 20px 15px;
  border-radius: 7px;
  background-color: #fff;
  border-top-right-radius: 0;
}
.it-nw-header-main .it-nw-header-logo {
  padding-left: 5px;
}
.it-nw-header-main .navbar-nav {
  display: inherit;
}
.it-nw-header-main .it-nw-btn {
  margin-left: 45px;
}
.it-nw-header-main .it-nw-btn a {
  width: 180px;
  height: 50px;
}
.it-nw-header-main .it-nw-btn a i {
  margin-left: 15px;
}

.it-nw-main-navigation {
  padding-top: 12px;
}
.it-nw-main-navigation li {
  margin-left: 50px;
}
.it-nw-main-navigation li a {
  color: #00036c;
  display: inline;
  font-weight: 600;
  padding-bottom: 30px;
}
.it-nw-main-navigation li a:hover,
.it-nw-main-navigation li a.active {
  color: #7938fd;
}
.it-nw-main-navigation .dropdown {
  position: relative;
}
.it-nw-main-navigation .dropdown:after {
  top: 0px;
  right: -10px;
  content: "+";
  font-size: 16px;
  font-weight: 700;
  position: absolute;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
}
.it-nw-main-navigation .dropdown:hover:after {
  color: #7938fd;
}
.it-nw-main-navigation .dropdown:hover .dropdown-menu {
  -webkit-transform: scaleY(1);
          transform: scaleY(1);
}
.it-nw-main-navigation .dropdown-menu {
  left: 0;
  top: 57px;
  z-index: 100;
  margin: 0px;
  padding: 0px;
  height: auto;
  min-width: 250px;
  display: block;
  border: none;
  border-radius: 0 !important;
  position: absolute;
  -webkit-transform: scaleY(0);
          transform: scaleY(0);
  background-color: #fff;
  background-clip: inherit;
  border-radius: 6px;
  -webkit-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  -webkit-transform-origin: center top 0;
          transform-origin: center top 0;
  -webkit-box-shadow: 0 8px 83px rgba(40, 40, 40, 0.08);
          box-shadow: 0 8px 83px rgba(40, 40, 40, 0.08);
}
.it-nw-main-navigation .dropdown-menu li {
  display: block;
  margin: 0 !important;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.it-nw-main-navigation .dropdown-menu li:last-child {
  border-bottom: none;
}
.it-nw-main-navigation .dropdown-menu li:hover .dropdown-menu {
  top: 0;
  opacity: 1;
}
.it-nw-main-navigation .dropdown-menu li:hover {
  background-color: #4c6df3;
}
.it-nw-main-navigation .dropdown-menu a {
  width: 100%;
  display: block;
  font-weight: 700;
  padding: 10px 20px 10px !important;
  font-size: 15px !important;
}
.it-nw-main-navigation .dropdown-menu a:after {
  display: none;
}
.it-nw-main-navigation .dropdown-menu a:hover {
  color: #fff !important;
}

.it-nw-header-sticky {
  top: -60px;
  width: 100%;
  z-index: 10;
  position: fixed;
  background: #fff;
  -webkit-animation-duration: 0.7s;
          animation-duration: 0.7s;
  -webkit-animation-fill-mode: both;
          animation-fill-mode: both;
  -webkit-animation-name: fadeInDown;
          animation-name: fadeInDown;
  -webkit-animation-timing-function: ease;
          animation-timing-function: ease;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
  -webkit-box-shadow: 0px 0px 18px 1px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 0px 18px 1px rgba(0, 0, 0, 0.1);
}

.it_nw_it_nw_mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  width: 280px;
  position: fixed;
  overflow-y: scroll;
  background-color: #020c16;
  padding: 100px 20px 50px 20px;
  -webkit-box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  -webkit-transition: all 0.5s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
  transition: all 0.5s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
}
.it_nw_it_nw_mobile_menu_content .main-navigation {
  width: 100%;
  margin-right: 0 !important;
}
.it_nw_it_nw_mobile_menu_content .main-navigation li {
  margin-left: 0 !important;
}
.it_nw_it_nw_mobile_menu_content .main-navigation .navbar-nav {
  width: 100%;
}
.it_nw_it_nw_mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  -webkit-transform: none !important;
          transform: none !important;
  background-color: transparent;
}
.it_nw_it_nw_mobile_menu_content .main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  margin-left: 0;
  padding-left: 0;
  margin: 5px 0px;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
}
.it_nw_it_nw_mobile_menu_content .main-navigation .navbar-nav li a {
  color: #c5c5c5;
  font-size: 15px;
  font-weight: 700;
  text-transform: uppercase;
}
.it_nw_it_nw_mobile_menu_content .m-brand-logo {
  margin-bottom: 30px;
}
.it_nw_it_nw_mobile_menu_content .dropdown-btn {
  right: 0;
  top: 0px;
  width: 30px;
  color: #c5c5c5;
  height: 30px;
  line-height: 30px;
  text-align: center;
  position: absolute;
  background-color: #061c31;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
}

.it_nw_it_nw_mobile_menu_wrap.it_nw_it_nw_mobile_menu_on .it_nw_it_nw_mobile_menu_content {
  right: 0px;
  -webkit-transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.4s;
  transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.4s;
}

.it_nw_it_nw_mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: -100%;
  height: 120vh;
  background-color: rgba(0, 0, 0, 0.9);
  -webkit-transition: all 0.8s ease-in 0.8s;
  transition: all 0.8s ease-in 0.8s;
}

.it_nw_it_nw_mobile_menu_overlay_on {
  overflow: hidden;
}

.it_nw_it_nw_mobile_menu_wrap.it_nw_it_nw_mobile_menu_on .it_nw_it_nw_mobile_menu_overlay {
  right: 0;
  -webkit-transition: all 0.8s ease-out 0s;
  transition: all 0.8s ease-out 0s;
}

.it_nw_mobile_menu_button {
  position: absolute;
  display: none;
  right: 10px;
  top: -58px;
  cursor: pointer;
  color: #fd5d0a;
  text-align: center;
  font-size: 25px;
}

.it_nw_mobile_menu .main-navigation .navbar-nav li a:after {
  display: none;
}
.it_nw_mobile_menu .main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.it_nw_mobile_menu .it_nw_it_nw_mobile_menu_content .main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  -webkit-transition: none;
  transition: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 5px 0px;
}
.it_nw_mobile_menu .it_nw_it_nw_mobile_menu_content .main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  line-height: 1;
  padding: 5px 20px;
}
.it_nw_mobile_menu .it_nw_it_nw_mobile_menu_content .main-navigation .navbar-nav .dropdown-menu li a {
  color: #c5c5c5;
  font-size: 14px;
}
.it_nw_mobile_menu .it_nw_it_nw_mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

.it-nw-banner-section {
  overflow: hidden;
}
.it-nw-banner-section:before {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
  z-index: 1;
  position: absolute;
  background-image: url(../img/its-2/bn-sh1.png);
}

.it-nw-banner-item {
  padding: 370px 0px 250px;
}

.it-nw-banner-text {
  z-index: 1;
  max-width: 580px;
  position: relative;
}
.it-nw-banner-text h1 {
  color: #fff;
  font-size: 60px;
  font-weight: 700;
  line-height: 1.367;
  padding-bottom: 20px;
}
.it-nw-banner-text h1 b {
  width: 100%;
  display: block;
}
.it-nw-banner-text p {
  color: #fff;
  font-weight: 500;
  padding-bottom: 45px;
}

.it-nw-banner-btn .it-nw-btn {
  margin-right: 40px;
}
.it-nw-banner-btn .it-nw-banner-video a {
  width: 50px;
  height: 50px;
  margin-right: 20px;
  border-radius: 100%;
  border: 2px solid #4c6df3;
}
.it-nw-banner-btn .it-nw-banner-video span {
  color: #fff;
  font-size: 15px;
  font-weight: 600;
  font-family: "Poppins";
}

.it-nw-banner-img {
  right: -145px;
  bottom: -45px;
}

/*---------------------------------------------------- */
/*(2)- Feature area*/
/*----------------------------------------------------*/
.it-nw-feature-section {
  z-index: 1;
  overflow: visible;
  padding: 95px 0px 0px;
}
.it-nw-feature-section .it-nw-ft-bg {
  left: 0;
  right: 0;
  top: 30px;
  z-index: -1;
}

.it-nw-side-bg {
  top: 0;
  right: 0;
  opacity: 0.08;
}

.it-nw-feature-content {
  padding-top: 90px;
}

.it-nw-feature-innerbox {
  padding: 70px 15px 30px;
  background-color: #fff;
  -webkit-box-shadow: 0px 10px 35px 0px rgba(0, 27, 175, 0.1);
          box-shadow: 0px 10px 35px 0px rgba(0, 27, 175, 0.1);
}
.it-nw-feature-innerbox .it-nw-feature-inner-icon {
  left: 0;
  right: 0;
  z-index: 1;
  top: -42px;
  width: 80px;
  height: 85px;
  overflow: hidden;
  margin: 0 auto;
  background-color: #fff;
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
  -webkit-box-shadow: 0px 10px 35px 0px rgba(0, 27, 175, 0.2);
          box-shadow: 0px 10px 35px 0px rgba(0, 27, 175, 0.2);
}
.it-nw-feature-innerbox .it-nw-feature-inner-icon i {
  line-height: 0.8;
  font-size: 45px;
}
.it-nw-feature-innerbox .it-nw-feature-inner-icon:after {
  top: 0;
  right: 0;
  content: "";
  width: 100%;
  z-index: -1;
  height: 0%;
  position: absolute;
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
}
.it-nw-feature-innerbox .it-nw-feature-inner-text h3 {
  font-size: 24px;
  font-weight: 700;
  color: #00133e;
  padding-bottom: 12px;
}
.it-nw-feature-innerbox:hover .it-nw-feature-inner-icon:after {
  height: 100%;
}
.it-nw-feature-innerbox:hover .it-nw-feature-inner-icon i {
  background: -webkit-gradient(linear, left top, left bottom, from(white), to(white));
  background: linear-gradient(to bottom, white 0%, white 100%);
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/*---------------------------------------------------- */
/*(3)- About area*/
/*----------------------------------------------------*/
.it-nw-about-section {
  padding: 95px 0px 100px;
}
.it-nw-about-section .it-nw-dot-bg {
  left: 0;
  bottom: 0;
  width: 60%;
  opacity: 0.06;
}

.it-nw-about-img-wrapper {
  overflow: hidden;
  max-width: 560px;
  border-radius: 10px;
}

.it-nw-about-img-2 {
  margin-left: 40px;
}

.it-nw-about-tab-btn {
  margin: 35px 0px 35px;
}
.it-nw-about-tab-btn .nav-tabs {
  border: none;
}
.it-nw-about-tab-btn .nav-tabs .nav-item.show .nav-link, .it-nw-about-tab-btn .nav-tabs .nav-link.active,
.it-nw-about-tab-btn .nav-tabs .nav-link {
  border: none;
}
.it-nw-about-tab-btn .nav-tabs .nav-link.active {
  color: #fff;
  background-color: #00133e;
}
.it-nw-about-tab-btn li {
  margin-right: 25px;
}
.it-nw-about-tab-btn li a {
  color: #1b2a4f;
  font-size: 15px;
  font-weight: 600;
  border-radius: 5px;
  padding: 15px 30px;
  background-color: #fff;
  font-family: "Poppins";
  -webkit-box-shadow: 0px 5px 30px 0px rgba(0, 27, 175, 0.1);
          box-shadow: 0px 5px 30px 0px rgba(0, 27, 175, 0.1);
}
.it-nw-about-tab-btn li:last-child {
  margin-right: 0;
}

.it-nw-about-tab-content p {
  padding-bottom: 25px;
}
.it-nw-about-tab-content li {
  font-weight: 600;
  padding-left: 25px;
  position: relative;
  color: #00133e;
}
.it-nw-about-tab-content li:before {
  top: 0;
  left: 0;
  content: "";
  font-weight: 900;
  position: absolute;
  color: #00133e;
  font-family: "Font Awesome 5 Free";
}

.it-nw-about-bottom {
  margin-top: 35px;
}
.it-nw-about-bottom .it-nw-btn {
  margin-right: 65px;
}
.it-nw-about-bottom .it-nw-about-arrow {
  left: 38%;
  top: -25px;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}

.it-nw-about-cta .it-nw-ab-cta-icon {
  width: 50px;
  height: 50px;
  margin-right: 15px;
}
.it-nw-about-cta .it-nw-ab-cta-icon i {
  color: #fff;
  font-size: 25px;
}
.it-nw-about-cta .it-nx-about-cta-text span {
  font-size: 18px;
  font-weight: 500;
  color: #00133e;
}
.it-nw-about-cta .it-nx-about-cta-text h4 {
  font-size: 18px;
  font-weight: 700;
  color: #00133e;
}

/*---------------------------------------------------- */
/*(4)- Service area*/
/*----------------------------------------------------*/
.it-nw-service-section {
  background-color: #f5f6f9;
  padding: 120px 0px 100px;
}
.it-nw-service-section .it-nw-section-title {
  padding-top: 20px;
}
.it-nw-service-section .it-nw-btn {
  margin-top: 50px;
}
.it-nw-service-section .it-nw-btn a {
  margin: 0 auto;
}
.it-nw-service-section .it-nw-service-sh1 {
  left: 0;
  bottom: 0;
}
.it-nw-service-section .it-nw-service-sh2 {
  right: 0;
  bottom: 0;
}

.it-nw-service-innerbox {
  background-color: #fff;
  padding: 55px 25px 25px;
  -webkit-box-shadow: 0px 5px 35px 0px rgba(0, 27, 175, 0.05);
          box-shadow: 0px 5px 35px 0px rgba(0, 27, 175, 0.05);
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
}
.it-nw-service-innerbox .it-nw-service-inner-icon {
  top: -30px;
  z-index: 1;
  width: 60px;
  height: 60px;
  overflow: hidden;
  position: absolute;
  border-radius: 10px;
  background-color: #fff;
  border-top-right-radius: 0;
  -webkit-box-shadow: 0px 5px 35px 0px rgba(0, 27, 175, 0.15);
          box-shadow: 0px 5px 35px 0px rgba(0, 27, 175, 0.15);
}
.it-nw-service-innerbox .it-nw-service-inner-icon:after {
  top: 0;
  right: 0;
  content: "";
  width: 100%;
  z-index: -1;
  height: 0%;
  position: absolute;
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
}
.it-nw-service-innerbox .it-nw-service-inner-icon i {
  font-size: 35px;
}
.it-nw-service-innerbox .it-nw-service-inner-text h3 {
  font-size: 20px;
  font-weight: 700;
  color: #00133e;
  padding-bottom: 8px;
}
.it-nw-service-innerbox .it-nw-service-inner-text p {
  padding-bottom: 10px;
}
.it-nw-service-innerbox .it-nw-service-inner-text a {
  color: #000000;
  font-size: 15px;
  font-weight: 600;
  font-family: "Poppins";
}
.it-nw-service-innerbox .it-nw-service-inner-text a i {
  -webkit-transition: 300ms all ease;
  transition: 300ms all ease;
}
.it-nw-service-innerbox .it-nw-service-inner-text a:hover {
  color: #4c6df3;
}
.it-nw-service-innerbox .it-nw-service-inner-text a:hover i {
  margin-left: 5px;
}
.it-nw-service-innerbox:hover {
  -webkit-box-shadow: 0px 20px 40px 0px rgba(0, 27, 175, 0.1);
          box-shadow: 0px 20px 40px 0px rgba(0, 27, 175, 0.1);
}
.it-nw-service-innerbox:hover .it-nw-service-inner-icon:after {
  height: 100%;
}
.it-nw-service-innerbox:hover .it-nw-service-inner-icon i {
  background: -webkit-gradient(linear, left top, left bottom, from(white), to(white));
  background: linear-gradient(to bottom, white 0%, white 100%);
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.it-nw-service-lower-wrapper {
  padding-top: 55px;
}

/*---------------------------------------------------- */
/*(5)- CTA area*/
/*----------------------------------------------------*/
.it-nw-cta-section {
  padding: 65px 0px;
}

.it-nw-cta-text h2 {
  color: #fff;
  font-size: 36px;
  font-weight: 700;
  max-width: 495px;
  line-height: 1.333;
}

.it-nw-cta-info .it-nw-cta-icon {
  margin-right: 20px;
}
.it-nw-cta-info .it-nw-cta-icon i {
  color: #fff;
  font-size: 24px;
}
.it-nw-cta-info .it-nw-cta-text {
  color: #fff;
}
.it-nw-cta-info .it-nw-cta-text span {
  font-size: 18px;
  font-weight: 500;
}
.it-nw-cta-info .it-nw-cta-text h3 {
  font-size: 18px;
  font-weight: 700;
}

.it-nw-cta-info-btn .it-nw-cta-arrow {
  top: -5px;
  left: -75px;
}
.it-nw-cta-info-btn .it-nw-btn {
  margin-left: 80px;
}

/*---------------------------------------------------- */
/*(6)- Project area*/
/*----------------------------------------------------*/
.it-nw-project-section {
  padding: 95px 0px 100px;
}
.it-nw-project-section .it-nw-section-title.middle-align {
  max-width: 630px;
}
.it-nw-project-section .it-nw-project-content {
  padding-top: 50px;
}
.it-nw-project-section .it-nw-project-content .it-nw-btn {
  margin-top: 50px;
}
.it-nw-project-section .it-nw-project-content .it-nw-btn a {
  margin: 0 auto;
}

.it-nw-project-innerbox {
  overflow: hidden;
  border-radius: 8px;
}
.it-nw-project-innerbox:before {
  left: 0;
  opacity: 0;
  width: 100%;
  content: "";
  z-index: 1;
  height: 100%;
  bottom: -20px;
  visibility: hidden;
  position: absolute;
  background-size: cover;
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
  background-image: url(../img/its-2/ps.png);
}
.it-nw-project-innerbox .it-nw-project-inner-img img {
  width: 100%;
}
.it-nw-project-innerbox .it-nw-project-inner-text {
  left: 0px;
  z-index: 2;
  opacity: 0;
  color: #fff;
  bottom: 40px;
  visibility: hidden;
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
  -webkit-transition-delay: 0.5s;
          transition-delay: 0.5s;
}
.it-nw-project-innerbox .it-nw-project-inner-text span {
  font-weight: 500;
  position: relative;
}
.it-nw-project-innerbox .it-nw-project-inner-text span:after {
  top: 16px;
  content: "";
  width: 65px;
  right: -72px;
  height: 2px;
  position: absolute;
  background-color: #fff;
}
.it-nw-project-innerbox .it-nw-project-inner-text h3 {
  font-size: 20px;
  padding-top: 5px;
  font-weight: 700;
}
.it-nw-project-innerbox .it-nw-project-link {
  z-index: 2;
  opacity: 0;
  right: 0px;
  bottom: 35px;
  visibility: hidden;
  position: absolute;
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
  -webkit-transition-delay: 0.7s;
          transition-delay: 0.7s;
}
.it-nw-project-innerbox .it-nw-project-link a {
  width: 60px;
  height: 60px;
  border-radius: 100%;
  background: linear-gradient(30deg, #7f31ff 0%, #4279f1 100%);
}
.it-nw-project-innerbox .it-nw-project-link a i {
  color: #fff;
  font-size: 26px;
}

.it-nw-project-slider-area .owl-next,
.it-nw-project-slider-area .owl-prev {
  top: 50%;
  color: #fff;
  width: 60px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  position: absolute;
  border-radius: 100%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  background-color: #00133e;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
}
.it-nw-project-slider-area .owl-next:hover,
.it-nw-project-slider-area .owl-prev:hover {
  background-color: #4c6df3;
}
.it-nw-project-slider-area .owl-prev {
  left: 20.5%;
}
.it-nw-project-slider-area .owl-next {
  right: 20.5%;
}
.it-nw-project-slider-area .owl-item.active.center .it-nw-project-innerbox:before {
  bottom: 0;
  opacity: 1;
  visibility: visible;
}
.it-nw-project-slider-area .owl-item.active.center .it-nw-project-innerbox .it-nw-project-inner-text {
  opacity: 1;
  left: 35px;
  visibility: visible;
}
.it-nw-project-slider-area .owl-item.active.center .it-nw-project-innerbox .it-nw-project-link {
  right: 35px;
  opacity: 1;
  visibility: visible;
}

/*---------------------------------------------------- */
/*(7)- Why Choose area*/
/*----------------------------------------------------*/
@-webkit-keyframes border_animation {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }
  20% {
    -webkit-transform: scale(1.24);
    transform: scale(1.24);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(2.1);
    transform: scale(2.1);
    opacity: 0;
  }
}
@keyframes border_animation {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }
  20% {
    -webkit-transform: scale(1.24);
    transform: scale(1.24);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(2.1);
    transform: scale(2.1);
    opacity: 0;
  }
}
.it-nw-why-choose-section {
  padding: 100px 0px;
  background-color: #f5f6f9;
}
.it-nw-why-choose-section .it-nw-whc-shape {
  top: 0;
  right: 0;
  position: absolute;
}

.it-nw-why-choose-text .it-nw-section-title p {
  max-width: 480px;
}

.it-nw-why-choose-feature-list {
  padding: 15px;
  margin-bottom: 30px;
  background-color: #fff;
  -webkit-box-shadow: 0px 10px 50px 0px rgba(0, 27, 175, 0.1);
          box-shadow: 0px 10px 50px 0px rgba(0, 27, 175, 0.1);
}
.it-nw-why-choose-feature-list .it-nw-why-choose-feature-icon {
  margin-right: 28px;
}
.it-nw-why-choose-feature-list .it-nw-why-choose-feature-icon i {
  font-size: 50px;
  line-height: 0.7;
}
.it-nw-why-choose-feature-list .it-nw-why-choose-feature-text h3 {
  font-size: 18px;
  font-weight: 700;
  color: #00133e;
  padding-bottom: 5px;
}

.it-nw-why-choose-feature {
  padding: 30px 0px 20px;
}

.it-nx-wc-video-img {
  overflow: hidden;
  border-radius: 10px;
  border-top-right-radius: 0;
}

.it-nw-why-choose-video-play {
  top: 90px;
  left: 90px;
  width: 60px;
  color: #fff;
  height: 60px;
  line-height: 60px;
  text-align: center;
  border-radius: 100%;
  background: linear-gradient(30deg, #7f31ff 0%, #4279f1 100%);
}
.it-nw-why-choose-video-play .video_btn_border {
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  height: 100%;
  border-radius: 60px;
  position: absolute;
  -webkit-animation-play-state: running;
          animation-play-state: running;
  border: 10px solid #4c6df3;
  -webkit-animation: border_animation 3.9s linear 0s infinite;
          animation: border_animation 3.9s linear 0s infinite;
}
.it-nw-why-choose-video-play .video_btn_border.border_wrap-2 {
  -webkit-animation-delay: 1.3s;
          animation-delay: 1.3s;
}

/*---------------------------------------------------- */
/*(7)- Fun fact area*/
/*----------------------------------------------------*/
.it-nw-fun-fact-section {
  z-index: 1;
  overflow: visible;
}
.it-nw-fun-fact-section:before {
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  z-index: -1;
  height: 105px;
  position: absolute;
  background-color: #f5f6f9;
}

.it-nw-fun-fact-content {
  background-color: #fff;
  padding: 55px 30px 30px;
  -webkit-box-shadow: 0px 10px 60px 0px rgba(0, 27, 175, 0.1);
          box-shadow: 0px 10px 60px 0px rgba(0, 27, 175, 0.1);
}
.it-nw-fun-fact-content:after {
  left: 0;
  right: 0;
  width: 95%;
  content: "";
  height: 25px;
  bottom: -25px;
  margin: 0 auto;
  position: absolute;
  border-radius: 7px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  background: linear-gradient(5deg, #7f31ff 0%, #4279f1 100%);
}

.it-nw-fun-fact-innerbox .it-nw-fun-fact-icon {
  width: 80px;
  height: 80px;
  margin-right: 12px;
  background-color: #eaedfa;
}
.it-nw-fun-fact-innerbox .it-nw-fun-fact-icon i {
  font-size: 45px;
  line-height: 0.7;
}
.it-nw-fun-fact-innerbox .it-nw-fun-fact-text h3 {
  font-size: 36px;
  font-weight: 700;
  color: #00133e;
}
.it-nw-fun-fact-innerbox .it-nw-fun-fact-text p {
  font-weight: 600;
}

/*---------------------------------------------------- */
/*(8)- Team area*/
/*----------------------------------------------------*/
.it-nw-team-section {
  overflow: visible;
  padding: 110px 0px;
}
.it-nw-team-section .it-nw-team-shape {
  left: 0;
  top: -60px;
}

.it-nw-team-content {
  padding-top: 50px;
}

.it-nw-team-innerbox .it-nw-team-text {
  -webkit-box-shadow: 0px 15px 50px 0px rgba(0, 27, 175, 0.1);
          box-shadow: 0px 15px 50px 0px rgba(0, 27, 175, 0.1);
}
.it-nw-team-innerbox .it-nw-team-img {
  overflow: hidden;
  border-top-left-radius: 8px;
}
.it-nw-team-innerbox .it-nw-team-img .it-nw-tm-shape {
  left: 0;
  opacity: 0;
  bottom: -50px;
  visibility: hidden;
  -webkit-transition: 400ms all ease;
  transition: 400ms all ease;
}
.it-nw-team-innerbox .it-nw-team-social {
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
}
.it-nw-team-innerbox .it-nw-team-social a {
  width: 30px;
  height: 30px;
  margin: 0px 5px;
  line-height: 30px;
  text-align: center;
  border-radius: 100%;
  display: inline-block;
  background-color: #fff;
}
.it-nw-team-innerbox .it-nw-team-social a .fb {
  color: #16599b;
}
.it-nw-team-innerbox .it-nw-team-social a .tw {
  color: #03a9f4;
}
.it-nw-team-innerbox .it-nw-team-social a .dr {
  color: #ea4f8b;
}
.it-nw-team-innerbox .it-nw-team-text {
  padding: 30px;
  border-radius: 10px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.it-nw-team-innerbox .it-nw-team-text h3 {
  font-size: 22px;
  font-weight: 700;
  color: #00133e;
  padding-bottom: 5px;
}
.it-nw-team-innerbox .it-nw-team-text span {
  font-weight: 600;
  color: #00133e;
}
.it-nw-team-innerbox .it-nw-team-text .it-nw-tm-link a {
  left: 0;
  right: 0;
  width: 40px;
  color: #fff;
  height: 40px;
  opacity: 0;
  bottom: 0px;
  visibility: hidden;
  margin: 0 auto;
  position: absolute;
  border-radius: 100%;
}
.it-nw-team-innerbox .it-nw-team-text .it-nw-tm-link a:hover {
  -webkit-transform: rotate(130deg);
          transform: rotate(130deg);
}
.it-nw-team-innerbox:hover .it-nw-team-social {
  opacity: 1;
  bottom: 30px;
  visibility: visible;
}
.it-nw-team-innerbox:hover .it-nw-tm-shape {
  bottom: 0;
  opacity: 1;
  visibility: visible;
}
.it-nw-team-innerbox:hover .it-nw-tm-link a {
  opacity: 1;
  bottom: -20px;
  visibility: visible;
}

/*---------------------------------------------------- */
/*(8)- Testimonial area*/
/*----------------------------------------------------*/
.it-nw-testimonial-section {
  z-index: 1;
  overflow: visible;
  padding: 100px 0px 50px;
}
.it-nw-testimonial-section:before {
  top: 0;
  z-index: -1;
  left: 0;
  content: "";
  width: 100%;
  height: 460px;
  position: absolute;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url(../img/its-2/bg/tst-bg.jpg);
}
.it-nw-testimonial-section .it-nw-testi-shape {
  left: 0;
  bottom: -330px;
  z-index: 0;
  opacity: 0.07;
  width: 30%;
}
.it-nw-testimonial-section .it-nw-section-title .it-nw-title-tag {
  color: #fff;
}
.it-nw-testimonial-section .it-nw-section-title .it-nw-title-tag:before, .it-nw-testimonial-section .it-nw-section-title .it-nw-title-tag:after {
  background-color: #fff;
}
.it-nw-testimonial-section .it-nw-section-title h2 {
  padding-bottom: 25px;
}
.it-nw-testimonial-section .it-nw-section-title h2, .it-nw-testimonial-section .it-nw-section-title p {
  color: #fff;
}

.it-nx-testimonial-content {
  padding-top: 75px;
}

.it-nw-testimonial-innerbox {
  background-color: #fff;
  padding: 40px 30px 40px 40px;
  -webkit-box-shadow: 0px 15px 50px 0px rgba(0, 27, 175, 0.1);
          box-shadow: 0px 15px 50px 0px rgba(0, 27, 175, 0.1);
}
.it-nw-testimonial-innerbox .it-nw-testimonial-img-name {
  padding-left: 150px;
}
.it-nw-testimonial-innerbox .it-nw-testimonial-img {
  left: 40px;
  top: -40px;
  width: 120px;
  height: 130px;
  overflow: hidden;
  position: absolute;
  margin-right: 30px;
  -webkit-box-shadow: 0px 15px 50px 0px rgba(0, 27, 175, 0.15);
          box-shadow: 0px 15px 50px 0px rgba(0, 27, 175, 0.15);
}
.it-nw-testimonial-innerbox .it-nw-testimonial-name h3 {
  font-size: 24px;
  font-weight: 700;
  color: #00133e;
}
.it-nw-testimonial-innerbox .it-nw-testimonial-rate {
  color: #ff8b17;
  font-weight: 600;
  padding-top: 15px;
}
.it-nw-testimonial-innerbox .it-nw-testimonial-rate i {
  margin-right: 5px;
  -webkit-transform: rotate(10deg);
          transform: rotate(10deg);
}
.it-nw-testimonial-innerbox .it-nw-testimonial-text {
  margin-top: 26px;
  color: #00133e;
}

.it-nx-testimonial-slider .owl-stage-outer,
.it-nw-blog-slider .owl-stage-outer {
  overflow: visible;
}
.it-nx-testimonial-slider .owl-item,
.it-nw-blog-slider .owl-item {
  opacity: 0;
  -webkit-transition: opacity 500ms;
  transition: opacity 500ms;
}
.it-nx-testimonial-slider .owl-item.active,
.it-nw-blog-slider .owl-item.active {
  opacity: 1;
}
.it-nx-testimonial-slider .owl-dots,
.it-nw-blog-slider .owl-dots {
  margin-top: 45px;
  text-align: center;
}
.it-nx-testimonial-slider .owl-dots .owl-dot,
.it-nw-blog-slider .owl-dots .owl-dot {
  width: 8px;
  height: 8px;
  margin: 0px 6px;
  border-radius: 30px;
  display: inline-block;
  border-top-right-radius: 0;
  background-color: #00133e;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
}
.it-nx-testimonial-slider .owl-dots .owl-dot.active,
.it-nw-blog-slider .owl-dots .owl-dot.active {
  width: 35px;
  background-color: #4c6df3;
}

/*---------------------------------------------------- */
/*(9)- Faq area*/
/*----------------------------------------------------*/
.it-nw-faq-section {
  z-index: 1;
}
.it-nw-faq-section .it-nw-faq-sh {
  right: 0;
  bottom: 0;
  z-index: -1;
}

.it-nw-faq-content {
  padding: 50px 50px 0px;
  background-color: #fff;
}
.it-nw-faq-content .accordion {
  border-top: 1px solid #d8d8d8;
}
.it-nw-faq-content .faq-header h3 {
  margin-bottom: 0;
}
.it-nw-faq-content .faq-body {
  padding: 15px 0px 0px 0px;
}
.it-nw-faq-content .faq_title:not(.collapsed),
.it-nw-faq-content .faq_title {
  color: #000;
  font-size: 15px;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-weight: 600;
  padding: 15px 0px;
  font-family: "Poppins";
  position: relative;
  border: 1px solid #d8d8d8;
  text-align: left;
  border-right: 0;
  border-left: 0;
  border-top: 0;
  width: 100%;
  background-color: transparent;
}
.it-nw-faq-content .accordion-item:first-of-type {
  border-radius: 0;
}
.it-nw-faq-content .accordion-item {
  border: none;
}
.it-nw-faq-content .faq_title::after {
  right: 0;
  width: auto;
  height: auto;
  content: "";
  color: #b1b0b0;
  font-weight: 900;
  position: absolute;
  background-image: none;
  font-family: "Font Awesome 5 Free";
}
.it-nw-faq-content .faq_title:not(.collapsed)::after {
  -webkit-transform: rotate(0);
          transform: rotate(0);
  content: "";
  color: #4c6df3;
}
.it-nw-faq-content .accordion-body {
  padding: 20px 0px;
}
.it-nw-faq-content .accordion-body a {
  color: #000;
  font-weight: 600;
}

/*---------------------------------------------------- */
/*(10)- Blog area*/
/*----------------------------------------------------*/
.it-nw-blog-section {
  z-index: 1;
  margin-bottom: 100px;
  padding: 95px 0px 110px;
}
.it-nw-blog-section .it-nw-section-title {
  max-width: 580px;
}
.it-nw-blog-section .it-nw-blog-sh {
  left: 0;
  z-index: -1;
  bottom: 100px;
}

.it-nw-blog-top-wrap .it-nw-btn {
  padding-top: 70px;
}

.it-nw-blog-content {
  padding-top: 45px;
}

.it-nw-blog-innerbox {
  background-color: #fff;
  -webkit-box-shadow: 0px 10px 45px 0px rgba(0, 27, 175, 0.1);
          box-shadow: 0px 10px 45px 0px rgba(0, 27, 175, 0.1);
}
.it-nw-blog-innerbox .it-nw-blog-inner-img {
  border-top-left-radius: 8px;
}
.it-nw-blog-innerbox .it-nw-blog-inner-text {
  padding: 30px 25px 40px;
}
.it-nw-blog-innerbox .it-nw-blog-inner-text .it-nw-blog-meta {
  padding-bottom: 25px;
}
.it-nw-blog-innerbox .it-nw-blog-inner-text .it-nw-blog-meta a {
  color: #00184d;
  font-size: 15px;
  font-weight: 600;
}
.it-nw-blog-innerbox .it-nw-blog-inner-text .it-nw-blog-meta a i {
  margin-right: 5px;
}
.it-nw-blog-innerbox .it-nw-blog-inner-text h3 {
  font-size: 24px;
  font-weight: 700;
  color: #00133e;
  padding-bottom: 18px;
}
.it-nw-blog-innerbox .it-nw-blog-inner-text h3:hover {
  color: #4c6df3;
}
.it-nw-blog-innerbox .it-nw-blog-inner-text .blog-more {
  font-size: 15px;
  font-weight: 600;
  color: #00133e;
  font-family: "Poppins";
}
.it-nw-blog-innerbox .it-nw-blog-inner-text .blog-more i {
  margin-left: 5px;
  -webkit-transition: 0.3s all ease;
  transition: 0.3s all ease;
}
.it-nw-blog-innerbox .it-nw-blog-inner-text .blog-more:hover {
  color: #4c6df3;
}
.it-nw-blog-innerbox .it-nw-blog-inner-text .blog-more:hover i {
  margin-left: 8px;
}

/*---------------------------------------------------- */
/*(11)- Footer area*/
/*----------------------------------------------------*/
.it-nw-footer-section {
  z-index: 2;
  padding-top: 185px;
}

.it-nw-newslatter-area {
  left: 0;
  right: 0;
  top: -90px;
  z-index: 2;
  width: 100%;
  margin: 0 auto;
  max-width: 1170px;
  border-radius: 8px;
  position: absolute;
  padding: 45px 15px 50px;
  background-color: #fff;
  border-bottom-right-radius: 0;
  -webkit-box-shadow: 0px 0px 50px 0px rgba(0, 27, 175, 0.15);
          box-shadow: 0px 0px 50px 0px rgba(0, 27, 175, 0.15);
}
.it-nw-newslatter-area:before {
  left: 0;
  right: 0;
  width: 95%;
  content: "";
  top: -25px;
  height: 25px;
  margin: 0 auto;
  position: absolute;
  border-radius: 7px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background: linear-gradient(5deg, #7f31ff 0%, #4279f1 100%);
}

.it-nw-newslatter-content {
  width: 100%;
}

.it-nw-newslatter-text h3 {
  color: #00184d;
  font-size: 36px;
  font-weight: 700;
  padding-bottom: 8px;
}
.it-nw-newslatter-text span {
  color: #00184d;
}

.it-nw-newslatter-form {
  width: 530px;
}
.it-nw-newslatter-form input {
  width: 100%;
  height: 50px;
  border-radius: 5px;
  padding-left: 20px;
  border: 1px solid #e7e7e7;
}
.it-nw-newslatter-form button {
  top: 0;
  right: 0;
  border: none;
  height: 50px;
  width: 185px;
  position: absolute;
}

.it-nw-fooer-widget-area {
  padding-bottom: 85px;
}
.it-nw-fooer-widget-area .widget-title {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  position: relative;
  padding-bottom: 15px;
  margin-bottom: 30px !important;
}
.it-nw-fooer-widget-area .widget-title:after {
  left: 0;
  bottom: 0;
  height: 3px;
  width: 33px;
  content: "";
  position: absolute;
}
.it-nw-fooer-widget-area .it-nw-logo-widget a {
  display: block;
  margin-bottom: 30px;
}
.it-nw-fooer-widget-area .it-nw-logo-widget p {
  color: #fff;
}
.it-nw-fooer-widget-area .it-nw-fooer-menu li {
  position: relative;
  padding-left: 18px;
  margin-bottom: 12px;
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
}
.it-nw-fooer-widget-area .it-nw-fooer-menu li:before {
  left: 0;
  top: 2px;
  font-size: 14px;
  content: "";
  position: absolute;
  color: #4c6df3;
  font-weight: 900;
  font-family: "Font Awesome 5 Free";
}
.it-nw-fooer-widget-area .it-nw-fooer-menu li a {
  color: #fff;
  position: relative;
}
.it-nw-fooer-widget-area .it-nw-fooer-menu li a:after {
  left: 0;
  bottom: -2px;
  content: "";
  width: 0%;
  height: 1px;
  position: absolute;
  -webkit-transition: 500ms all ease;
  transition: 500ms all ease;
  background-color: #4c6df3;
}
.it-nw-fooer-widget-area .it-nw-fooer-menu li:hover {
  margin-left: 8px;
}
.it-nw-fooer-widget-area .it-nw-fooer-menu li:hover a {
  color: #4c6df3;
}
.it-nw-fooer-widget-area .it-nw-fooer-menu li:hover a:after {
  width: 100%;
}
.it-nw-fooer-widget-area .it-nw-contact-widget {
  max-width: 245px;
}
.it-nw-fooer-widget-area .it-nw-contact-widget li {
  color: #fff;
  margin-bottom: 15px;
}
.it-nw-fooer-widget-area .it-nw-contact-widget li i {
  float: left;
  color: #4c6df3;
  padding-top: 3px;
  margin-right: 12px;
}
.it-nw-fooer-widget-area .it-nw-contact-widget li span {
  display: block;
  overflow: hidden;
}

.it-nw-copyright-wrap {
  color: #fff;
  padding: 15px 40px;
  background: linear-gradient(5deg, #7f31ff 0%, #4279f1 100%);
}

.it-nw-copyright-menu li {
  margin-left: 35px;
  position: relative;
}
.it-nw-copyright-menu li a:hover {
  text-decoration: underline;
}
.it-nw-copyright-menu li:after {
  top: 5px;
  width: 1px;
  content: "";
  right: -18px;
  height: 15px;
  position: absolute;
  background-color: #fff;
}
.it-nw-copyright-menu li:last-child:after {
  display: none;
}

/*---------------------------------------------------- */
/*(12)- Responsive area*/
/*----------------------------------------------------*/
@media screen and (max-width: 1280px) {
  .it-nw-testimonial-section {
    overflow: hidden;
  }
}
@media screen and (max-width: 1170px) {
  .it-nw-banner-img {
    bottom: 0;
    width: 60%;
  }
}
@media screen and (max-width: 1024px) {
  .it-nw-main-navigation li {
    margin-left: 30px;
  }

  .it-nw-section-title h2 {
    font-size: 32px;
  }

  .it-nw-about-tab-btn li a {
    padding: 15px 18px;
  }

  .it-nw-about-bottom .it-nw-about-arrow,
.it-nw-cta-info-btn .it-nw-cta-arrow {
    display: none;
  }

  .it-nw-about-bottom .it-nw-btn {
    margin-right: 25px;
  }
  .it-nw-about-bottom .it-nw-btn a {
    height: 50px;
  }

  .it-nw-cta-info-btn .it-nw-btn {
    margin-left: 30px;
  }

  .it-nw-cta-text h2 {
    font-size: 32px;
  }

  .it-nw-project-slider-area .owl-next, .it-nw-project-slider-area .owl-prev {
    width: 45px;
    height: 45px;
    line-height: 45px;
  }

  .it-nw-faq-content {
    padding: 50px 0px 95px;
  }

  .it-nw-blog-innerbox {
    margin: 0 auto;
    max-width: 570px;
  }
}
@media screen and (max-width: 991px) {
  .it-nw-header-area .it-nw-header-top-content {
    display: none !important;
  }

  .it-nw-header-main .navbar-nav {
    display: none;
  }

  .it-nw-header-main .it-nw-header-logo {
    width: 120px;
    padding-left: 0;
    padding-top: 8px;
  }

  .it-nw-header-main {
    padding: 15px 10px;
  }

  .it-nw-header-sticky {
    top: 0;
  }

  .it-nw-banner-img {
    display: none;
  }

  .it-nw-banner-item {
    padding: 220px 0px 135px;
  }

  .it-nw-feature-innerbox {
    margin-bottom: 70px;
  }

  .it-nw-about-text-wrapper,
.it-nw-why-choose-video {
    margin: 0 auto;
    margin-top: 40px;
    max-width: 570px;
  }

  .it-nw-about-img-wrapper,
.it-nw-why-choose-text {
    margin: 0 auto;
    max-width: 570px;
  }

  .it-nw-feature-section {
    padding-bottom: 20px;
  }

  .it-nw-service-innerbox {
    margin-top: 70px;
  }

  .it-nw-fun-fact-innerbox {
    margin-bottom: 20px;
  }

  .it-nw-team-innerbox {
    margin: 0 auto;
    max-width: 270px;
    margin-bottom: 30px;
  }

  .it-nw-fooer-widget {
    margin-bottom: 30px;
  }

  .it_nw_mobile_menu_button {
    display: block;
  }

  .it-nw-header-main .it-nw-btn {
    margin-right: 40px;
  }

  .it_nw_mobile_menu_button {
    top: -58px;
  }
}
@media screen and (max-width: 920px) {
  .it-nw-cta-content,
.it-nw-newslatter-content {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }

  .it-nw-cta-info-btn {
    margin-top: 30px;
  }

  .it-nw-project-innerbox .it-nw-project-link a {
    width: 40px;
    height: 40px;
  }
  .it-nw-project-innerbox .it-nw-project-link a i {
    font-size: 18px;
  }

  .it-nw-newslatter-form {
    width: 100%;
    margin-top: 30px;
  }

  .it-nw-newslatter-area {
    position: static;
    margin-bottom: 40px;
  }

  .it-nw-footer-section {
    padding-top: 80px;
  }

  .it-nw-newslatter-area:before {
    display: none;
  }

  .it-nw-blog-section {
    padding-bottom: 80px;
  }

  .it-nw-service-lower-wrapper {
    padding-top: 0;
  }
}
@media screen and (max-width: 680px) {
  .it-nw-blog-top-wrap {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }

  .it-nw-blog-top-wrap .it-nw-btn {
    padding-top: 30px;
  }
}
@media screen and (max-width: 580px) {
  .it-nw-banner-text h1 {
    font-size: 55px;
  }
}
@media screen and (max-width: 480px) {
  .it-nw-banner-text h1 {
    font-size: 38px;
  }

  .it-nw-feature-section {
    padding: 60px 0px 10px;
  }

  .it-nw-section-title h2 {
    font-size: 26px;
  }

  .it-nw-about-tab-btn li {
    margin-right: 10px;
  }

  .it-nw-about-tab-btn li a {
    font-size: 14px;
    padding: 8px 10px;
  }

  .it-nw-about-tab-btn {
    margin: 20px 0px 20px;
  }

  .it-nw-about-bottom,
.it-nw-cta-info-btn {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }

  .it-nw-about-cta {
    margin-top: 20px;
  }

  .it-nw-about-section {
    padding-bottom: 40px;
  }

  .it-nw-service-section,
.it-nw-project-section,
.it-nw-why-choose-section,
.it-nw-team-section {
    padding: 60px 0px;
  }

  .it-nw-section-title .it-nw-title-tag:after,
.it-nw-section-title.middle-align .it-nw-title-tag:before {
    display: none;
  }

  .it-nw-cta-info {
    margin-right: 30px;
  }

  .it-nw-cta-info-btn .it-nw-btn {
    margin-left: 0;
    margin-top: 20px;
  }

  .it-nw-project-slider-area .owl-nav {
    margin-top: 30px;
    text-align: center;
  }

  .it-nw-project-slider-area .owl-next, .it-nw-project-slider-area .owl-prev {
    position: static;
    margin: 0px 5px;
    display: inline-block;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }

  .it-nw-project-section .it-nw-project-content .it-nw-btn {
    margin-top: 30px;
  }

  .it-nw-testimonial-innerbox {
    padding: 30px 15px 30px 15px;
  }

  .it-nw-testimonial-innerbox .it-nw-testimonial-img {
    height: 80px;
    width: 80px;
    position: static;
    margin-right: 15px;
  }

  .it-nw-testimonial-innerbox .it-nw-testimonial-img-name {
    padding-left: 0;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }

  .it-nw-testimonial-innerbox .it-nw-testimonial-name h3 {
    font-size: 16px;
  }

  .it-nw-testimonial-innerbox .it-nw-testimonial-text {
    margin-top: 15px;
  }

  .it-nw-faq-content {
    padding-bottom: 40px;
  }

  .it-nw-faq-content .faq_title::after {
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
  }

  .it-nw-blog-innerbox .it-nw-blog-inner-text h3 {
    font-size: 20px;
  }

  .it-nw-newslatter-text h3 {
    font-size: 26px;
  }

  .it-nw-newslatter-form .it-nw-btn button i {
    display: none;
  }
  .it-nw-newslatter-form .it-nw-btn button {
    width: 140px;
  }

  .it-nw-fooer-widget-area {
    padding-bottom: 30px;
  }

  .it-nw-why-choose-video-play {
    top: 50px;
    left: 50px;
  }

  .it-nw-fun-fact-innerbox .it-nw-fun-fact-text h3 {
    font-size: 28px;
  }

  .it-nw-team-innerbox:hover .it-nw-tm-link a {
    bottom: 10px;
  }

  .it-nw-section-title .it-nw-title-tag {
    font-size: 16px;
  }

  .scrollup {
    width: 30px;
    right: 10px;
    height: 30px;
    line-height: 30px;
  }

  .it-nw-header-main .it-nw-btn {
    margin-left: 20px;
  }
  .it-nw-header-main .it-nw-btn a {
    width: 125px;
    height: 40px;
  }
  .it-nw-header-main .it-nw-btn a i {
    display: none;
  }

  .it_nw_mobile_menu_button {
    top: -52px;
  }

  .it-nw-header-main .it-nw-header-logo {
    padding-top: 3px;
  }

  .it-nw-about-img-2 {
    margin-left: 10px;
  }

  .it-nw-btn a, .it-nw-btn button {
    height: 50px;
    width: 150px;
  }

  .it-nw-copyright-wrap {
    padding: 15px;
  }
  .it-nw-about-section {
    padding-top: 0px;
  }
  .it-nw-about-tab-btn .nav-tabs .nav-item {
    margin-bottom: 10px;
  }
  .it-nw-blog-section {
    padding-top: 0;
    margin-bottom: 0
  }
}
@media screen and (max-width: 420px) {
  .it-nw-banner-text h1 {
    font-size: 34px;
  }

  .it-nw-cta-text h2 {
    font-size: 28px;
  }

  .it-nw-faq-content .faq_title::after {
    font-size: 10px;
  }
}
/*---------------------------------------------------- */