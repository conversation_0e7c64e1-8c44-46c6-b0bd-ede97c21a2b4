//  =================
//      Imports
//  =================

@import '../../base/base';    // Base Variables

.tags-input-wrapper {
  background: transparent;
  padding: 10px;
  border-radius: 4px;

  input {
    width: 150px;
    display: block;
    font-weight: 400;
    line-height: 1.5;
    background-clip: padding-box;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    height: auto;
    border: 1px solid $m-color_1;
    color: $dark;
    font-size: 15px;
    padding: 8px 10px;
    letter-spacing: 1px;
    background-color: $m-color_1;
  }

  .tag {
    display: inline-block;
    background-color: $primary;
    color: $white;
    font-size: 13px;
    border-radius: 4px;
    padding: 4px 3px 3px 7px;
    margin-right: 15px;
    margin-bottom: 7px;
    box-shadow: 0 5px 15px -2px rgba(43, 80, 237, 0.35);

    a {
      margin: 0 7px 3px;
      display: inline-block;
      cursor: pointer;
    }
  }
}