/*

ISBL Editor style dark color scheme (c) <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

*/

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #404040;
  color: #f0f0f0;
}

/* Base color: saturation 0; */

.hljs,
.hljs-subst {
  color: #f0f0f0;
}

.hljs-comment {
  color: #b5b5b5;
  font-style: italic;
}

.hljs-keyword,
.hljs-attribute,
.hljs-selector-tag,
.hljs-meta-keyword,
.hljs-doctag,
.hljs-name {
  color: #f0f0f0;
  font-weight: bold;
}


/* User color: hue: 0 */

.hljs-string {
  color: #97bf0d;
}

.hljs-type,
.hljs-number,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-deletion {
  color: #f0f0f0;
}

.hljs-title,
.hljs-section {
  color: #df471e;
}

.hljs-title>.hljs-built_in {
  color: #81bce9;
  font-weight: normal;
}

.hljs-regexp,
.hljs-symbol,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-selector-pseudo {
  color: #e2c696;
}

/* Language color: hue: 90; */

.hljs-built_in,
.hljs-literal {
  color: #97bf0d;
  font-weight: bold;
}

.hljs-bullet,
.hljs-code,
.hljs-addition {
  color: #397300;
}

.hljs-class  {
  color: #ce9d4d;
  font-weight: bold;
}

/* Meta color: hue: 200 */

.hljs-meta {
  color: #1f7199;
}

.hljs-meta-string {
  color: #4d99bf;
}


/* Misc effects */

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}
