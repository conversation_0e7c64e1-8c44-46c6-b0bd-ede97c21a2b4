//  =================
//      Imports
//  =================

@import '../../../base/base';    // Base Variables

/*
    Tab Section
*/

.faq .faq-layouting .fq-tab-section {
  padding: 35px 50px;
  background: $white;
  border-radius: 6px;
  -webkit-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.0901960784), 0 1px 20px 0 rgba(0, 0, 0, 0.08), 0px 1px 11px 0px rgba(0, 0, 0, 0.06);
  -moz-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.0901960784), 0 1px 20px 0 rgba(0, 0, 0, 0.08), 0px 1px 11px 0px rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.0901960784), 0 1px 20px 0 rgba(0, 0, 0, 0.08), 0px 1px 11px 0px rgba(0, 0, 0, 0.06);

  h2 {
    font-size: 25px;
    font-weight: 700;
    margin-bottom: 45px;
    letter-spacing: 0px;
    text-align: center;

    span {
      color: $primary;
    }
  }

  .accordion .card {
    border: 2px solid $m-color_3;
    border-radius: 6px;
    margin-bottom: 4px;

    .card-header {
      padding: 0;
      border: none;
      background: none;

      > div {
        padding: 19px 30px;
        font-weight: 600;
        font-size: 16px;
        color: $primary;
        cursor: pointer;

        &[aria-expanded="true"] {
          border-bottom: 2px solid $m-color_3;
        }
      }

      div {
        .faq-q-title {
          overflow: hidden;
          font-size: 14px;
          color: $m-color_6;
          font-weight: 600;
          letter-spacing: 1px;
        }

        &[aria-expanded="true"] .faq-q-title {
          color: $primary;
        }

        .icons {
          display: inline-block;
          float: right;

          svg {
            color: $m-color_6;
          }
        }

        &[aria-expanded="true"] .icons svg {
          color: $primary;
        }

        svg.feather-thumbs-up {
          cursor: pointer;
          vertical-align: bottom;
          margin-right: 10px;
          width: 18px;
          color: $m-color_6;
          fill: rgba(0, 23, 55, 0.08);
        }
      }
    }

    &:hover .card-header div svg.feather-thumbs-up {
      color: $primary;
      fill: rgba(27, 85, 226, 0.2392156863);
    }

    .card-header div {
      &[aria-expanded="true"] svg.feather-thumbs-up {
        color: $primary;
        fill: rgba(27, 85, 226, 0.2392156863);
      }

      span.faq-like-count {
        font-size: 13px;
        font-weight: 700;
        color: $m-color_6;
        fill: rgba(0, 23, 55, 0.08);
      }
    }

    &:hover .card-header div span.faq-like-count, .card-header div[aria-expanded="true"] span.faq-like-count {
      color: $primary;
      fill: rgba(27, 85, 226, 0.2392156863);
    }

    .collapse {}

    .card-body {
      padding: 19px 30px;

      p {
        font-size: 13px;
        line-height: 23px;
        letter-spacing: 1px;
      }
    }
  }
}

/*
    Media Query
*/
@media (max-width: 575px) {
  .faq .faq-layouting .fq-tab-section {
    padding: 35px 25px;

    .accordion .card .card-header div svg.feather-code {
      display: none;
    }
  }
}