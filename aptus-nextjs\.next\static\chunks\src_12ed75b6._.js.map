{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaPhone, FaBars, FaTimes } from 'react-icons/fa';\n\nexport default function Navbar() {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const toggleMenu = () => setIsOpen(!isOpen);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n    setIsOpen(false);\n  };\n\n  return (\n    <nav className=\"bg-white shadow-lg border-t-4 border-blue-600 sticky top-0 z-40\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"bg-blue-600 text-white px-6 py-3 -my-3\">\n            <h2 className=\"text-xl font-bold\">Aptus Group</h2>\n          </div>\n\n          {/* Desktop Menu */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            <button\n              onClick={() => scrollToSection('home')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Home\n            </button>\n            <button\n              onClick={() => scrollToSection('about')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              About\n            </button>\n            <button\n              onClick={() => scrollToSection('services')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Services\n            </button>\n            <button\n              onClick={() => scrollToSection('quote')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Get Free Quote\n            </button>\n            <button\n              onClick={() => scrollToSection('track')}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Track\n            </button>\n          </div>\n\n          {/* Phone Number */}\n          <div className=\"hidden lg:flex items-center text-gray-700\">\n            <FaPhone className=\"text-blue-600 mr-2\" />\n            <a href=\"tel:254107816884\" className=\"font-semibold hover:text-blue-600 transition-colors\">\n              +254 107 816 884\n            </a>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={toggleMenu}\n            className=\"md:hidden text-gray-700 hover:text-blue-600 transition-colors\"\n          >\n            {isOpen ? <FaTimes size={24} /> : <FaBars size={24} />}\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"md:hidden py-4 border-t\"\n          >\n            <div className=\"flex flex-col space-y-4\">\n              <button\n                onClick={() => scrollToSection('home')}\n                className=\"text-left text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                Home\n              </button>\n              <button\n                onClick={() => scrollToSection('about')}\n                className=\"text-left text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                About\n              </button>\n              <button\n                onClick={() => scrollToSection('services')}\n                className=\"text-left text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                Services\n              </button>\n              <button\n                onClick={() => scrollToSection('quote')}\n                className=\"text-left text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                Get Free Quote\n              </button>\n              <button\n                onClick={() => scrollToSection('track')}\n                className=\"text-left text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                Track\n              </button>\n              <div className=\"flex items-center text-gray-700 pt-2\">\n                <FaPhone className=\"text-blue-600 mr-2\" />\n                <a href=\"tel:254107816884\" className=\"font-semibold hover:text-blue-600 transition-colors\">\n                  +254 107 816 884\n                </a>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,IAAM,UAAU,CAAC;IAEpC,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAAoB;;;;;;;;;;;sCAIpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iJAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAE,MAAK;oCAAmB,WAAU;8CAAsD;;;;;;;;;;;;sCAM7F,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAET,uBAAS,6LAAC,iJAAA,CAAA,UAAO;gCAAC,MAAM;;;;;qDAAS,6LAAC,iJAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;gBAKnD,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iJAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAE,MAAK;wCAAmB,WAAU;kDAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3G;GA5HwB;KAAA", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/Hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\nconst slides = [\n  {\n    id: 1,\n    image: \"/images/carousel-1.jpg\",\n    title: \"Transport & Logistics\",\n    heading: \"Powering Trade through Logistics\",\n    description:\n      \"Our passion is what happens after the delivery. We deliver Hope, Opportunity, and Potential! Cargo Solutions and Global Networks that catalyze Growth.\",\n    buttonText: \"Free Quote\",\n    buttonAction: \"quote\",\n  },\n  {\n    id: 2,\n    image: \"/images/safe.jpg\",\n    title: \"Safe Deposit box & Warehousing\",\n    heading: \"Safe Depository for your Valuables\",\n    description:\n      \"Complete Peace of Mind. Your valuables are automatically Insured. Premium protection of your Valuables.\",\n    buttonText: \"Free Quote\",\n    buttonAction: \"quote\",\n  },\n];\n\nexport default function Hero() {\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentSlide((prev) => (prev + 1) % slides.length);\n    }, 5000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: \"smooth\" });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"relative h-screen overflow-hidden\">\n      <AnimatePresence mode=\"wait\">\n        <motion.div\n          key={currentSlide}\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ duration: 1 }}\n          className=\"absolute inset-0\"\n        >\n          <div\n            className=\"relative h-full bg-cover bg-center bg-no-repeat\"\n            style={{\n              backgroundImage: `url(${slides[currentSlide].image})`,\n            }}\n          >\n            {/* <div className=\"absolute inset-0 bg-black bg-opacity-50\" /> */}\n\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"container mx-auto px-4\">\n                <div className=\"max-w-4xl\">\n                  <motion.h5\n                    initial={{ y: 50, opacity: 0 }}\n                    animate={{ y: 0, opacity: 1 }}\n                    transition={{ delay: 0.2 }}\n                    className=\"text-white uppercase text-lg mb-4 tracking-wider\"\n                  >\n                    {slides[currentSlide].title}\n                  </motion.h5>\n\n                  <motion.h1\n                    initial={{ y: 50, opacity: 0 }}\n                    animate={{ y: 0, opacity: 1 }}\n                    transition={{ delay: 0.4 }}\n                    className=\"text-white text-4xl md:text-6xl font-bold mb-6 leading-tight\"\n                  >\n                    {slides[currentSlide].heading\n                      .split(\" \")\n                      .map((word, index) => (\n                        <span key={index}>\n                          {word === \"Trade\" ||\n                          word === \"Logistics\" ||\n                          word === \"Valuables\" ? (\n                            <span className=\"text-blue-400\">{word}</span>\n                          ) : (\n                            word\n                          )}{\" \"}\n                        </span>\n                      ))}\n                  </motion.h1>\n\n                  <motion.p\n                    initial={{ y: 50, opacity: 0 }}\n                    animate={{ y: 0, opacity: 1 }}\n                    transition={{ delay: 0.6 }}\n                    className=\"text-white text-lg md:text-xl mb-8 max-w-3xl leading-relaxed\"\n                  >\n                    {slides[currentSlide].description}\n                  </motion.p>\n\n                  <motion.button\n                    initial={{ y: 50, opacity: 0 }}\n                    animate={{ y: 0, opacity: 1 }}\n                    transition={{ delay: 0.8 }}\n                    onClick={() =>\n                      scrollToSection(slides[currentSlide].buttonAction)\n                    }\n                    className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg hover:shadow-xl\"\n                  >\n                    {slides[currentSlide].buttonText}\n                  </motion.button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </AnimatePresence>\n\n      {/* Slide Indicators */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2\">\n        {slides.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentSlide(index)}\n            className={`w-3 h-3 rounded-full transition-colors ${\n              index === currentSlide ? \"bg-white\" : \"bg-white bg-opacity-50\"\n            }`}\n          />\n        ))}\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAKA,MAAM,SAAS;IACb;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,aACE;QACF,YAAY;QACZ,cAAc;IAChB;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,aACE;QACF,YAAY;QACZ,cAAc;IAChB;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,QAAQ;wCAAY;oBACxB;gDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;;gBACtD;uCAAG;YAEH;kCAAO,IAAM,cAAc;;QAC7B;yBAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;;0BAC3B,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAE;oBAC1B,WAAU;8BAEV,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,AAAC,OAAiC,OAA3B,MAAM,CAAC,aAAa,CAAC,KAAK,EAAC;wBACrD;kCAIA,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;sDAET,MAAM,CAAC,aAAa,CAAC,KAAK;;;;;;sDAG7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;sDAET,MAAM,CAAC,aAAa,CAAC,OAAO,CAC1B,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,MAAM,sBACV,6LAAC;;wDACE,SAAS,WACV,SAAS,eACT,SAAS,4BACP,6LAAC;4DAAK,WAAU;sEAAiB;;;;;mEAEjC;wDACC;;mDAPM;;;;;;;;;;sDAYjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;sDAET,MAAM,CAAC,aAAa,CAAC,WAAW;;;;;;sDAGnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,SAAS;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAC5B,YAAY;gDAAE,OAAO;4CAAI;4CACzB,SAAS,IACP,gBAAgB,MAAM,CAAC,aAAa,CAAC,YAAY;4CAEnD,WAAU;sDAET,MAAM,CAAC,aAAa,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;mBAlErC;;;;;;;;;;0BA4ET,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,AAAC,0CAEX,OADC,UAAU,eAAe,aAAa;uBAHnC;;;;;;;;;;;;;;;;AAUjB;GA/GwB;KAAA", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/About.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { FaGlobe, FaShippingFast } from 'react-icons/fa';\n\nconst aboutSections = [\n  {\n    id: 1,\n    image: '/images/transport-and-logistics.jpg',\n    subtitle: 'About Aptus Group',\n    title: 'Cargo Logistics Solutions',\n    description: 'Africa presents great potential and more global markets now seek to tap into the possibilities present in the continent. Aptus Group is proud to contribute to this Africa reawakening by providing much-needed global cargo connections in East Africa, the Great Lakes region, and the rest of the world. We are Powering Trade for our customers through world-class transport and logistics services. We approach each shipment we handle and every ton we move as an opportunity to accelerate growth, unlock potential, deliver hope in our communities, and contribute to economic growth in the markets we serve. That\\'s how we are Powering Trade.',\n    features: [\n      {\n        icon: FaGlobe,\n        title: 'Transparent Affordable Pricing',\n        description: 'International supply chains involves a myriad of unknown risks and challenging.'\n      },\n      {\n        icon: FaShippingFast,\n        title: 'Real Time Tracking',\n        description: 'We ensure customers\\' supply chains are fully compliant by comprehensive practices.'\n      }\n    ]\n  },\n  {\n    id: 2,\n    image: '/images/Safe-Deposit-Box-Bank-Vault-Locker.jpg',\n    subtitle: 'Safe Depository',\n    title: 'Safe Depository Services',\n    description: 'Aptus Group Kenya, (part of Aptus Group Global Vaults) is a World\\'s First that has revolutionized the Safe Deposit Box industry. Using advanced Safe robotics, it is fully automated & insured. Secure your valuables today with the leading global Safe Deposit Brand.',\n    features: [\n      {\n        icon: FaGlobe,\n        title: 'Automated & Insured',\n        description: 'fully automated & discrete; you alone control access to privacy pod and personal Safe Deposit Box.'\n      },\n      {\n        icon: FaShippingFast,\n        title: 'Real Time Tracking',\n        description: 'You can track and access your Safe Depository Box at anytime from 8AM till late.'\n      }\n    ]\n  }\n];\n\nexport default function About() {\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-gray-50\">\n      {aboutSections.map((section, sectionIndex) => (\n        <div key={section.id} className={`container mx-auto px-4 ${sectionIndex > 0 ? 'mt-20' : ''}`}>\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            {/* Image */}\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"relative h-96 lg:h-[500px]\"\n            >\n              <Image\n                src={section.image}\n                alt={section.title}\n                fill\n                className=\"object-cover rounded-lg shadow-lg\"\n              />\n            </motion.div>\n\n            {/* Content */}\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"space-y-6\"\n            >\n              <h6 className=\"text-blue-600 uppercase text-sm font-semibold tracking-wider\">\n                {section.subtitle}\n              </h6>\n              \n              <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 leading-tight\">\n                {section.title}\n              </h2>\n              \n              <p className=\"text-gray-600 text-lg leading-relaxed\">\n                {section.description}\n              </p>\n\n              {/* Features */}\n              <div className=\"grid sm:grid-cols-2 gap-6 mt-8\">\n                {section.features.map((feature, index) => (\n                  <motion.div\n                    key={index}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.6, delay: 0.4 + index * 0.2 }}\n                    viewport={{ once: true }}\n                    className=\"space-y-3\"\n                  >\n                    <feature.icon className=\"text-blue-600 text-3xl\" />\n                    <h5 className=\"font-semibold text-gray-900\">{feature.title}</h5>\n                    <p className=\"text-gray-600 text-sm\">{feature.description}</p>\n                  </motion.div>\n                ))}\n              </div>\n\n              <motion.button\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.8 }}\n                viewport={{ once: true }}\n                onClick={() => scrollToSection('quote')}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-300 shadow-lg hover:shadow-xl\"\n              >\n                Get a Free Quote\n              </motion.button>\n            </motion.div>\n          </div>\n        </div>\n      ))}\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,gBAAgB;IACpB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,aAAa;QACb,UAAU;YACR;gBACE,MAAM,iJAAA,CAAA,UAAO;gBACb,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM,iJAAA,CAAA,iBAAc;gBACpB,OAAO;gBACP,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,OAAO;QACP,aAAa;QACb,UAAU;YACR;gBACE,MAAM,iJAAA,CAAA,UAAO;gBACb,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM,iJAAA,CAAA,iBAAc;gBACpB,OAAO;gBACP,aAAa;YACf;SACD;IACH;CACD;AAEc,SAAS;IACtB,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC3B,cAAc,GAAG,CAAC,CAAC,SAAS,6BAC3B,6LAAC;gBAAqB,WAAW,AAAC,0BAAyD,OAAhC,eAAe,IAAI,UAAU;0BACtF,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,KAAK;gCAClB,KAAK,QAAQ,KAAK;gCAClB,IAAI;gCACJ,WAAU;;;;;;;;;;;sCAKd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ;;;;;;8CAGnB,6LAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAGhB,6LAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAItB,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC,QAAQ,IAAI;oDAAC,WAAU;;;;;;8DACxB,6LAAC;oDAAG,WAAU;8DAA+B,QAAQ,KAAK;;;;;;8DAC1D,6LAAC;oDAAE,WAAU;8DAAyB,QAAQ,WAAW;;;;;;;2CATpD;;;;;;;;;;8CAcX,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;;;;;;;eA/DG,QAAQ,EAAE;;;;;;;;;;AAwE5B;KAnFwB", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/Stats.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaPhone, FaUsers, FaShip, FaStar } from 'react-icons/fa';\nimport { useEffect, useState } from 'react';\n\nconst stats = [\n  {\n    icon: FaUsers,\n    number: 200000,\n    label: 'Warehouse m²',\n    color: 'bg-blue-600'\n  },\n  {\n    icon: FaShip,\n    number: 1789,\n    label: 'Complete Shipments',\n    color: 'bg-gray-600'\n  },\n  {\n    icon: FaStar,\n    number: 434,\n    label: 'Trucking Fleet',\n    color: 'bg-green-600'\n  }\n];\n\nfunction CountUp({ end, duration = 2000 }: { end: number; duration?: number }) {\n  const [count, setCount] = useState(0);\n\n  useEffect(() => {\n    let startTime: number;\n    let animationFrame: number;\n\n    const animate = (timestamp: number) => {\n      if (!startTime) startTime = timestamp;\n      const progress = Math.min((timestamp - startTime) / duration, 1);\n      \n      setCount(Math.floor(progress * end));\n      \n      if (progress < 1) {\n        animationFrame = requestAnimationFrame(animate);\n      }\n    };\n\n    animationFrame = requestAnimationFrame(animate);\n\n    return () => {\n      if (animationFrame) {\n        cancelAnimationFrame(animationFrame);\n      }\n    };\n  }, [end, duration]);\n\n  return <span>{count.toLocaleString()}</span>;\n}\n\nexport default function Stats() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <h6 className=\"text-blue-600 uppercase text-sm font-semibold tracking-wider\">\n              Aptus Group\n            </h6>\n            \n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 leading-tight\">\n              #1 Place To Manage All Of Your Cargo Shipment and Logistics\n            </h2>\n            \n            <p className=\"text-gray-600 text-lg leading-relaxed\">\n              Aptus Group leverages Skilled personnel, communications, tracking processing software, \n              combined with decades of experience! Through an integrated supply chain solutions, \n              Aptus Group drives sustainable competitive advantages to some of World's largest companies.\n            </p>\n\n            {/* Contact Info */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              viewport={{ once: true }}\n              className=\"flex items-center space-x-4 bg-gray-50 p-4 rounded-lg\"\n            >\n              <div className=\"bg-blue-600 p-3 rounded-full\">\n                <FaPhone className=\"text-white text-xl\" />\n              </div>\n              <div>\n                <h6 className=\"font-semibold text-gray-900\">Call to learn more!</h6>\n                <a \n                  href=\"tel:254101631676\" \n                  className=\"text-blue-600 text-xl font-bold hover:text-blue-700 transition-colors\"\n                >\n                  +254 101 631 676\n                </a>\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Right Stats */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\">\n            {stats.map((stat, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.2 }}\n                viewport={{ once: true }}\n                className={`${stat.color} text-white p-6 rounded-lg text-center space-y-3 shadow-lg hover:shadow-xl transition-shadow duration-300`}\n              >\n                <stat.icon className=\"text-4xl mx-auto\" />\n                <h2 className=\"text-3xl font-bold\">\n                  <CountUp end={stat.number} />\n                </h2>\n                <p className=\"text-sm opacity-90\">{stat.label}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,QAAQ;IACZ;QACE,MAAM,iJAAA,CAAA,UAAO;QACb,QAAQ;QACR,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,iJAAA,CAAA,SAAM;QACZ,QAAQ;QACR,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,iJAAA,CAAA,SAAM;QACZ,QAAQ;QACR,OAAO;QACP,OAAO;IACT;CACD;AAED,SAAS,QAAQ,KAA4D;QAA5D,EAAE,GAAG,EAAE,WAAW,IAAI,EAAsC,GAA5D;;IACf,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI;YACJ,IAAI;YAEJ,MAAM;6CAAU,CAAC;oBACf,IAAI,CAAC,WAAW,YAAY;oBAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,YAAY,SAAS,IAAI,UAAU;oBAE9D,SAAS,KAAK,KAAK,CAAC,WAAW;oBAE/B,IAAI,WAAW,GAAG;wBAChB,iBAAiB,sBAAsB;oBACzC;gBACF;;YAEA,iBAAiB,sBAAsB;YAEvC;qCAAO;oBACL,IAAI,gBAAgB;wBAClB,qBAAqB;oBACvB;gBACF;;QACF;4BAAG;QAAC;QAAK;KAAS;IAElB,qBAAO,6LAAC;kBAAM,MAAM,cAAc;;;;;;AACpC;GA5BS;KAAA;AA8BM,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAI7E,6LAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAI3E,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAOrD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAW,AAAC,GAAa,OAAX,KAAK,KAAK,EAAC;;kDAEzB,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAQ,KAAK,KAAK,MAAM;;;;;;;;;;;kDAE3B,6LAAC;wCAAE,WAAU;kDAAsB,KAAK,KAAK;;;;;;;+BAXxC;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBrB;MAzEwB", "debugId": null}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/Services.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { FaArrowRight } from 'react-icons/fa';\n\nconst logisticsServices = [\n  {\n    id: 1,\n    image: '/images/service-1.jpg',\n    title: 'Air, Sea & Road Freight',\n    description: 'We can provide with the comprehensive service in the sphere of urgent, valuable, fragile or any cargoes conscientious accelerated delivery by air.'\n  },\n  {\n    id: 2,\n    image: '/images/service-2.jpg',\n    title: 'Customs Clearing',\n    description: 'We provides with the main types of basic conditions International sea transportation is implemented by our partners vessels, the largest ocean carriers.'\n  },\n  {\n    id: 3,\n    image: '/images/service-3.jpg',\n    title: 'Freight Consulting',\n    description: 'We provides a wide range of transportation services including quality international road transportation of cargoes & goods arriving from the ports all over the world.'\n  }\n];\n\nconst warehousingServices = [\n  {\n    id: 1,\n    image: '/images/service-3.jpg',\n    title: 'CFS (Clearing and Forwarding)',\n    description: 'Aptus Group CFS is a customs bonded terminal in Mombasa located 7.5 kilometers from the Port.'\n  },\n  {\n    id: 2,\n    image: '/images/service-4.jpg',\n    title: 'Warehousing Services',\n    description: 'We offer our customers modern, spacious and secure warehousing that meet your unique needs.'\n  },\n  {\n    id: 3,\n    image: '/images/service-5.jpg',\n    title: 'Safe Depository & Safekeeping',\n    description: 'Aptus Group uses advanced Safe robotics for full automation. Secure your valuables with a leading safe deposit brand.'\n  }\n];\n\ninterface ServiceCardProps {\n  service: {\n    id: number;\n    image: string;\n    title: string;\n    description: string;\n  };\n  index: number;\n}\n\nfunction ServiceCard({ service, index }: ServiceCardProps) {\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 50 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay: index * 0.1 }}\n      viewport={{ once: true }}\n      className=\"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\"\n    >\n      <div className=\"relative h-48 overflow-hidden\">\n        <Image\n          src={service.image}\n          alt={service.title}\n          fill\n          className=\"object-cover hover:scale-105 transition-transform duration-300\"\n        />\n      </div>\n      \n      <div className=\"p-6 space-y-4\">\n        <h4 className=\"text-xl font-semibold text-gray-900\">{service.title}</h4>\n        <p className=\"text-gray-600 leading-relaxed\">{service.description}</p>\n        \n        <button\n          onClick={() => scrollToSection('quote')}\n          className=\"group flex items-center text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-300\"\n        >\n          <FaArrowRight className=\"mr-2 group-hover:translate-x-1 transition-transform duration-300\" />\n          <span>Learn More</span>\n        </button>\n      </div>\n    </motion.div>\n  );\n}\n\nexport default function Services() {\n  return (\n    <section id=\"services\" className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Logistics Services */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h6 className=\"text-blue-600 uppercase text-sm font-semibold tracking-wider mb-2\">\n            Our Services\n          </h6>\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900\">\n            Aptus Group Logistics Services\n          </h2>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n          {logisticsServices.map((service, index) => (\n            <ServiceCard key={service.id} service={service} index={index} />\n          ))}\n        </div>\n\n        {/* Warehousing Services */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900\">\n            Aptus Group Warehousing Services\n          </h2>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {warehousingServices.map((service, index) => (\n            <ServiceCard key={service.id} service={service} index={index + 3} />\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,oBAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,sBAAsB;IAC1B;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAYD,SAAS,YAAY,KAAoC;QAApC,EAAE,OAAO,EAAE,KAAK,EAAoB,GAApC;IACnB,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,UAAU;YAAE,MAAM;QAAK;QACvB,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK,QAAQ,KAAK;oBAClB,KAAK,QAAQ,KAAK;oBAClB,IAAI;oBACJ,WAAU;;;;;;;;;;;0BAId,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuC,QAAQ,KAAK;;;;;;kCAClE,6LAAC;wBAAE,WAAU;kCAAiC,QAAQ,WAAW;;;;;;kCAEjE,6LAAC;wBACC,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;KAvCS;AAyCM,SAAS;IACtB,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,6LAAC;4BAAG,WAAU;sCAA+C;;;;;;;;;;;;8BAK/D,6LAAC;oBAAI,WAAU;8BACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC;4BAA6B,SAAS;4BAAS,OAAO;2BAArC,QAAQ,EAAE;;;;;;;;;;8BAKhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAG,WAAU;kCAA+C;;;;;;;;;;;8BAK/D,6LAAC;oBAAI,WAAU;8BACZ,oBAAoB,GAAG,CAAC,CAAC,SAAS,sBACjC,6LAAC;4BAA6B,SAAS;4BAAS,OAAO,QAAQ;2BAA7C,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AAMxC;MA/CwB", "debugId": null}}, {"offset": {"line": 1354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/QuoteForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaPhone } from 'react-icons/fa';\n\ninterface FormData {\n  senderInfo: string;\n  holdersEmail: string;\n  holdersTel: string;\n  type: string;\n  weight: string;\n  length: string;\n  width: string;\n  height: string;\n  originCity: string;\n  destinationCity: string;\n  details: string;\n}\n\nexport default function QuoteForm() {\n  const [formData, setFormData] = useState<FormData>({\n    senderInfo: '',\n    holdersEmail: '',\n    holdersTel: '',\n    type: '',\n    weight: '',\n    length: '',\n    width: '',\n    height: '',\n    originCity: '',\n    destinationCity: '',\n    details: ''\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    // Simulate API call\n    setTimeout(() => {\n      setIsSubmitting(false);\n      setIsSubmitted(true);\n      \n      // Reset form after 3 seconds\n      setTimeout(() => {\n        setIsSubmitted(false);\n        setFormData({\n          senderInfo: '',\n          holdersEmail: '',\n          holdersTel: '',\n          type: '',\n          weight: '',\n          length: '',\n          width: '',\n          height: '',\n          originCity: '',\n          destinationCity: '',\n          details: ''\n        });\n      }, 3000);\n    }, 2000);\n  };\n\n  if (isSubmitted) {\n    return (\n      <section id=\"quote\" className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"text-center py-20\"\n          >\n            <h2 className=\"text-3xl font-bold text-green-600 mb-4\">Quote Request Submitted!</h2>\n            <p className=\"text-gray-600 mb-6\">We will get back to you shortly.</p>\n            <button\n              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors\"\n            >\n              Back Home\n            </button>\n          </motion.div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section id=\"quote\" className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <h6 className=\"text-blue-600 uppercase text-sm font-semibold tracking-wider\">\n              Get A Quote\n            </h6>\n            \n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 leading-tight\">\n              Request A Free Quote!\n            </h2>\n            \n            <p className=\"text-gray-600 text-lg leading-relaxed\">\n              We deliver a seamless service for cargo we handle by air, land or sea through extensive \n              asset investments. We consistently benchmark our services with global standards of excellence.\n            </p>\n\n            {/* Contact Info */}\n            <div className=\"flex items-center space-x-4 bg-gray-50 p-4 rounded-lg\">\n              <div className=\"bg-blue-600 p-3 rounded-full\">\n                <FaPhone className=\"text-white text-xl\" />\n              </div>\n              <div>\n                <h6 className=\"font-semibold text-gray-900\">Call for any query!</h6>\n                <a \n                  href=\"tel:254101631676\" \n                  className=\"text-blue-600 text-xl font-bold hover:text-blue-700 transition-colors\"\n                >\n                  +254 101 631 676\n                </a>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Quote Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"bg-gray-50 p-8 rounded-lg shadow-lg\"\n          >\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"grid sm:grid-cols-2 gap-4\">\n                <input\n                  type=\"text\"\n                  name=\"senderInfo\"\n                  value={formData.senderInfo}\n                  onChange={handleInputChange}\n                  placeholder=\"Your Name\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <input\n                  type=\"email\"\n                  name=\"holdersEmail\"\n                  value={formData.holdersEmail}\n                  onChange={handleInputChange}\n                  placeholder=\"Your Email\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div className=\"grid sm:grid-cols-2 gap-4\">\n                <input\n                  type=\"tel\"\n                  name=\"holdersTel\"\n                  value={formData.holdersTel}\n                  onChange={handleInputChange}\n                  placeholder=\"Your Mobile\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <select\n                  name=\"type\"\n                  value={formData.type}\n                  onChange={handleInputChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                >\n                  <option value=\"\">Select A Freight</option>\n                  <option value=\"air\">Air</option>\n                  <option value=\"sea\">Sea</option>\n                  <option value=\"road\">Road</option>\n                  <option value=\"safe\">Safe Deposit/Warehousing</option>\n                </select>\n              </div>\n\n              <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4\">\n                <input\n                  type=\"number\"\n                  name=\"weight\"\n                  value={formData.weight}\n                  onChange={handleInputChange}\n                  placeholder=\"Weight (kg)\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <input\n                  type=\"number\"\n                  name=\"length\"\n                  value={formData.length}\n                  onChange={handleInputChange}\n                  placeholder=\"Length (cm)\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <input\n                  type=\"number\"\n                  name=\"width\"\n                  value={formData.width}\n                  onChange={handleInputChange}\n                  placeholder=\"Width (cm)\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <input\n                  type=\"number\"\n                  name=\"height\"\n                  value={formData.height}\n                  onChange={handleInputChange}\n                  placeholder=\"Height (cm)\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <div className=\"grid sm:grid-cols-2 gap-4\">\n                <input\n                  type=\"text\"\n                  name=\"originCity\"\n                  value={formData.originCity}\n                  onChange={handleInputChange}\n                  placeholder=\"City of Origin\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <input\n                  type=\"text\"\n                  name=\"destinationCity\"\n                  value={formData.destinationCity}\n                  onChange={handleInputChange}\n                  placeholder=\"Deliver City\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n\n              <textarea\n                name=\"details\"\n                value={formData.details}\n                onChange={handleInputChange}\n                placeholder=\"Special Note\"\n                required\n                rows={4}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n\n              <button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 rounded-lg font-semibold transition-colors duration-300 shadow-lg hover:shadow-xl\"\n              >\n                {isSubmitting ? 'Submitting...' : 'Request Quote'}\n              </button>\n            </form>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,YAAY;QACZ,cAAc;QACd,YAAY;QACZ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,iBAAiB;QACjB,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,oBAAoB;QACpB,WAAW;YACT,gBAAgB;YAChB,eAAe;YAEf,6BAA6B;YAC7B,WAAW;gBACT,eAAe;gBACf,YAAY;oBACV,YAAY;oBACZ,cAAc;oBACd,YAAY;oBACZ,MAAM;oBACN,QAAQ;oBACR,QAAQ;oBACR,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,iBAAiB;oBACjB,SAAS;gBACX;YACF,GAAG;QACL,GAAG;IACL;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YAAQ,IAAG;YAAQ,WAAU;sBAC5B,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,6LAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC;oCAAE,KAAK;oCAAG,UAAU;gCAAS;4BAC5D,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAI7E,6LAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAI3E,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAMrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAQP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,UAAU;4CAC1B,UAAU;4CACV,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,YAAY;4CAC5B,UAAU;4CACV,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,UAAU;4CAC1B,UAAU;4CACV,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,QAAQ;4CACR,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;8CAIzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,MAAM;4CACtB,UAAU;4CACV,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,MAAM;4CACtB,UAAU;4CACV,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,MAAM;4CACtB,UAAU;4CACV,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,UAAU;4CAC1B,UAAU;4CACV,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,eAAe;4CAC/B,UAAU;4CACV,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,aAAY;oCACZ,QAAQ;oCACR,MAAM;oCACN,WAAU;;;;;;8CAGZ,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;GAlQwB;KAAA", "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/TrackingForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\n\nexport default function TrackingForm() {\n  const [trackingCode, setTrackingCode] = useState('');\n  const [isTracking, setIsTracking] = useState(false);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!trackingCode.trim()) return;\n\n    setIsTracking(true);\n    \n    // Simulate tracking lookup\n    setTimeout(() => {\n      setIsTracking(false);\n      // In a real app, this would redirect to a tracking results page\n      alert(`Tracking shipment: ${trackingCode}`);\n      setTrackingCode('');\n    }, 2000);\n  };\n\n  return (\n    <section id=\"track\" className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content - Form */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 leading-tight\">\n              Track your Aptus Package\n            </h2>\n\n            <h6 className=\"text-blue-600 uppercase text-sm font-semibold tracking-wider\">\n              Real-time Tracking\n            </h6>\n\n            <div className=\"bg-white p-8 rounded-lg shadow-lg\">\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\n                <input\n                  type=\"text\"\n                  value={trackingCode}\n                  onChange={(e) => setTrackingCode(e.target.value)}\n                  placeholder=\"Enter Shipment No. eg. EY24K004\"\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg\"\n                />\n                \n                <button\n                  type=\"submit\"\n                  disabled={isTracking}\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-3 rounded-lg font-semibold text-lg transition-colors duration-300 shadow-lg hover:shadow-xl\"\n                >\n                  {isTracking ? 'Tracking...' : 'Track Shipment'}\n                </button>\n              </form>\n            </div>\n          </motion.div>\n\n          {/* Right Content - Description */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <p className=\"text-gray-600 text-lg leading-relaxed\">\n              Tracking is available through this Web site or by calling the Aptus Cargo Management Center. \n              We are able to provide real time status reports of your valued deposits and cargo with us.\n            </p>\n\n            {/* Tracking Features */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0\"></div>\n                <p className=\"text-gray-600\">Real-time location updates</p>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0\"></div>\n                <p className=\"text-gray-600\">Delivery status notifications</p>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0\"></div>\n                <p className=\"text-gray-600\">Estimated delivery time</p>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0\"></div>\n                <p className=\"text-gray-600\">24/7 customer support</p>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,aAAa,IAAI,IAAI;QAE1B,cAAc;QAEd,2BAA2B;QAC3B,WAAW;YACT,cAAc;YACd,gEAAgE;YAChE,MAAM,AAAC,sBAAkC,OAAb;YAC5B,gBAAgB;QAClB,GAAG;IACL;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA6D;;;;;;0CAI3E,6LAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAI7E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;4CACC,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,aAAY;4CACZ,QAAQ;4CACR,WAAU;;;;;;sDAGZ,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAOtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAMrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;GAlGwB;KAAA", "debugId": null}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/Features.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { FaGlobe, FaShippingFast, FaHeadphones } from 'react-icons/fa';\n\nconst features = [\n  {\n    icon: FaGlobe,\n    title: 'Professional Global Logistics',\n    description: 'Aptus Group offers a full range of Logistics services for every business.'\n  },\n  {\n    icon: FaShippingFast,\n    title: 'On Time Delivery',\n    description: 'Aptus delivers Air, Land and Sea Freights in time through extensive asset investments.'\n  },\n  {\n    icon: FaHeadphones,\n    title: '24/7 Support and Tracking',\n    description: 'Get world-class support from our agents who will ensure a seamless service for your cargo. Track your cargo by its shipment number anytime.'\n  }\n];\n\nexport default function Features() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div>\n              <h6 className=\"text-blue-600 uppercase text-sm font-semibold tracking-wider mb-4\">\n                Why Choose Aptus Group\n              </h6>\n              <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900 leading-tight\">\n                We Are Trusted Logistics Company Since 2012\n              </h2>\n            </div>\n\n            {/* Features List */}\n            <div className=\"space-y-8\">\n              {features.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, y: 30 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.2 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-start space-x-4\"\n                >\n                  <div className=\"flex-shrink-0\">\n                    <feature.icon className=\"text-blue-600 text-4xl\" />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <h5 className=\"text-xl font-semibold text-gray-900\">\n                      {feature.title}\n                    </h5>\n                    <p className=\"text-gray-600 leading-relaxed\">\n                      {feature.description}\n                    </p>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Right Image */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"relative h-96 lg:h-[500px]\"\n          >\n            <Image\n              src=\"/images/feature.jpg\"\n              alt=\"Aptus Group Features\"\n              fill\n              className=\"object-cover rounded-lg shadow-lg\"\n            />\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,WAAW;IACf;QACE,MAAM,iJAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,iJAAA,CAAA,iBAAc;QACpB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,iJAAA,CAAA,eAAY;QAClB,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoE;;;;;;kDAGlF,6LAAC;wCAAG,WAAU;kDAA6D;;;;;;;;;;;;0CAM7E,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,QAAQ,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAE1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,6LAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;uCAfnB;;;;;;;;;;;;;;;;kCAwBb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,IAAI;4BACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;KApEwB", "debugId": null}}, {"offset": {"line": 2368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/Testimonials.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaQuoteRight, FaChevronLeft, FaChevronRight } from 'react-icons/fa';\n\nconst testimonials = [\n  {\n    id: 1,\n    name: '<PERSON>',\n    company: '<PERSON><PERSON>',\n    message: 'Aptus customer service is the best we have had. We will keep making our ocean shipping through you.'\n  },\n  {\n    id: 2,\n    name: '<PERSON>',\n    company: 'Autova',\n    message: 'Expertly delivered cargo using innovative solutions to fit an ever changing business atmosphere.'\n  },\n  {\n    id: 3,\n    name: '<PERSON><PERSON>',\n    company: 'Hotelier',\n    message: 'Aptus customer service is the best we have had. We will keep making our ocean shipping through you.'\n  },\n  {\n    id: 4,\n    name: 'June K',\n    company: 'Cargof',\n    message: 'Aptus customer service is the best we have had. We will keep making our ocean shipping through you.'\n  }\n];\n\nexport default function Testimonials() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n    }, 5000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  const goToPrevious = () => {\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);\n  };\n\n  const goToNext = () => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n  };\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-gray-900\">\n            What Our Clients Say!\n          </h2>\n        </motion.div>\n\n        <div className=\"relative max-w-4xl mx-auto\">\n          {/* Navigation Buttons */}\n          <button\n            onClick={goToPrevious}\n            className=\"absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 z-10 bg-white hover:bg-gray-50 text-gray-600 hover:text-blue-600 p-3 rounded-full shadow-lg transition-colors duration-300\"\n          >\n            <FaChevronLeft />\n          </button>\n\n          <button\n            onClick={goToNext}\n            className=\"absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 z-10 bg-white hover:bg-gray-50 text-gray-600 hover:text-blue-600 p-3 rounded-full shadow-lg transition-colors duration-300\"\n          >\n            <FaChevronRight />\n          </button>\n\n          {/* Testimonial Cards */}\n          <div className=\"overflow-hidden\">\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={currentIndex}\n                initial={{ opacity: 0, x: 100 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -100 }}\n                transition={{ duration: 0.5 }}\n                className=\"bg-white p-8 rounded-lg shadow-lg relative\"\n              >\n                <FaQuoteRight className=\"absolute top-4 right-4 text-gray-200 text-4xl\" />\n                \n                <div className=\"mb-6\">\n                  <p className=\"text-gray-600 text-lg leading-relaxed italic\">\n                    \"{testimonials[currentIndex].message}\"\n                  </p>\n                </div>\n\n                <div className=\"flex items-center\">\n                  <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4\">\n                    {testimonials[currentIndex].name.charAt(0)}\n                  </div>\n                  <div>\n                    <h5 className=\"font-semibold text-gray-900\">\n                      {testimonials[currentIndex].name}\n                    </h5>\n                    <p className=\"text-gray-600 text-sm\">\n                      {testimonials[currentIndex].company}\n                    </p>\n                  </div>\n                </div>\n              </motion.div>\n            </AnimatePresence>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center mt-8 space-x-2\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentIndex(index)}\n                className={`w-3 h-3 rounded-full transition-colors duration-300 ${\n                  index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,SAAS;IACX;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,QAAQ;gDAAY;oBACxB;wDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;;gBAC5D;+CAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IAClF;IAEA,MAAM,WAAW;QACf,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;IAC5D;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAG,WAAU;kCAA+C;;;;;;;;;;;8BAK/D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,iJAAA,CAAA,gBAAa;;;;;;;;;;sCAGhB,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,iJAAA,CAAA,iBAAc;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAI;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAI;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAEV,6LAAC,iJAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDAExB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;oDAA+C;oDACxD,YAAY,CAAC,aAAa,CAAC,OAAO;oDAAC;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;8DAE1C,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;sEAElC,6LAAC;4DAAE,WAAU;sEACV,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;;mCAxBpC;;;;;;;;;;;;;;;sCAiCX,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,AAAC,uDAEX,OADC,UAAU,eAAe,gBAAgB;mCAHtC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GAtGwB;KAAA", "debugId": null}}, {"offset": {"line": 2645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaMapMarkerAlt, FaPhone, FaEnvelope, FaTwitter, FaFacebookF, FaYoutube, FaLinkedinIn } from 'react-icons/fa';\n\nconst services = [\n  'Air Freight',\n  'Sea Freight',\n  'Road Freight',\n  'Logistic Solutions',\n  'Safe Depository solutions'\n];\n\nconst quickLinks = [\n  { name: 'About Us', href: '#about' },\n  { name: 'Contact Us', href: '#quote' },\n  { name: 'Our Services', href: '#services' },\n  { name: 'Support', href: '#track' }\n];\n\nconst socialLinks = [\n  { icon: FaTwitter, href: '#', label: 'Twitter' },\n  { icon: FaFacebookF, href: '#', label: 'Facebook' },\n  { icon: FaYoutube, href: '#', label: 'YouTube' },\n  { icon: FaLinkedinIn, href: '#', label: 'LinkedIn' }\n];\n\nexport default function Footer() {\n  const scrollToSection = (sectionId: string) => {\n    const element = document.getElementById(sectionId.replace('#', ''));\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <footer className=\"bg-gray-900 text-white pt-16 pb-8\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8\">\n          {/* Address Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"space-y-4\"\n          >\n            <h4 className=\"text-xl font-semibold mb-6\">Address</h4>\n            \n            <div className=\"flex items-start space-x-3\">\n              <FaMapMarkerAlt className=\"text-blue-400 mt-1 flex-shrink-0\" />\n              <p className=\"text-gray-300\">Airport North Road, Nairobi, KENYA</p>\n            </div>\n            \n            <div className=\"flex items-center space-x-3\">\n              <FaPhone className=\"text-blue-400 flex-shrink-0\" />\n              <a \n                href=\"tel:254101631676\" \n                className=\"text-gray-300 hover:text-white transition-colors\"\n              >\n                +254 101 631 676\n              </a>\n            </div>\n            \n            <div className=\"flex items-center space-x-3\">\n              <FaEnvelope className=\"text-blue-400 flex-shrink-0\" />\n              <a \n                href=\"mailto:<EMAIL>\" \n                className=\"text-gray-300 hover:text-white transition-colors\"\n              >\n                <EMAIL>\n              </a>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex space-x-3 pt-4\">\n              {socialLinks.map((social, index) => (\n                <a\n                  key={index}\n                  href={social.href}\n                  aria-label={social.label}\n                  className=\"w-10 h-10 bg-gray-800 hover:bg-blue-600 rounded-full flex items-center justify-center transition-colors duration-300\"\n                >\n                  <social.icon />\n                </a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Services Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.1 }}\n            viewport={{ once: true }}\n            className=\"space-y-4\"\n          >\n            <h4 className=\"text-xl font-semibold mb-6\">Services</h4>\n            <div className=\"space-y-3\">\n              {services.map((service, index) => (\n                <a\n                  key={index}\n                  href=\"#services\"\n                  onClick={(e) => {\n                    e.preventDefault();\n                    scrollToSection('#services');\n                  }}\n                  className=\"block text-gray-300 hover:text-white transition-colors duration-300\"\n                >\n                  {service}\n                </a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Quick Links Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-4\"\n          >\n            <h4 className=\"text-xl font-semibold mb-6\">Quick Links</h4>\n            <div className=\"space-y-3\">\n              {quickLinks.map((link, index) => (\n                <a\n                  key={index}\n                  href={link.href}\n                  onClick={(e) => {\n                    e.preventDefault();\n                    scrollToSection(link.href);\n                  }}\n                  className=\"block text-gray-300 hover:text-white transition-colors duration-300\"\n                >\n                  {link.name}\n                </a>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Company Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.3 }}\n            viewport={{ once: true }}\n            className=\"space-y-4\"\n          >\n            <h4 className=\"text-xl font-semibold mb-6\">Aptus Group</h4>\n            <p className=\"text-gray-300 leading-relaxed\">\n              Powering Trade through world-class transport and logistics services across \n              East Africa, the Great Lakes region, and beyond.\n            </p>\n            <div className=\"pt-4\">\n              <div className=\"bg-blue-600 text-white px-4 py-2 rounded inline-block\">\n                <h3 className=\"font-bold\">Aptus Group</h3>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Copyright Section */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"border-t border-gray-800 pt-8\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <p className=\"text-gray-400 text-center md:text-left\">\n              © <a href=\"#\" className=\"hover:text-white transition-colors\">Aptus Group</a>, All Rights Reserved.\n            </p>\n            <p className=\"text-gray-400 text-center md:text-right text-sm\">\n              Powered by{' '}\n              <a \n                href=\"https://wezabusiness.com\" \n                className=\"hover:text-white transition-colors\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                WezaBusiness\n              </a>\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;IACjB;QAAE,MAAM;QAAY,MAAM;IAAS;IACnC;QAAE,MAAM;QAAc,MAAM;IAAS;IACrC;QAAE,MAAM;QAAgB,MAAM;IAAY;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAS;CACnC;AAED,MAAM,cAAc;IAClB;QAAE,MAAM,iJAAA,CAAA,YAAS;QAAE,MAAM;QAAK,OAAO;IAAU;IAC/C;QAAE,MAAM,iJAAA,CAAA,cAAW;QAAE,MAAM;QAAK,OAAO;IAAW;IAClD;QAAE,MAAM,iJAAA,CAAA,YAAS;QAAE,MAAM;QAAK,OAAO;IAAU;IAC/C;QAAE,MAAM,iJAAA,CAAA,eAAY;QAAE,MAAM;QAAK,OAAO;IAAW;CACpD;AAEc,SAAS;IACtB,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,cAAc,CAAC,UAAU,OAAO,CAAC,KAAK;QAC/D,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAE3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAMH,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6LAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,cAAY,OAAO,KAAK;4CACxB,WAAU;sDAEV,cAAA,6LAAC,OAAO,IAAI;;;;;2CALP;;;;;;;;;;;;;;;;sCAYb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4CAEC,MAAK;4CACL,SAAS,CAAC;gDACR,EAAE,cAAc;gDAChB,gBAAgB;4CAClB;4CACA,WAAU;sDAET;2CARI;;;;;;;;;;;;;;;;sCAeb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,SAAS,CAAC;gDACR,EAAE,cAAc;gDAChB,gBAAgB,KAAK,IAAI;4CAC3B;4CACA,WAAU;sDAET,KAAK,IAAI;2CARL;;;;;;;;;;;;;;;;sCAeb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAI7C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAyC;kDAClD,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAqC;;;;;;oCAAe;;;;;;;0CAE9E,6LAAC;gCAAE,WAAU;;oCAAkD;oCAClD;kDACX,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAO;wCACP,KAAI;kDACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAnKwB", "debugId": null}}, {"offset": {"line": 3112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\n\nexport default function LoadingSpinner() {\n  return (\n    <div className=\"fixed inset-0 bg-white flex items-center justify-center z-50\">\n      <motion.div\n        className=\"w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full\"\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      />\n      <span className=\"ml-4 text-gray-600\">Loading...</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;;;;;;0BAE9D,6LAAC;gBAAK,WAAU;0BAAqB;;;;;;;;;;;;AAG3C;KAXwB", "debugId": null}}, {"offset": {"line": 3166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/code/aptus/aptus-nextjs/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { FaArrowUp } from \"react-icons/fa\";\n\nimport Navbar from \"@/components/Navbar\";\nimport Hero from \"@/components/Hero\";\nimport About from \"@/components/About\";\nimport Stats from \"@/components/Stats\";\nimport Services from \"@/components/Services\";\nimport QuoteForm from \"@/components/QuoteForm\";\nimport TrackingForm from \"@/components/TrackingForm\";\nimport Features from \"@/components/Features\";\nimport Testimonials from \"@/components/Testimonials\";\nimport Footer from \"@/components/Footer\";\nimport LoadingSpinner from \"@/components/LoadingSpinner\";\n\nexport default function Home() {\n  const [loading, setLoading] = useState(true);\n  const [showBackToTop, setShowBackToTop] = useState(false);\n\n  useEffect(() => {\n    // Simulate loading\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 2000);\n\n    // Handle scroll for back to top button\n    const handleScroll = () => {\n      setShowBackToTop(window.scrollY > 300);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n\n    return () => {\n      clearTimeout(timer);\n      window.removeEventListener(\"scroll\", handleScroll);\n    };\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  if (loading) {\n    return <LoadingSpinner />;\n  }\n\n  return (\n    <div className=\"min-h-screen\">\n      <Navbar />\n      <Hero />\n      <About />\n      <Stats />\n      <Services />\n      <QuoteForm />\n      <TrackingForm />\n      <Features />\n      <Testimonials />\n      <Footer />\n\n      {/* Back to Top Button */}\n      {showBackToTop && (\n        <motion.button\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          onClick={scrollToTop}\n          className=\"fixed bottom-6 right-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-50\"\n        >\n          <FaArrowUp />\n        </motion.button>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAhBA;;;;;;;;;;;;;;;AAkBe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,mBAAmB;YACnB,MAAM,QAAQ;wCAAW;oBACvB,WAAW;gBACb;uCAAG;YAEH,uCAAuC;YACvC,MAAM;+CAAe;oBACnB,iBAAiB,OAAO,OAAO,GAAG;gBACpC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;kCAAO;oBACL,aAAa;oBACb,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;yBAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,IAAI,SAAS;QACX,qBAAO,6LAAC,uIAAA,CAAA,UAAc;;;;;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BACP,6LAAC,6HAAA,CAAA,UAAI;;;;;0BACL,6LAAC,8HAAA,CAAA,UAAK;;;;;0BACN,6LAAC,8HAAA,CAAA,UAAK;;;;;0BACN,6LAAC,iIAAA,CAAA,UAAQ;;;;;0BACT,6LAAC,kIAAA,CAAA,UAAS;;;;;0BACV,6LAAC,qIAAA,CAAA,UAAY;;;;;0BACb,6LAAC,iIAAA,CAAA,UAAQ;;;;;0BACT,6LAAC,qIAAA,CAAA,UAAY;;;;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;YAGN,+BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC,iJAAA,CAAA,YAAS;;;;;;;;;;;;;;;;AAKpB;GAzDwB;KAAA", "debugId": null}}]}