/*!
* phone-codes/phone-mx.min.js
* https://github.com/RobinHerbots/Inputmask
* Copyright (c) 2010 - 2018 <PERSON>
* Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
* Version: 4.0.0-beta.51
*/

!function(c){"function"==typeof define&&define.amd?define(["../inputmask"],c):"object"==typeof exports?module.exports=c(require("../inputmask")):c(window.Inputmask)}(function(c){return c.extendAliases({phonemx:{alias:"abstractphone",countrycode:"52",phoneCodes:[{mask:"+52 (744) ###-####",cc:"MX",cd:"Mexico",city:"Acapulco"},{mask:"+52 (449) ###-####",cc:"MX",cd:"Mexico",city:"Aguascalientes"},{mask:"+52 (833) ###-####",cc:"MX",cd:"Mexico",city:"Altamira"},{mask:"+52 (81) ####-####",cc:"MX",cd:"Mexico",city:"Apodaca"},{mask:"+52 (624) ###-####",cc:"MX",cd:"Mexico",city:"Cabo San Lucas"},{mask:"+52 (981) ###-####",cc:"MX",cd:"Mexico",city:"Campeche"},{mask:"+52 (998) ###-####",cc:"MX",cd:"Mexico",city:"Cancún"},{mask:"+52 (461) ###-####",cc:"MX",cd:"Mexico",city:"Celaya"},{mask:"+52 (983) ###-####",cc:"MX",cd:"Mexico",city:"Chetumal"},{mask:"+52 (614) ###-####",cc:"MX",cd:"Mexico",city:"Chihuahua"},{mask:"+52 (747) ###-####",cc:"MX",cd:"Mexico",city:"Chilpancingo De Los Bravo"},{mask:"+52 (877) ###-####",cc:"MX",cd:"Mexico",city:"Ciudad Acuña"},{mask:"+52 (656) ###-####",cc:"MX",cd:"Mexico",city:"Ciudad Juárez"},{mask:"+52 (871) ###-####",cc:"MX",cd:"Mexico",city:"Ciudad Lerdo"},{mask:"+52 (833) ###-####",cc:"MX",cd:"Mexico",city:"Ciudad Madero"},{mask:"+52 (644) ###-####",cc:"MX",cd:"Mexico",city:"Ciudad Obregón"},{mask:"+52 (834) ###-####",cc:"MX",cd:"Mexico",city:"Ciudad Victoria"},{mask:"+52 (312) ###-####",cc:"MX",cd:"Mexico",city:"Colima"},{mask:"+52 (963) ###-####",cc:"MX",cd:"Mexico",city:"Comitán"},{mask:"+52 (271) ###-####",cc:"MX",cd:"Mexico",city:"Córdoba"},{mask:"+52 (777) ###-####",cc:"MX",cd:"Mexico",city:"Cuernavaca"},{mask:"+52 (667) ###-####",cc:"MX",cd:"Mexico",city:"Culiacán Rosales"},{mask:"+52 (646) ###-####",cc:"MX",cd:"Mexico",city:"Ensenada"},{mask:"+52 (871) ###-####",cc:"MX",cd:"Mexico",city:"Gómez Palacio"},{mask:"+52 (33) ####-####",cc:"MX",cd:"Mexico",city:"Guadalajara"},{mask:"+52 (473) ###-####",cc:"MX",cd:"Mexico",city:"Guanajuato"},{mask:"+52 (662) ###-####",cc:"MX",cd:"Mexico",city:"Hermosillo"},{mask:"+52 (715) ###-####",cc:"MX",cd:"Mexico",city:"Heroica Zitácuaro"},{mask:"+52 (777) ###-####",cc:"MX",cd:"Mexico",city:"Jiutepec"},{mask:"+52 (612) ###-####",cc:"MX",cd:"Mexico",city:"La Paz (Baja California Sur)"},{mask:"+52 (477) ###-####",cc:"MX",cd:"Mexico",city:"León"},{mask:"+52 (314) ###-####",cc:"MX",cd:"Mexico",city:"Manzanillo"},{mask:"+52 (868) ###-####",cc:"MX",cd:"Mexico",city:"Matamoros"},{mask:"+52 (999) ###-####",cc:"MX",cd:"Mexico",city:"Mérida"},{mask:"+52 (686) ###-####",cc:"MX",cd:"Mexico",city:"Mexicali"},{mask:"+52 (55) ####-####",cc:"MX",cd:"Mexico",city:"México D.F."},{mask:"+52 (81) ####-####",cc:"MX",cd:"Mexico",city:"Monterrey"},{mask:"+52 (443) ###-####",cc:"MX",cd:"Mexico",city:"Morelia"},{mask:"+52 (867) ###-####",cc:"MX",cd:"Mexico",city:"Nuevo Laredo"},{mask:"+52 (951) ###-####",cc:"MX",cd:"Mexico",city:"Oaxaca De Juárez"},{mask:"+52 (771) ###-####",cc:"MX",cd:"Mexico",city:"Pachuca De Soto"},{mask:"+52 (984) ###-####",cc:"MX",cd:"Mexico",city:"Playa Del Carmen"},{mask:"+52 (222) ###-####",cc:"MX",cd:"Mexico",city:"Puebla"},{mask:"+52 (322) ###-####",cc:"MX",cd:"Mexico",city:"Puerto Vallarta"},{mask:"+52 (899) ###-####",cc:"MX",cd:"Mexico",city:"Reynosa"},{mask:"+52 (464) ###-####",cc:"MX",cd:"Mexico",city:"Salamanca"},{mask:"+52 (844) ###-####",cc:"MX",cd:"Mexico",city:"Saltillo"},{mask:"+52 (967) ###-####",cc:"MX",cd:"Mexico",city:"San Cristóbal De Las Casas"},{mask:"+52 (444) ###-####",cc:"MX",cd:"Mexico",city:"San Luis Potosí"},{mask:"+52 (81) ####-####",cc:"MX",cd:"Mexico",city:"San Nicolás De Los Garza"},{mask:"+52 (615) ###-####",cc:"MX",cd:"Mexico",city:"Santa Rosalía"},{mask:"+52 (442) ###-####",cc:"MX",cd:"Mexico",city:"Santiago De Querétaro"},{mask:"+52 (833) ###-####",cc:"MX",cd:"Mexico",city:"Tampico"},{mask:"+52 (665) ###-####",cc:"MX",cd:"Mexico",city:"Tecate"},{mask:"+52 (311) ###-####",cc:"MX",cd:"Mexico",city:"Tepic"},{mask:"+52 (664) ###-####",cc:"MX",cd:"Mexico",city:"Tijuana"},{mask:"+52 (246) ###-####",cc:"MX",cd:"Mexico",city:"Tlaxcala De Xicohténcatl"},{mask:"+52 (612) ###-####",cc:"MX",cd:"Mexico",city:"Todos Santos"},{mask:"+52 (722) ###-####",cc:"MX",cd:"Mexico",city:"Toluca De Lerdo"},{mask:"+52 (871) ###-####",cc:"MX",cd:"Mexico",city:"Torreón"},{mask:"+52 (984) ###-####",cc:"MX",cd:"Mexico",city:"Tulúm"},{mask:"+52 (961) ###-####",cc:"MX",cd:"Mexico",city:"Tuxtla Gutiérrez"},{mask:"+52 (452) ###-####",cc:"MX",cd:"Mexico",city:"Uruapan"},{mask:"+52 (985) ###-####",cc:"MX",cd:"Mexico",city:"Valladolid"},{mask:"+52 (457) ###-####",cc:"MX",cd:"Mexico",city:"Valparaíso"},{mask:"+52 (229) ###-####",cc:"MX",cd:"Mexico",city:"Veracruz"},{mask:"+52 (618) ###-####",cc:"MX",cd:"Mexico",city:"Victoria De Durango"},{mask:"+52 (993) ###-####",cc:"MX",cd:"Mexico",city:"Villahermosa"},{mask:"+52 (228) ###-####",cc:"MX",cd:"Mexico",city:"Xalapa-Enríquez"},{mask:"+52 (492) ###-####",cc:"MX",cd:"Mexico",city:"Zacatecas"},{mask:"+52 (351) ###-####",cc:"MX",cd:"Mexico",city:"Zamora"},{mask:"+52 (33) ####-####",cc:"MX",cd:"Mexico",city:"Zapopan"}]}}),c});