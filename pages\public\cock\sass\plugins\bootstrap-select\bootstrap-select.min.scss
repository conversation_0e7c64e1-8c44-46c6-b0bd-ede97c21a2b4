//  =================
//      Imports
//  =================

@import '../../base/base';    // Base Variables

select {
  &.bs-select-hidden, &.selectpicker {
    display: none !important;
  }
}

.bootstrap-select {
  width: 220px \0;

  &.btn-group > .dropdown-toggle {
    height: auto;
    border: 1px solid $m-color_4;
    color: $dark !important;
    font-size: 15px;
    padding: 8px 10px;
    letter-spacing: 1px;
    background-color: $white;
    height: auto;
    padding: .75rem 1.25rem;
    border-radius: 6px;
    box-shadow: none;
  }

  > {
    .dropdown-toggle {
      width: 100%;
      padding-right: 25px;
      z-index: 1;

      &.bs-placeholder {
        color: $m-color_6;

        &:hover, &:focus, &:active {
          color: $m-color_6;
        }
      }
    }

    select {
      position: absolute !important;
      bottom: 0;
      left: 50%;
      display: block !important;
      width: 0.5px !important;
      height: 100% !important;
      padding: 0 !important;
      opacity: 0 !important;
      border: 0;

      &.mobile-device {
        top: 0;
        left: 0;
        display: block !important;
        width: 100% !important;
        z-index: 2;
      }
    }
  }
}

.has-error .bootstrap-select .dropdown-toggle, .error .bootstrap-select .dropdown-toggle {
  border-color: #b94a48;
}

.bootstrap-select {
  &.fit-width {
    width: auto !important;
  }

  &:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 220px;
  }

  &.show .dropdown-toggle {
    border: 1px solid $primary !important;
    box-shadow: 0 0 5px 2px rgba(194, 213, 255, 0.6196078431);
    color: $dark;

    &.btn-outline-primary {
      border: 1px solid $primary !important;
    }

    &.btn-outline-info {
      border: 1px solid $info !important;
    }

    &.btn-outline-success {
      border: 1px solid $success !important;
    }

    &.btn-outline-warning {
      border: 1px solid $warning !important;
    }

    &.btn-outline-danger {
      border: 1px solid $danger !important;
    }
  }

  &.form-control {
    margin-bottom: 0;
    padding: 0;
    border: 0;
    background: transparent;

    &:not([class*=col-]) {
      width: 100%;
    }

    &.input-group-btn {
      z-index: auto;

      &:not(:first-child):not(:last-child) > .btn {
        border-radius: 0;
      }
    }
  }

  &.btn-group {
    &:not(.input-group-btn), &[class*=col-] {
      float: none;
      display: inline-block;
      margin-left: 0;
    }

    &.dropdown-menu-right, &[class*=col-].dropdown-menu-right {
      float: right;
    }
  }
}

.row .bootstrap-select.btn-group[class*=col-].dropdown-menu-right {
  float: right;
}

.form-inline .bootstrap-select.btn-group, .form-horizontal .bootstrap-select.btn-group, .form-group .bootstrap-select.btn-group {
  margin-bottom: 0;
}

.form-group-lg .bootstrap-select.btn-group.form-control, .form-group-sm .bootstrap-select.btn-group.form-control {
  padding: 0;
}

.form-group-lg .bootstrap-select.btn-group.form-control .dropdown-toggle, .form-group-sm .bootstrap-select.btn-group.form-control .dropdown-toggle {
  height: 100%;
  font-size: inherit;
  line-height: inherit;
  border-radius: inherit;
}

.form-inline .bootstrap-select.btn-group .form-control {
  width: 100%;
}

.bootstrap-select.btn-group {
  &.disabled {
    cursor: not-allowed;

    &:focus {
      outline: 0 !important;
    }
  }

  > .disabled {
    cursor: not-allowed;
  }

  > .disabled:focus {
    outline: 0 !important;
  }

  &.bs-container {
    position: absolute;
    height: 0 !important;
    padding: 0 !important;

    .dropdown-menu {
      z-index: 1060;
    }
  }

  .dropdown-toggle {
    .filter-option {
      display: inline-block;
      text-align: left;
    }

    .caret {
      position: absolute;
      top: 50%;
      right: 12px;
      margin-top: -2px;
      vertical-align: middle;
    }

    &:after {
      display: none;
    }
  }
}

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid\9;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}

.bootstrap-select {
  &.btn-group {
    &[class*=col-] .dropdown-toggle {
      width: 100%;
    }

    .dropdown-menu {
      min-width: 100%;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box;
      padding: 0;
      -webkit-box-shadow: 2px 5px 17px 0 rgba(31, 45, 61, 0.1);
      box-shadow: 2px 5px 17px 0 rgba(31, 45, 61, 0.1);

      .popover-title {
        padding: 15px;
      }

      &.inner {
        display: block;
        position: static;
        float: none;
        border: 0;
        padding: 0;
        margin: 0;
        border-radius: 0;
        box-shadow: none;
      }

      a.dropdown-item {
        position: relative;
        cursor: pointer;
        user-select: none;
        padding: 0;

        &.active small {
          color: $white;
        }

        &.disabled a {
          cursor: not-allowed;
        }

        &.hidden {
          display: none;
        }
      }
    }
  }

  .dropdown-header {
    padding: 10px 22px;
  }

  .dropdown-item {
    &.active, &:active {
      background-color: transparent;
      color: $m-color_9;
    }
  }

  &.btn-group {
    .dropdown-menu {
      a.dropdown-item {
        span.dropdown-item-inner {
          display: block;
          padding: 9px 19px;
          font-weight: 600;

          &:not([class*="bg-"]):hover {
            color: $primary;
            background-color: rgba(27, 85, 226, 0.2392156863);
          }

          &.opt {
            position: relative;
            padding-left: 2.25em;
          }

          span {
            &.check-mark {
              display: none;
            }

            &.text {
              display: inline-block;
            }
          }
        }

        small {
          padding-left: 0.5em;
        }
      }

      .dropdown-item .span {
        &.check-mark {
          display: none;
        }

        &.text {
          display: inline-block;
        }
      }

      .notify {
        position: absolute;
        bottom: 5px;
        width: 96%;
        margin: 0 2%;
        min-height: 26px;
        padding: 3px 5px;
        background: $m-color_1;
        border: 1px solid #e3e3e3;
        -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
        pointer-events: none;
        opacity: .9;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
      }
    }

    .no-results {
      padding: 3px;
      background: $m-color_1;
      margin: 0 5px;
      white-space: nowrap;
    }

    &.fit-width .dropdown-toggle {
      .filter-option {
        position: static;
      }

      .caret {
        position: static;
        top: auto;
        margin-top: -1px;
      }
    }

    &.show-tick .dropdown-menu a {
      &.selected span.dropdown-item-inner span.check-mark {
        position: absolute;
        display: inline-block;
        right: 15px;
        margin-top: 5px;
      }

      a span.text {
        margin-right: 34px;
      }
    }
  }

  &.show-menu-arrow {
    &.open > .dropdown-toggle {
      z-index: 1061;
    }

    .dropdown-toggle {
      &:before {
        content: '';
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-bottom: 7px solid rgba(204, 204, 204, 0.2);
        position: absolute;
        bottom: -4px;
        left: 9px;
        display: none;
      }

      &:after {
        content: '';
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-bottom: 6px solid $white;
        position: absolute;
        bottom: -4px;
        left: 10px;
        display: none;
      }
    }

    &.dropup .dropdown-toggle {
      &:before {
        bottom: auto;
        top: -3px;
        border-top: 7px solid rgba(204, 204, 204, 0.2);
        border-bottom: 0;
      }

      &:after {
        bottom: auto;
        top: -3px;
        border-top: 6px solid $white;
        border-bottom: 0;
      }
    }

    &.pull-right .dropdown-toggle {
      &:before {
        right: 12px;
        left: auto;
      }

      &:after {
        right: 13px;
        left: auto;
      }
    }

    &.open > .dropdown-toggle {
      &:before, &:after {
        display: block;
      }
    }
  }
}

.bs-searchbox, .bs-actionsbox, .bs-donebutton {
  padding: 4px 8px;
}

.bs-actionsbox {
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;

  .btn-group button {
    width: 50%;
  }
}

.bs-donebutton {
  float: left;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;

  .btn-group button {
    width: 100%;
  }
}

.bs-searchbox {
  + .bs-actionsbox {
    padding: 0 8px 4px;
  }

  .form-control {
    margin-bottom: 0;
    width: 100%;
    float: none;
  }
}

.input-group .bs-searchbox .form-control {
  width: 100%;
}

.btn-outline-primary:hover {
  color: $primary !important;
}

.btn-outline-info:hover {
  color: $info !important;
}

.btn-outline-warning:hover {
  color: $warning !important;
}

.btn-outline-success:hover {
  color: $success !important;
}

.btn-outline-danger:hover {
  color: $danger !important;
}

.bootstrap-select.btn-group > .dropdown-toggle {
  &.btn-outline-primary, &.btn-outline-success, &.btn-outline-info, &.btn-outline-danger, &.btn-outline-warning {
    background-color: transparent;
  }
}

.btn-outline-primary:not(:disabled):not(.disabled) {
  &.active, &:active {
    background-color: transparent;
    color: $primary !important;
  }
}

.show > .btn-outline-primary.dropdown-toggle {
  background-color: transparent;
  color: $primary !important;
}

.btn-outline-success:not(:disabled):not(.disabled) {
  &.active, &:active {
    background-color: transparent;
    color: $success !important;
  }
}

.show > .btn-outline-success.dropdown-toggle {
  background-color: transparent;
  color: $success !important;
}

.btn-outline-info:not(:disabled):not(.disabled) {
  &.active, &:active {
    background-color: transparent;
    color: $info !important;
  }
}

.show > .btn-outline-info.dropdown-toggle {
  background-color: transparent;
  color: $info !important;
}

.btn-outline-danger:not(:disabled):not(.disabled) {
  &.active, &:active {
    background-color: transparent;
    color: $danger !important;
  }
}

.show > .btn-outline-danger.dropdown-toggle {
  background-color: transparent;
  color: $danger !important;
}

.btn-outline-warning:not(:disabled):not(.disabled) {
  &.active, &:active {
    background-color: transparent;
    color: $warning !important;
  }
}

.show > .btn-outline-warning.dropdown-toggle {
  background-color: transparent;
  color: $warning !important;
}

.btn {
  &.btn-outline-primary .caret {
    border-top-color: $primary;
  }

  &.btn-outline-success .caret {
    border-top-color: $success;
  }

  &.btn-outline-info .caret {
    border-top-color: $info;
  }

  &.btn-outline-danger .caret {
    border-top-color: $danger;
  }

  &.btn-outline-warning .caret {
    border-top-color: $warning;
  }
}