//  =================
//      Imports
//  =================

@import '../../base/base';    // Base Variables

.table-controls {
  padding: 0;
  margin: 0;
  list-style: none;

  > li {
    display: inline-block;
    margin: 0 2px;
    line-height: 1;

    > a {
      display: inline-block;

      i {
        margin: 0;
        color: #555;
        font-size: 16px;
        display: block;

        &:hover {
          text-decoration: none;
        }
      }
    }
  }
}

.table .progress {
  margin-bottom: 0;
}

/*Table Colors*/

.contextual-table.table > {
  thead > tr > th, tbody > tr > td {
    border: none;
  }
}

.table-default > {
  td, th {
    background-color: $m-color_1;
    color: $dark;
  }
}

.table-primary > {
  td, th {
    background-color: #c7d8fd;
    color: $info;
  }
}

.table-secondary > {
  td, th {
    background-color: #e0d4f9;
    color: $secondary;
  }
}

.table-success > {
  td, th {
    background-color: #cbfdf3;
    color: $success;
  }
}

.table-danger > {
  td, th {
    background-color: $l-danger;
    color: $danger;
  }
}

.table-warning > {
  td, th {
    background-color: #fdefd5;
    color: $warning;
  }
}

.table-info > {
  td, th {
    background-color: $l-info;
    color: $info;
  }
}

.table-light > {
  td, th {
    background-color: #ffffff;
    color: $m-color_6;
  }
}

.table-dark > {
  td, th {
    background-color: $l-dark;
    color: $m-color_9;
    border-color: #fff !important;
  }
}