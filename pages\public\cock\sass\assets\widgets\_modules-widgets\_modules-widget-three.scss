/*
    ====================
        Order Summary
    ====================
*/
body.minimal .widget-three {
    border: 1px solid #e0e6ed!important;
    box-shadow: none;
}
.widget-three {
  position: relative;
  background: $white;
  padding: 20px;
  border-radius: 8px;
  height: 100%;
  -webkit-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
  -moz-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
  box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);

  .widget-heading {
    margin-bottom: 54px;

    h5 {
      font-size: 19px;
      display: block;
      color: $m-color_10;
      font-weight: 600;
      margin-bottom: 0;
    }
  }

  .widget-content {
    font-size: 17px;

    .summary-list {
      display: flex;

      &:not(:last-child) {
        margin-bottom: 30px;
      }
    }

    .w-icon {
      display: inline-block;
      padding: 8px 8px;
      border-radius: 50%;
      display: inline-flex;
      align-self: center;
      height: 34px;
      width: 34px;
      margin-right: 12px;

      svg {
        display: block;
        width: 17px;
        height: 17px;
      }
    }

    .summary-list {
      &:nth-child(1) .w-icon {
        background: $l-secondary;
      }

      &:nth-child(2) .w-icon {
        background: $l-success;
      }

      &:nth-child(3) .w-icon {
        background: $l-warning;
      }

      &:nth-child(1) .w-icon svg {
        color: $secondary;
      }

      &:nth-child(2) .w-icon svg {
        color: $m-color_14;
      }

      &:nth-child(3) .w-icon svg {
        color: $warning;
      }
    }

    .w-summary-details {
      width: 100%;
      align-self: center;
    }

    .w-summary-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1px;

      h6 {
        font-size: 13px;
        font-weight: 600;
        margin-bottom: 0;
        color: $m-color_6;
      }

      p {
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 0;
        color: $m-color_6;
      }
    }

    .w-summary-stats .progress {
      margin-bottom: 0;
      height: 6px;
      border-radius: 20px;
      box-shadow: 0 2px 2px rgba(224, 230, 237, 0.4588235294), 1px 6px 7px rgba(224, 230, 237, 0.4588235294);
    }
  }
}