//  =================
//      Imports
//  =================

@import '../base/base';    // Base Variables

html {
  min-height: 100%;
}

body {
  color: #888ea8;
  height: 100%;
  font-size: 0.875rem;
  background: $body-color;
  overflow-x: hidden;
  overflow-y: auto;
  letter-spacing: 0.0312rem;
  font-family: 'Quicksand', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  color: #3b3f5c;
}

:focus {
  outline: none;
}

::-moz-selection {
  /* Code for Firefox */
  color: #1b55e2;
  background: transparent;
}

::selection {
  color: #1b55e2;
  background: transparent;
}

p {
  margin-top: 0;
  margin-bottom: 0.625rem;
  color: #515365;
}

hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border-top: 1px solid #f1f2f3;
}

strong {
  font-weight: 600;
}

code {
  color: #e7515a;
}

/*Page title*/

.page-header {
  border: 0;
  margin: 0;
  display: flex;
  margin-top: 20px;
  border-bottom: 1px solid #e0e6ed;
  padding-bottom: 7px;
}

.page-title {
  position: relative;
  align-self: center;
  margin-right: 15px;

  &:before {
    content: '';
    height: 2px;
    width: 100%;
    background: #5c1ac3;
    position: absolute;
    border-radius: 50px;
    bottom: -8px;
  }

  h3 {
    margin: 0;
    margin-bottom: 0;
    font-size: 25px;
    color: #3b3f5c;
  }

  span {
    display: block;
    font-size: 11px;
    color: #888;
    font-weight: normal;
    display: block;
    font-size: 11px;
    color: #888;
    font-weight: normal;
  }
}

.page-header {
  .breadcrumb-one {
    align-self: flex-end;

    .breadcrumb {
      padding: 0;
      vertical-align: sub;
      background-color: transparent;
      margin-bottom: 0;
    }

    .breadcrumb-item {
      align-self: center;

      a {
        color: #888ea8;
        vertical-align: sub;
        font-size: 14px;
        font-weight: 500;

        svg {
          width: 18px;
          height: 18px;
          vertical-align: sub;
          fill: #888ea8;
        }
      }

      &.active a {
        color: #515365;
      }

      span {
        vertical-align: sub;
      }

      &.active {
        color: #5c1ac3;
        font-weight: 600;
        margin-left: 14px;
      }

      + .breadcrumb-item {
        padding: 0px;
        position: relative;

        &::before {
          color: #888ea8;
          content: ".";
          position: absolute;
          left: -10px;
          top: -6px;
          font-size: 20px;
          padding: 0;
        }
      }
    }
  }
}

.main-container {
  min-height: 100vh;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

#container.fixed-header {
  margin-top: 56px;
}

#content {
  width: 50%;
  flex-grow: 8;
  margin-top: 80px;
  margin-bottom: 0;
  margin-left: 150px;
  -webkit-transition: .3s ease all;
  transition: .3s ease all;
}

.main-container-fluid > .main-content > .container {
  float: left;
  width: 100%;
}

#content > .wrapper {
  -webkit-transition: margin ease-in-out .1s;
  -moz-transition: margin ease-in-out .1s;
  -o-transition: margin ease-in-out .1s;
  transition: margin ease-in-out .1s;
  position: relative;
}

.widget {
  padding: 0;
  margin-top: 0;
  margin-bottom: 0;
  border-radius: 6px;
  -webkit-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
  -moz-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
  box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
}

.layout-top-spacing {
  margin-top: 25px;
}

.layout-spacing {
  padding-bottom: 40px;
}

.layout-px-spacing {
  padding: 0 20px 0 20px !important;
  min-height: calc(100vh - 140px) !important;
}

.widget.box .widget-header {
  background: #fff;
  padding: 0px 8px 0px;
  border-top-right-radius: 6px;
  border-top-left-radius: 6px;
}

.row [class*="col-"] .widget .widget-header h4 {
  color: #3b3f5c;
  font-size: 17px;
  font-weight: 600;
  margin: 0;
  padding: 16px 15px;
}

.seperator-header {
  background: transparent;
  box-shadow: none;
  margin-bottom: 40px;
  border-radius: 0;

  h4 {
    color: #888ea8;
    margin-bottom: 0;
    display: inline-block;
    border: 2px dashed #888ea8;
    line-height: 1.4;
    padding: 3px 6px;
    font-size: 15px;
    font-weight: 600;
    border-radius: 4px;
    letter-spacing: 1px;
  }
}

.widget .widget-header {
  border-bottom: 0px solid #f1f2f3;

  &:before {
    display: table;
    content: "";
    line-height: 0;
  }

  &:after {
    display: table;
    content: "";
    line-height: 0;
    clear: both;
  }
}

.widget-content-area {
  padding: 20px;
  position: relative;
  background-color: #fff;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.content-area {
  max-width: 58.333333%;
  margin-left: 80px;
}


//  ===============================
//       _structure Imports 
//  ===============================

@import '_structure/_navbar';
@import '_structure/_sidebar';
@import '_structure/_footer';
@import '_structure/_media-query';


@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .navbar {
    padding: 12px 0 11px 20px;

    .navbar-item .nav-item form.form-inline input.search-form-control {
      padding-top: 8px;
    }
  }
}