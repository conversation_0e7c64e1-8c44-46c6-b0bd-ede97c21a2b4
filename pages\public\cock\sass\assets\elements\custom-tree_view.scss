//  =================
//      Imports
//  =================

@import '../../base/base';    // Base Variables

/*  Tree View   */

#myUL {
  list-style-type: none;

  ul {
    list-style-type: none;
  }

  margin: 0;
  padding: 0;
  color: $dark;
  font-weight: 600;
}

.caret {
  cursor: pointer;
  -webkit-user-select: none;

  /* Safari 3.1+ */
  -moz-user-select: none;

  /* Firefox 2+ */
  -ms-user-select: none;

  /* IE 10+ */
  user-select: none;
  font-size: 13px;
  color: $m-color_10;
  text-transform: capitalize;

  &.caret-down::before {
    content: url('data:image/svg+xml, <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 24 24" fill="none" stroke="%232b50ed" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-folder-plus"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path><line x1="12" y1="11" x2="12" y2="17"></line><line x1="9" y1="14" x2="15" y2="14"></line></svg>');
  }

  &::before {
    content: url('data:image/svg+xml, <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 24 24" fill="none" stroke="%232b50ed" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-folder-minus"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path><line x1="9" y1="14" x2="15" y2="14"></line></svg>');
    color: $m-color_6;
    display: inline-block;
    margin-right: 6px;
    vertical-align: sub;
  }
}

.nested {
  display: none;
  padding-left: 28px;

  li {
    margin-top: 8px;
    margin-bottom: 8px;
    color: $secondary;
    font-size: 13px;
  }
}

.active {
  display: block;
}

.file-tree {
  * {
    list-style-type: none;
  }

  margin-bottom: 0;
  padding-left: 20px;
  list-style: none;
  overflow: hidden;

  ul {
    list-style: none;
    display: none;
    padding-left: 30px;
  }

  li {
    position: relative;
    padding: 5px 0;
    border-radius: 0.25rem;
    cursor: auto;
    white-space: nowrap;
    font-size: 0.875rem;
    font-weight: 600;
    color: $m-color_10;
  }

  .file-tree-folder {
    -webkit-transition: all 0.2s;
    -moz-transition: all 0.2s;
    transition: all 0.2s;
    -webkit-transition-delay: 0s;
    -moz-transition-delay: 0s;
    transition-delay: 0s;
    cursor: pointer;

    &::before {
      content: "\e9b9";
      font-size: 1.25rem;
    }

    &::after {
      -webkit-transition: all 0.2s;
      -moz-transition: all 0.2s;
      transition: all 0.2s;
      -webkit-transition-delay: 0s;
      -moz-transition-delay: 0s;
      transition-delay: 0s;
      position: absolute;
      top: .4rem;
      left: -2em;
      content: "\f182";
      display: block;
      text-align: center;
      font-family: 'flaticon' !important;
      content: url('data:image/svg+xml, <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24" fill="none" stroke="%232b50ed" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-chevron-down"><polyline points="6 9 12 15 18 9"></polyline></svg>');
      font-size: 10px;
      color: $info;
    }

    &.open::after {
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      transform: rotate(180deg);
    }
  }

  li {
    &.file-tree-folder::before {
      display: inline-block;
      text-align: center;
      width: 30px;
      margin-right: .25rem;
      vertical-align: middle;
      font-size: 1rem;
      color: $info;
      content: url('data:image/svg+xml, <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24" fill="none" stroke="%232b50ed" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-folder"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>');
    }

    &::before {
      content: url('data:image/svg+xml, <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 24 24" fill="none" stroke="%232b50ed" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-file-text"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>');
      font-size: 1rem;
      color: $info;
      margin-right: 9px;
      position: relative;
      top: 2px;
    }
  }
}

@media (max-width: 767px) {
  .custo-radio {
    margin-top: 5px;
  }
}