/*
    ===========================
        Top Selling Product
    ===========================
*/

.widget-table-three {
  position: relative;

  h5 {
    font-weight: 700;
    font-size: 19px;
    letter-spacing: 1px;
    margin-bottom: 20px;
  }

  .widget-content {
    background: transparent;
  }

  .table {
    border-collapse: separate;
    border-spacing: 0 5px;
    margin-bottom: 0;

    > {
      thead > tr > th {
        &:first-child .th-content {
          margin-left: 10px;
        }

        &:last-child .th-content {
          text-align: right;
          margin-right: 10px;
        }

        text-transform: initial;
        font-weight: 600;
        border-top: none;
        background: $m-color_3;
        padding-top: 0;
        padding-bottom: 0;
        padding-right: 0;
        padding-left: 0;
        -webkit-transition: all 0.1s ease;
        transition: all 0.1s ease;
        padding: 10px 0 10px 15px;

        &:first-child {
          border-bottom-left-radius: 6px;
          border-top-left-radius: 6px;
        }

        &:last-child {
          border-bottom-right-radius: 6px;
          border-top-right-radius: 6px;
        }

        .th-content {
          color: $primary;
          font-weight: 600;
          font-size: 14px;
          letter-spacing: 1px;
        }

        &:nth-last-child(2) .th-content {}
      }

      tbody > tr {
        > td {
          border-top: none;
          padding-top: 0;
          padding-bottom: 0;
          padding-right: 0;
          padding-left: 0;
          -webkit-transition: all 0.1s ease;
          transition: all 0.1s ease;
        }

        &:hover {
          > td {
            transform: translateY(-1px) scale(1.01);
          }

          > td .td-content {
            color: $dark;
          }
        }

        background: transparent;

        > td .td-content {
          cursor: pointer;
          font-weight: 600;
          letter-spacing: 1px;
          color: $m-color_6;
        }

        > td {
          &:first-child {
            border-top-left-radius: 6px;
            padding: 10px 0px 10px 15px;
            border-bottom-left-radius: 6px;
          }

          &:last-child {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;

            .td-content {
              text-align: right;
              padding: 0 15px 0 0;
            }
          }
        }
      }
    }

    tr > td:nth-last-child(2) .td-content {
      padding: 10px 0 10px 15px;
    }

    .td-content {
      .discount-pricing {
        padding: 10px 0 10px 15px;
      }

      &.product-name {
        color: $m-color_9;
        letter-spacing: 1px;
      }

      img {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        margin-right: 10px;
      }

      .pricing {
        padding: 10px 0 10px 15px;
      }

      .tag {
        background: transparent;
        transform: none;
        font-weight: 600;
        letter-spacing: 2px;
        padding: 2px 5px;
        border-radius: 6px;
      }

      .tag-primary {
        color: $primary;
        border: 1px dashed $primary;
        background: $l-primary;
      }

      .tag-success {
        color: $m-color_14;
        border: 1px dashed $m-color_14;
        background: $l-success;
      }

      .tag-danger {
        color: $danger;
        border: 1px dashed $danger;
        background: $l-danger;
      }

      a {
        padding: 0;
        font-size: 13px;
        background: transparent;
        transform: none;
        letter-spacing: 1px;
        border-bottom: 1px dashed $m-color_4;
      }
    }
  }
}