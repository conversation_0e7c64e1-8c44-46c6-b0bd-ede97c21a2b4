//  =================
//      Imports
//  =================

@import '../../base/base';    // Base Variables

.noUi-target {
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid $m-color_3;
}

.noUi-horizontal {
  height: 8px;

  .noUi-handle {
    width: 25px;
    height: 20px;
    top: -8px;
  }
}

.noUi-handle {
  &:after, &:before {
    display: none;
  }
}

.noUi-connect {
  background: $primary;
}

.noUi-tooltip {
  border: 1px solid $m-color_3;
  border-radius: 8px;
  background: $white;
  color: $black;
  padding: 6px 14px;
  font-size: 13px;
  font-weight: 600;
}

.noUi-horizontal .noUi-tooltip {
  bottom: 148%;
}

.example-val {
  font-weight: 700;
  font-size: 14px;
  color: $primary;

  span.precentage-val {
    display: inline-block;
    background: #ffffff;
    border-radius: 5px;
    color: $dark;
    border: 1px solid $m-color_3;
    padding: 4px 6px;
    font-size: 14px;
  }
}