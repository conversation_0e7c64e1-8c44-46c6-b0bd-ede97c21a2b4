//  =================
//      Imports
//  =================

@import '../../base/base';    // Base Variables

.avatar {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 3rem;
  font-size: 1rem;
}

.avatar--group {
  display: inline-flex;
  margin-right: 15px;
}

.avatar {
  img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
  }

  .avatar-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: $m-color_4;
    color: $white;
  }
}

.avatar--group {
  .avatar-xl {
    margin-left: -1.28125rem;
  }

  .avatar {
    margin-left: -.75rem;
  }

  img, .avatar .avatar-title {
    border: 2px solid #ffffff;
  }
}

.avatar-xl {
  width: 5.125rem;
  height: 5.125rem;
  font-size: 1.70833rem;
}

.avatar-lg {
  width: 4rem;
  height: 4rem;
  font-size: 1.33333rem;
}

.avatar-sm {
  width: 2.5rem;
  height: 2.5rem;
  font-size: .83333rem;
}

/*
	Indicators
*/

.avatar-indicators:before {
  content: "";
  position: absolute;
  bottom: 1%;
  right: 5%;
  width: 28%;
  height: 28%;
  border-radius: 50%;
  border: 3px solid $white;
}

.avatar-offline:before {
  background-color: $m-color_4;
}

.avatar-online:before {
  background-color: $m-color_14;
}

.avatar {
  &.translateY-axis {
    img, .avatar-title {
      -webkit-transition: all 0.35s ease;
      transition: all 0.35s ease;
    }

    img:hover, .avatar-title:hover {
      -webkit-transform: translateY(-5px) scale(1.02);
      transform: translateY(-5px) scale(1.02);
    }
  }

  &.translateX-axis {
    img, .avatar-title {
      -webkit-transition: all 0.35s ease;
      transition: all 0.35s ease;
    }

    img:hover, .avatar-title:hover {
      -webkit-transform: translateX(5px) scale(1.02);
      transform: translateX(5px) scale(1.02);
    }
  }
}