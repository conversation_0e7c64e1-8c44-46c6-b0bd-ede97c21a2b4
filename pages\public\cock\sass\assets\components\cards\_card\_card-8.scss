/*
	Component Card 8
*/

.component-card_8 {
  background: $white;
  border: 1px solid $m-color_3;
  width: 24rem;
  margin: 0 auto;
  -webkit-box-shadow: 4px 6px 10px -3px $m-color_4;
  box-shadow: 4px 6px 10px -3px $m-color_4;

  .progress-order {
    .progress-order-header {
      h6 {
        color: $m-color_10;
        font-weight: 600;
        font-size: 16px;
      }

      span.badge {
        font-size: 12px;
        padding: 6px 10px;
        color: $primary;
        background-color: $l-primary;
      }
    }

    .progress-order-body {
      ul li {
        img {
          width: 40px;
          height: 40px;
        }

        &.badge-notify {
          position: relative;

          .notification {
            position: absolute;
            top: -15px;
            right: -34px;

            span.badge {
              background-color: $white;
              color: $primary;
              box-shadow: 0 0 20px 0 #d0d0d0;
              font-size: 12px;
              padding: 4px 7px;
            }
          }
        }
      }

      .p-o-percentage {
        font-size: 14px;
        color: $primary;
      }

      .progress {
        height: 6px;
        border-radius: 30px;

        .p-o-progress {
          border-radius: 30px;
        }
      }
    }
  }
}

.pending-order {
  padding: 22px 20px;
}