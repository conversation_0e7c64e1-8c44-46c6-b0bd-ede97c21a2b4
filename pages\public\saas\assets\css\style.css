@charset "UTF-8";
/*----------------------------------------------------
@File: Default Styles
@Author: 
@URL: 

This file contains the styling for the actual theme, this
is the file you need to edit to change the look of the
theme.
---------------------------------------------------- */
/*=====================================================================
@Template Name: 
@Author: Themexriver.

**Css Table Of Content**
1- App Landing--
  *1- Global Area
  *2- Header Area
  *3- Banner Area
  *4- Feature Area
  *5- Service Area
  *6- Funfact Area
  *7- How this work Area
  *8- App Download Area
  *9- Team Area
  *10- FAQ area
  *11- App Screenshoot Area
  *12- Testimonial Area
  *13- Partner Area
  *14- Newslatter Area
  *15- Footer Area
2- Digital SEO --
  *1- Global Area
  *2- Header Area
  *3- Banner Area
  *4- Service Area
  *5- ABout Area
  *6- Newslatter Area
  *7- Mission Area
  *8- Pricing Plan Area
  *9= Testimonial Area
  *10- Case Study Area
  *11- Call to Action Area
  *12- Blog Area
  *13- Footer
3- SaaS Modern--
  *1- Global Area
  *2- Header Area
  *3- Banner Area
  *4- Featured Area
  *5- ABout Area
  *6- Partner Area
  *7- Team Area
  *8- Testimonial Area
  *9- Newslatter Area 
4- SaaS Classic --
  *1- Global Area
  *2- Header Area
  *3- Banner Area
  *4- Service Area
  *5- ABout Area
  *6- Feature Area
  *7- Team Area
  *8- FAQ Area
  *9= Pricing Area
  *10- Footer
 5- StartUp Agency --
  *1- Global Area
  *2- Header Area
  *3- Banner Area
  *4- Feature Area
  *5- ABout Area
  *6- Work Process Area
  *7- Portfolio Area
  *8- Testimonial Area
  *9= Partner Area
  *10- Footer 
 2- Digital Agency --
  *1- Global Area
  *2- Header Area
  *3- Banner Area
  *4- Service Area
  *5- CountDown Area
  *6- About Area
  *7- Experience Area
  *8- PortFolio Area
  *9= Team Area
  *10- Testimonial Area
  *11- Blog Area
  *12- Newslatter Area
  *13- Footer 
  =====================================================================*/
  @import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@400;500;600;700&display=swap");
  @import url("https://fonts.googleapis.com/css?family=Poppins:400,500,600,700|Roboto:100,500,300,400,700&display=swap");
  @import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
  /*----------------------------------------------------*/
  /*App landing global area*/
  /*----------------------------------------------------*/
  .app-eight-home {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    font-size: 16px;
    line-height: 1.4;
    font-family: "Circular Std Book";
    -moz-osx-font-smoothing: antialiased;
    -webkit-font-smoothing: antialiased;
  }
  .app-eight-home::selection {
    color: #ffffff;
    background-color: #6e3ebf;
  }
  .app-eight-home::-moz-selection {
    color: #ffffff;
    background-color: #6e3ebf;
  }
  .app-eight-home .container {
    max-width: 1200px;
  }
  .ul-li ul {
    margin: 0;
    padding: 0;
  }
  .ul-li ul li {
    list-style: none;
    display: inline-block;
  }
  .ul-li-block ul {
    margin: 0;
    padding: 0;
  }
  .ul-li-block ul li {
    list-style: none;
    display: block;
  }
  div#preloader {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99999;
    width: 100%;
    height: 100%;
    overflow: visible;
  }
  .ei-preloader {
    background-color: #fff;
    background: #f1f2f3 url("../img/app-landing/pre.svg") no-repeat center center;
  }
  .container {
    max-width: 1200px;
  }
  [data-background] {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
  }
  a {
    color: inherit;
    text-decoration: none;
  }
  a:hover,
  a:focus {
    text-decoration: none;
  }
  img {
    max-width: 100%;
    height: auto;
  }
  section {
    overflow: hidden;
  }
  button {
    cursor: pointer;
  }
  .form-control:focus,
  button:visited,
  button.active,
  button:hover,
  button:focus,
  input:visited,
  input.active,
  input:hover,
  input:focus,
  textarea:hover,
  textarea:focus,
  a:hover,
  a:focus,
  a:visited,
  a.active,
  select,
  select:hover,
  select:focus,
  select:visited {
    outline: none;
    box-shadow: none;
    text-decoration: none;
    color: inherit;
  }
  .form-control {
    box-shadow: none;
  }
  .relative-position {
    position: relative;
  }
  .pera-content p {
    margin-bottom: 0;
  }
  .appeight-headline h1,
  .appeight-headline h2,
  .appeight-headline h3,
  .appeight-headline h4,
  .appeight-headline h5,
  .appeight-headline h6 {
    margin: 0;
    font-family: "Poppins";
  }
  .ei-scrollup {
    width: 55px;
    right: 20px;
    z-index: 5;
    height: 55px;
    bottom: 20px;
    display: none;
    position: fixed;
    border-radius: 100%;
    line-height: 55px;
    background-image: linear-gradient(-45deg, #a80202 32%, #6b2c94 100%);
  }
  .ei-scrollup i {
    color: #fff;
    font-size: 20px;
  }
  .eight-section-title {
    margin: 0 auto;
  }
  .eight-section-title .eg-title-tag {
    color: #000000;
    font-size: 17px;
    font-weight: 500;
    position: relative;
    font-family: "Circular Std";
  }
  .eight-section-title .eg-title-tag i:nth-child(1) {
    height: 5px;
    width: 5px;
    background-color: #4ce7f3;
  }
  .eight-section-title h2 {
    font-size: 36px;
    font-weight: 700;
    padding: 15px 0px 12px;
    line-height: 1.306;
  }
  .eight-section-title h2 span {
    font-weight: 400;
  }
  .eight-section-title .square-shape {
    width: 40px;
    height: 35px;
    left: -52px;
    top: 0;
    position: absolute;
  }
  .eight-section-title .square-shape i {
    height: 9px;
    width: 9px;
    position: absolute;
    top: 0;
    left: 0;
  }
  .eight-section-title .square-shape i:nth-child(1) {
    top: -8px;
    width: 5px;
    height: 5px;
    left: 20px;
    background-color: #4ce7f3;
  }
  .eight-section-title .square-shape i:nth-child(2) {
    width: 10px;
    left: 28px;
    height: 10px;
    background-color: #4ce7f3;
  }
  .eight-section-title .square-shape i:nth-child(3) {
    height: 17px;
    width: 17px;
    background-color: #a80202;
  }
  .eight-section-title .square-shape i:nth-child(4) {
    top: 10px;
    left: 10px;
    width: 12px;
    height: 12px;
    background-color: #a80202;
  }

  .eight-section-title p {
    font-size: 17px;
    line-height: 1.647;
  }

  @keyframes scale {
    from {
      transform: scale(0.9);
      -webkit-transform: scale(0.9);
    }
    to {
      transform: scale(1.08);
      -webkit-transform: scale(1.08);
    }
  }
  @keyframes left-right-move {
    0% {
      transform: translateX(-100px);
    }
    50% {
      transform: translateX(-10px);
    }
    100% {
      transform: translateX(-100px);
    }
  }
  /*---------------------------------------------------- */
  /*global area*/
  /*----------------------------------------------------*/
  .feature-eight-section .eight-feature-box .feature-icon8 i,
  .ei-service-icon-text .ei-service-icon i,
  .eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .eg-how-work-icon i {
    display: block;
    background-image: linear-gradient(173deg, #a80202 30%, #50c7f5 67%, #72aaff 99%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .ei-service-icon-text:hover .ei-service-icon i,
  .eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text:hover .eg-how-work-icon i {
    display: block;
    background: -webkit-gradient(left top, right top, color-stop(0%, #fff), color-stop(100%, #fff));
    background: -ms-linear-gradient(left, #fff 0%, #fff 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .ei-service-icon-text .ei-service-icon:after,
  .eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .eg-how-work-icon:after {
    position: absolute;
    content: "";
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0;
    width: 100%;
    z-index: -1;
    transition: 0.3s all ease-in-out;
    transform: scale(0.5);
    border-radius: 100%;
    background-image: -ms-linear-gradient(173deg, #4cd0ff 0%, #617af4 53%, #a80202 99%);
  }
  @keyframes fadeFromLeft {
    0% {
      opacity: 0;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  @keyframes fadeFromRight {
    0% {
      opacity: 0;
      transform: translateX(20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  @keyframes fadeFromUp {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  .fadeFromUp {
    animation-name: fadeFromUp;
  }

  .fadeFromRight {
    animation-name: fadeFromRight;
  }

  .fadeFromLeft {
    animation-name: fadeFromLeft;
  }

  /*---------------------------------------------------- */
  /*Header area*/
  /*----------------------------------------------------*/
  .main-header-eight {
    top: 20px;
    width: 100%;
    z-index: 10;
    position: absolute;
  }

  .main-header-eight .appheader-content {
    padding: 0px 280px;
  }

  .main-header-eight .appheader-content .site-logo {
    margin-right: 255px;
  }

  .main-header-eight .appheader-content .navigation-eight {
    color: #fff;
    font-weight: 500;
    display: inline-block;
    font-family: "Poppins";
    padding-top: 10px;
  }

  .main-header-eight .appheader-content .navigation-eight li {
    margin-right: 25px;
  }

  .main-header-eight .appheader-content .navigation-eight li a {
    display: inline;
    padding: 28px 5px;
    position: relative;
  }

  .main-header-eight .appheader-content .navigation-eight li a:after {
    left: auto;
    right: 0;
    top: 0;
    height: 4px;
    content: "";
    width: 0%;
    position: absolute;
    border-radius: 50px;
    background-color: #49eff2;
    transition: 0.3s all ease-in-out;
  }

  .main-header-eight .appheader-content .navigation-eight li a:hover:after {
    width: 100%;
    right: auto;
    left: 0;
  }

  .main-header-eight .appheader-content .navigation-eight li a.active:after {
    width: 100%;
    right: auto;
    left: 0;
  }

  .main-header-eight .appheader-content .sign-up-btn-eight {
    height: 40px;
    width: 110px;
    line-height: 40px;
    border-radius: 20px;
    justify-content: center;
    background-color: #29f5eb;
    transition: 0.3s all ease-in-out;
  }

  .main-header-eight .appheader-content .sign-up-btn-eight a {
    font-weight: 600;
    display: block;
    width: 100%;
  }

  .main-header-eight .appheader-content .sign-up-btn-eight:hover {
    background-color: #009cff;
  }

  .main-header-eight .appheader-content .sign-up-btn-eight:hover a {
    color: #fff;
  }

  .main-header-eight .appheader-content .h-eight-social {
    padding-top: 10px;
    margin-left: 40px;
  }

  .main-header-eight .appheader-content .h-eight-social li {
    margin-left: 18px;
    color: #fff;
    transition: 0.3s all ease-in-out;
  }

  .main-header-eight .appheader-content .h-eight-social li:hover {
    transform: translateY(-5px);
  }

  .main-header-eight .appheader-content .dropdown {
    position: relative;
  }

  .main-header-eight .appheader-content .dropdown:after {
    content: "";
    position: absolute;
    right: -10px;
    top: 0px;
    transition: 0.3s all ease-in-out;
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
  }

  .main-header-eight .appheader-content .dropdown .dropdown-menu {
    top: 65px;
    left: 0;
    opacity: 0;
    z-index: 100;
    margin: 0px;
    padding: 0px;
    height: auto;
    width: 200px;
    display: block;
    border: none;
    padding: 10px 0px 0px;
    visibility: hidden;
    position: absolute;
    background-color: #fff;
    transition: all 0.4s ease-in-out;
    border: 2px solid #73299a;
    box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
    border-radius: 5px;
  }

  .main-header-eight .appheader-content .dropdown .dropdown-menu li {
    width: 100%;
    padding: 10px 15px;
    border-bottom: 1px solid #e5e5e5;
  }

  .main-header-eight .appheader-content .dropdown .dropdown-menu li a {
    color: #343434;
    font-size: 14px;
    padding: 10px 0px;
    position: relative;
    transition: 0.3s all ease-in-out;
  }

  .main-header-eight .appheader-content .dropdown .dropdown-menu li a:after {
    left: 0;
    top: 15px;
    width: 8px;
    height: 8px;
    content: "";
    position: absolute;
    border-radius: 100%;
    transform: scale(0);
    background-color: #73299a;
    transition: 0.3s all ease-in-out;
  }

  .main-header-eight .appheader-content .dropdown .dropdown-menu li a:hover {
    padding-left: 15px;
  }

  .main-header-eight .appheader-content .dropdown .dropdown-menu li a:hover:after {
    transform: scale(1);
  }

  .main-header-eight .appheader-content .dropdown:hover .dropdown-menu {
    top: 48px;
    opacity: 1;
    visibility: visible;
  }

/* -------------------------------- 
Sticky Menu
-------------------------------- */
.eisticky-menu-bg-overlay {
  background-color: #a80202;
  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  position: fixed;
  top: 0px;
  padding: 20px 0px;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
}

.main-header-eight.eisticky-menu-bg-overlay {
  z-index: 9;
  top: 0px;
  box-shadow: 0 0 20px -10px rgba(0, 0, 0, 0.8);
}

/*---------------------------------------------------- */
/*banner area*/
/*----------------------------------------------------*/
.eight-banner-section {
  overflow: visible;
  background: #6d8cf8;
  padding: 170px 0px 250px;
  background: linear-gradient(to right, #c12dd1, #6d8cf8);
}

.eight-banner-section:after {
  position: absolute;
  content: "";
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-image: url(../img/app-landing/shape/bl-shape.png);
}

.eight-banner-section .eight-banner-content {
  position: relative;
  z-index: 5;
}

.eight-banner-section .eight-banner-content .ei-banner-mbl-mockup {
  position: absolute;
  top: 0;
  right: 0;
}

.eight-banner-section .eight-banner-content .banner-content-box {
  max-width: 650px;
}

.eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn {
  padding: 45px 0px 25px 0px;
}

.eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn a {
  font-size: 15px;
  font-weight: 700;
  font-family: "Poppins";
  display: inline-block;
  transition: 0.3s all ease-in-out;
}

.eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn a:nth-child(1) {
  height: 55px;
  color: #fff;
  width: 175px;
  line-height: 55px;
  text-align: center;
  border-radius: 50px;
  margin-right: 20px;
  border-top-right-radius: 0;
  background-color: #ff00d6;
}

.eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn a:nth-child(1) i {
  top: 5px;
  color: #000;
  font-size: 25px;
  position: relative;
  margin-right: 10px;
}

.eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn a:nth-child(1):hover {
  background-color: #009cff;
  border-top-right-radius: 50px;
  border-bottom-left-radius: 0px;
}

.eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn a:nth-child(2) {
  color: #4ce7f3;
  position: relative;
}

.eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn a:nth-child(2) span {
  color: #fff;
  margin-right: 2px;
}

.eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn a:nth-child(2):after {
  width: 0%;
  content: "";
  position: absolute;
  height: 1px;
  background-color: #4ce7f3;
  bottom: -2px;
  left: auto;
  right: 0;
  transition: 0.3s all ease-in-out;
}

.eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn a:nth-child(2):hover:after {
  left: 22px;
  width: 80%;
  right: auto;
}

.eight-banner-section .eight-banner-content h1 {
  color: #fff;
  font-size: 72px;
  font-weight: 700;
  line-height: 1.083;
  padding-bottom: 20px;
}

.eight-banner-section .eight-banner-content h1 span {
  color: #000;
}

.eight-banner-section .eight-banner-content p {
  font-size: 19px;
  color: #fff;
}

.eight-banner-section .eight-banner-content .ei-banner-review ul {
  float: left;
  margin-right: 20px;
}

.eight-banner-section .eight-banner-content .ei-banner-review ul li {
  margin-right: 3px;
}

.eight-banner-section .eight-banner-content .ei-banner-review ul li i {
  color: #f6b91c;
  font-size: 15px;
}

.eight-banner-section .eight-banner-content .ei-banner-review p {
  font-size: 14px;
}

.eight-banner-section .eight-banner-content .ei-banner-review p span {
  font-family: "Poppins";
  font-weight: 700;
  color: #000;
}
@keyframes move_wave {
  0% {
    transform: translateX(0) translateZ(0) scaleY(1);
  }
  50% {
    transform: translateX(-25%) translateZ(0) scaleY(0.55);
  }
  100% {
    transform: translateX(-50%) translateZ(0) scaleY(1);
  }
}
.waveWrapper {
  overflow: hidden;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 0;
  margin: auto;
}

.waveWrapperInner {
  position: absolute;
  width: 100%;
  overflow: hidden;
  height: 100%;
  bottom: -1px;
  background: #6d8cf8;
 /* background: linear-gradient(to right, #c12dd1, #6d8cf8); */
  background: linear-gradient(to right, #a80202, #6d8cf8);

}

.bgTop {
  z-index: 15;
  opacity: 0.5;
}

.bgMiddle {
  z-index: 10;
  opacity: 0.75;
}

.bgBottom {
  z-index: 5;
}

.wave {
  position: absolute;
  left: 0;
  width: 200%;
  height: 100%;
  background-repeat: repeat no-repeat;
  background-position: 0 bottom;
  transform-origin: center bottom;
}

.waveTop {
  background-size: 50% 100px;
}

.waveAnimation .waveTop {
  animation: move-wave 3s;
  -webkit-animation: move-wave 3s;
  animation-delay: 1s;
}

.waveMiddle {
  background-size: 50% 120px;
}

.waveAnimation .waveMiddle {
  animation: move_wave 10s linear infinite;
}

.waveBottom {
  background-size: 50% 100px;
}

.waveAnimation .waveBottom {
  animation: move_wave 15s linear infinite;
}

.cd-headline.clip span {
  display: inline-block;
  padding-bottom: 10px;
}

.cd-headline.clip .cd-words-wrapper {
  display: inline-block;
  position: relative;
  text-align: left;
  vertical-align: top;
}

.cd-headline.clip .cd-words-wrapper::after {
  /* line */
  content: "";
  position: absolute;
  top: 10px;
  right: 0;
  width: 2px;
  height: 70px;
  background-color: #aebcb9;
}

.cd-headline.clip b {
  opacity: 0;
}

.cd-headline.clip b.is-visible {
  opacity: 1;
}

.cd-words-wrapper {
  display: inline-block;
  position: relative;
  text-align: left;
}

.cd-words-wrapper b {
  display: inline-block;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  white-space: nowrap;
}

.cd-words-wrapper b.is-visible {
  opacity: 1;
  position: relative;
}

.no-js .cd-words-wrapper b {
  opacity: 0;
}

/*---------------------------------------------------- */
/*Featured area*/
/*----------------------------------------------------*/
@keyframes rotate {
  0% {
    top: -10px;
    left: -10px;
  }
  25% {
    top: 0px;
    left: 0px;
  }
  50% {
    top: 0px;
    left: 0px;
  }
  75% {
    top: 5px;
    left: 5px;
  }
  100% {
    top: -10px;
    left: -10px;
  }
}
.feature-eight-section {
  padding: 165px 0px 120px;
}

.feature-eight-section .ei-feature-shape {
  right: 0;
  top: 150px;
  z-index: -1;
  position: absolute;
}

.feature-eight-section .eight-section-title {
  max-width: 500px;
  position: relative;
}


.feature-eight-section .eight-feature-content {
  padding-top: 70px;
}

.feature-eight-section .eight-feature-box {
  border-radius: 20px;
  padding: 40px 30px 53px;
  background-color: #e9ebf7;
  transition: 0.3s all ease-in-out;
}

.feature-eight-section .eight-feature-box .feature-icon8 {
  width: 98px;
  z-index: 1;
  height: 98px;
  margin: 0 auto;
  line-height: 98px;
  margin-bottom: 34px;
  border-radius: 25px;
  background-color: #ffffff;
  box-shadow: -0.707px 0.707px 10px 0px rgba(43, 1, 68, 0.1);
}

.feature-eight-section .eight-feature-box .feature-icon8:before,
.feature-eight-section .eight-feature-box .feature-icon8:after {
  content: "";
  z-index: -2;
  border-radius: 30%;
  mix-blend-mode: multiply;
  height: 110px;
  opacity: 0;
  left: 0px;
  top: 0px;
  width: 110px;
  position: absolute;
  transition: 0.3s all ease-in-out;
}

.feature-eight-section .eight-feature-box .feature-icon8:before {
  animation: rotate 1.8s linear infinite;
  background: #33f1ff;
}

.feature-eight-section .eight-feature-box .feature-icon8:after {
  animation: rotate 1.2s linear reverse infinite;
  background: #e933ff;
}

.feature-eight-section .eight-feature-box .feature-icon8 i {
  font-size: 50px;
  font-weight: 500;
}

.feature-eight-section .eight-feature-box .feature-icon8 .ei-icon-bg {
  height: 100%;
  width: 100%;
  position: absolute;
  background-color: #fff;
  top: 0;
  left: 0;
  z-index: -1;
  border-radius: 25px;
}

.feature-eight-section .eight-feature-box .ei-feature-more {
  position: absolute;
  background-color: #4ce7f3;
  width: 50px;
  height: 50px;
  border-radius: 100%;
  line-height: 50px;
  transition: 0.3s all ease-in-out;
  left: 0;
  right: 0;
  opacity: 0;
  visibility: hidden;
  bottom: 0px;
  margin: 0 auto;
}

.feature-eight-section .eight-feature-box .feature-text8 h3 {
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 13px;
}

.feature-eight-section .eight-feature-box .feature-text8 p {
  line-height: 1.5;
}

.feature-eight-section .eight-feature-box:hover {
  background-color: #fff;
  box-shadow: 0px 25px 38px 0px rgba(43, 1, 68, 0.17);
}

.feature-eight-section .eight-feature-box:hover .feature-icon8:before,
.feature-eight-section .eight-feature-box:hover .feature-icon8:after {
  display: block;
  opacity: 1;
}

.feature-eight-section .eight-feature-box:hover .ei-feature-more {
  opacity: 1;
  bottom: -25px;
  visibility: visible;
}

/*---------------------------------------------------- */
/*Service area*/
/*----------------------------------------------------*/
@keyframes spining {
  from {
    transform: rotate(0);
    -webkit-transform: rotate(0);
  }
  to {
    transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
  }
}
.eight-service-section {
  z-index: 1;
  padding: 135px 0px 140px;
  background-color: #eceef6;
}

.eight-service-section:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 50%;
  z-index: -2;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50% 50%;
  background-image: url(../img/app-landing/shape/s-shape2.png);
}

.eight-service-section:after {
  top: -5px;
  right: 0;
  content: "";
  z-index: -1;
  height: 640px;
  width: 780px;
  position: absolute;
  z-index: -2;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url(../img/app-landing/shape/s-shape1.png);
}

.eight-service-section .s-shape-bg1 {
  left: -95px;
  bottom: -15px;
  position: absolute;
  z-index: -1;
}

.eight-service-section .s-shape-bg2 {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  z-index: -2;
  transform: translateY(-50%);
}

.eight-service-section .eight-service-slide {
  width: 66.667%;
  float: left;
  position: relative;
  z-index: 1;
}

.eight-service-section .eight-service-slide .ei-service-slide-btn {
  margin-left: 265px;
}

.eight-service-section .eight-service-slide a {
  display: block;
  cursor: pointer;
}

.eight-service-section .eight-service-slide a:nth-child(1) {
  margin-bottom: 65px;
}

.eight-service-section .eight-service-slide a:nth-child(2) {
  margin-bottom: 55px;
  transform: translateX(-10px);
}

.eight-service-section .eight-service-slide a:nth-child(3) {
  transform: translateX(90px);
}

.eight-service-section .eight-service-text {
  width: 33.333333%;
  float: right;
  padding: 85px 0px 0px 70px;
}

.eight-service-section .eight-service-text:before {
  content: "";
  top: -180px;
  z-index: -1;
  left: -150px;
  width: 678px;
  height: 678px;
  position: absolute;
  animation: spining 10s linear infinite;
  background-image: url(../img/app-landing/shape/s-shape5.png);
}

.eight-service-section .eight-service-text h2 {
  color: #fff;
  font-size: 45px;
  font-weight: 700;
}

.eight-service-slide {
  width: 500px;
  padding-right: 70px;
}

.ei-service-icon-text .ei-service-icon {
  width: 108px;
  height: 108px;
  margin-left: 60px;
  position: relative;
  line-height: 108px;
  border-radius: 100%;
  overflow: hidden;
  z-index: 1;
  background-color: #e9ebf7;
  box-shadow: 0px 16px 20px 0px rgba(43, 1, 68, 0.11);
}

.ei-service-icon-text .ei-service-icon i {
  font-size: 55px;
}

.ei-service-icon-text .ei-service-text {
  max-width: 300px;
}

.ei-service-icon-text .ei-service-text h3 {
  font-size: 24px;
  font-weight: 700;
  padding-bottom: 16px;
}

.ei-service-icon-text .ei-service-text p {
  color: #5e5e5e;
  overflow: hidden;
  line-height: 1.647;
}

.ei-service-icon-text:hover .ei-service-icon:after {
  opacity: 1;
  transform: scale(1);
}

.ei-service-icon-text:hover .ei-service-icon i {
  color: #fff;
}

.ei-service-slide-mbl {
  position: absolute;
  right: -250px;
  top: -75px;
  width: 245px;
  height: 505px;
  margin: 0 auto;
  padding: 17px 19px;
  background-repeat: no-repeat;
}

.ei-service-slide-mbl .bx-controls-direction {
  display: none;
}

.ei-service-slide-mbl .image {
  border-radius: 18px;
  overflow: hidden;
}

/*---------------------------------------------------- */
/*Funfact area*/
/*----------------------------------------------------*/
.eg-fun-fact-section {
  padding: 20px 0px 110px;
}

.eg-fun-fact-section .fn-bg-shape {
  bottom: 0;
  left: 0;
}

.eg-fun-fact-section .eg-funfact-text {
  max-width: 465px;
}

.eg-fun-fact-section .eg-funfact-text .eight-section-title .eg-title-tag {
  font-size: 14px;
  margin-left: 55px;
}

.eg-fun-fact-section .eg-funfact-text .eight-section-title h2 {
  padding-bottom: 15px;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter {
  display: inline-block;
  margin-top: 40px;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter:after {
  left: 0;
  right: 0;
  top: 55%;
  width: 1px;
  content: "";
  height: 85%;
  margin: 0 auto;
  position: absolute;
  background-color: #dde0ee;
  transform: translateY(-50%);
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter:before {
  position: absolute;
  content: "";
  width: 90%;
  height: 1px;
  top: 50%;
  left: 0;
  right: 0;
  margin: 0 auto;
  transform: translateY(-50%);
  background-color: #dde0ee;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number {
  width: 50%;
  float: left;
  padding: 25px 0px 30px;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number .odometer {
  font-size: 50px;
  font-family: "Poppins";
  font-weight: 600;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number .odometer-formatting-mark {
  display: none;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number p {
  font-size: 26px;
  max-width: 130px;
  margin: 0 auto;
  line-height: 1.231;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number strong {
  top: 8px;
  line-height: 1;
  font-size: 50px;
  font-weight: 600;
  position: relative;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number:nth-child(1) .odometer,
.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number:nth-child(1) strong {
  color: #a80202;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number:nth-child(2) .odometer,
.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number:nth-child(2) strong {
  color: #a80202;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number:nth-child(3) .odometer,
.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number:nth-child(3) strong {
  color: #4ce7f3;
}

.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number:nth-child(4) .odometer,
.eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number:nth-child(4) strong {
  color: #009cff;
}

.eg-fun-fact-section .eg-fun-fact-mockup {
  top: 60px;
  left: -195px;
  position: absolute;
}

.eg-fun-fact-section .eg-fun-fact-mockup .fn-shape {
  position: absolute;
}

.eg-fun-fact-section .eg-fun-fact-mockup .fn-shape-item1 {
  top: 130px;
  left: -20px;
}

.eg-fun-fact-section .eg-fun-fact-mockup .fn-shape-item2 {
  right: 0;
  top: -25px;
}

.eg-fun-fact-section .eg-fun-fact-mockup .fn-shape-item3 {
  top: -35px;
  left: 140px;
  z-index: -1;
}

.eg-fun-fact-section .eg-fun-fact-mockup .fn-shape-item4 {
  z-index: -1;
  right: 35px;
  bottom: 105px;
}

/*---------------------------------------------------- */
/*How work area*/
/*----------------------------------------------------*/
.eg-how-work-section {
  padding-bottom: 135px;
  overflow: visible;
}

.eg-how-work-section .how-work-bg-shape {
  top: -185px;
  z-index: -1;
}

.eg-how-work-section .eight-section-title {
  padding-bottom: 18px;
}

.eg-how-work-section .eight-section-title .eg-title-tag {
  margin-left: 55px;
  font-size: 14px;
}

.eg-how-work-section .mCustomScrollBox {
  left: -30px;
}

.eg-how-work-section .mCS-autoHide > .mCustomScrollBox > .mCSB_scrollTools,
.eg-how-work-section .mCS-autoHide > .mCustomScrollBox ~ .mCSB_scrollTools {
  opacity: 1;
}

.eg-how-work-section .how-work-scroller {
  height: 310px;
}

.eg-how-work-section .eg-how-work-content {
  padding: 0 10px 0px 30px;
}

.eg-how-work-section .ei-how-work-content-item {
  padding-left: 180px;
}

.eg-how-work-section .ei-how-work-content-item .mCSB_draggerContainer {
  max-height: 290px;
}

.eg-how-work-section .ei-how-work-content-item .mCSB_scrollTools .mCSB_draggerRail {
  width: 13px;
  background-color: #e8e9f3;
}

.eg-how-work-section .ei-how-work-content-item .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  width: 13px;
  background-color: #a80202;
}

.eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text {
  margin-bottom: 25px;
  background-color: #fff;
  padding: 20px 50px 30px 20px;
  box-shadow: 0px 8px 9px 0px rgba(7, 7, 56, 0.15);
}

.eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .eg-how-work-icon {
  height: 65px;
  width: 65px;
  z-index: 1;
  overflow: hidden;
  line-height: 65px;
  margin-right: 35px;
  position: relative;
  border-radius: 100%;
  background-color: #f1f2fa;
  box-shadow: 0px 13px 24px 0px rgba(43, 1, 68, 0.17);
}

.eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .eg-how-work-icon i {
  font-size: 30px;
  font-weight: 500;
}

.eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .eg-how-work-icon .far {
  line-height: 65px;
}

.eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .eg-how-work-text h3 {
  color: #000000;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 13px;
}

.eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .scroller-no {
  left: -35px;
  bottom: 5px;
  z-index: -1;
  color: #dad2df;
  line-height: 1;
  font-size: 123px;
  font-weight: 700;
  position: absolute;
  font-family: "Playfair Display";
  transition: 0.3s all ease-in-out;
}

.eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text:hover .eg-how-work-icon:after {
  opacity: 1;
  transform: scale(1);
}

.eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text:hover .eg-how-work-icon i {
  color: #fff;
}

.eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text:hover .scroller-no {
  color: #7c0dbd;
}

.eg-how-work-section .how-work-mockup {
  padding-left: 60px;
}

.eg-how-work-section .how-work-mockup img {
  max-height: 500px;
}

.eg-how-work-section .how-work-mockup .hw-shape1 {
  top: -70px;
  right: 120px;
  z-index: -1;
}

.eg-how-work-section .how-work-mockup .hw-shape2 {
  left: 30px;
  z-index: -1;
  bottom: -15px;
}

/*---------------------------------------------------- */
/*App Download area*/
/*----------------------------------------------------*/
.ei-appdownload-section {
  padding: 100px 0px 50px;
  z-index: 1;
}

.ei-appdownload-section .ei-download-btn .download-icon {
  width: 40px;
  color: #fff;
  height: 40px;
  font-size: 26px;
  line-height: 40px;
  border-radius: 100%;
  margin-right: 15px;
  text-align: center;
  background-color: #ffa800;
  box-shadow: rgba(7, 7, 56, 0.15) 0px 8px 9px 0px;
}

.ei-appdownload-section .ei-download-btn p {
  padding-top: 8px;
  font-size: 15px;
  display: inline-block;
}

.ei-appdownload-section .ei-download-btn a {
  display: table;
  color: #0090ff;
  margin-top: 3px;
  font-size: 15px;
  font-weight: 700;
  font-family: "Poppins";
  position: relative;
}

.ei-appdownload-section .ei-download-btn a:after {
  left: 0;
  bottom: 0;
  content: "";
  height: 2px;
  width: 0%;
  position: absolute;
  background-color: #0090ff;
  transition: 0.3s all ease-in-out;
}

.ei-appdownload-section .ei-download-btn a:before {
  top: -2px;
  right: -25px;
  content: "";
  font-size: 20px;
  font-weight: 900;
  position: absolute;
  transition: 0.3s all ease-in-out;
  font-family: "Font Awesome 5 Free";
}

.ei-appdownload-section .ei-download-btn a:hover:before {
  right: -35px;
}

.ei-appdownload-section .ei-download-btn a:hover:after {
  width: 100%;
}

.ei-appdownload-section .app-down-btn {
  margin-bottom: 22px;
}

.ei-appdownload-section .app-down-btn a {
  margin-right: 10px;
  display: inline-block;
}

.ei-appdownload-section .ei-appdownloaad-shape {
  position: absolute;
  z-index: -1;
}

.ei-appdownload-section .app-shape1 {
  bottom: -170px;
  left: -115px;
}

.ei-appdownload-section .app-shape2 {
  top: -225px;
  right: 0;
  left: -270px;
  margin: 0 auto;
  text-align: center;
}

.ei-appdownload-section .app-shape3 {
  right: -10px;
  top: 0;
}

.ei-app-down-text {
  max-width: 450px;
  padding-top: 90px;
}

.ei-app-down-text .eight-section-title .eg-title-tag {
  margin-left: 55px;
}

.ei-app-down-text .eight-section-title h2 {
  padding-bottom: 15px;
}

.ei-app-down-text .eight-section-title p {
  color: #383838;
  padding-bottom: 32px;
}

/*---------------------------------------------------- */
/*team  area*/
/*----------------------------------------------------*/
.ei-team-section {
  padding: 145px 0px 135px;
}

.ei-team-section .eight-section-title {
  margin: 0 auto;
  max-width: 500px;
  padding-bottom: 70px;
}

.ei-team-section .eight-section-title .eg-title-tag {
  font-size: 14px;
}

.ei-team-section .ei-team-pic-text .ei-team-img:after {
  width: 236px;
  height: 236px;
  content: "";
  position: absolute;
  top: 0;
  left: 100px;
  opacity: 0;
  visibility: hidden;
  border: double 2px transparent;
  border-radius: 100%;
  background-image: linear-gradient(white, white), radial-gradient(circle at top left, #4171e8, #a80202, #4171e8);
  background-origin: border-box;
  transition: 0.3s all ease-in-out;
  background-clip: content-box, border-box;
}

.ei-team-section .ei-team-pic-text .ei-team-img .team-mem-img-ei {
  z-index: 1;
  width: 236px;
  height: 236px;
  margin: 0 auto;
  border-radius: 100%;
  overflow: hidden;
  background-color: #eff0f7;
}

.ei-team-section .ei-team-pic-text .ei-team-img .team-mem-img-ei .mshape-bg {
  width: 100%;
  z-index: -1;
  height: 100%;
  position: absolute;
}

.ei-team-section .ei-team-pic-text .ei-team-img .team-mem-img-ei .shape-bg1 {
  left: -110px;
  top: 70px;
}

.ei-team-section .ei-team-pic-text .ei-team-img .team-mem-img-ei .shape-bg2 {
  right: -65px;
  top: 35px;
}

.ei-team-section .ei-team-pic-text .ei-team-social {
  top: 47px;
  z-index: 1;
  right: 50px;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  transition-delay: 0.2s;
  transition: 0.5s all ease-in-out;
}

.ei-team-section .ei-team-pic-text .ei-team-social a {
  width: 35px;
  height: 35px;
  display: block;
  text-align: center;
  line-height: 37px;
  margin-bottom: 12px;
  border-radius: 100%;
  position: relative;
  z-index: 1;
  background-color: #fff;
  box-shadow: 0px 0px 18px 0px rgba(43, 1, 68, 0.23);
}

.ei-team-section .ei-team-pic-text .ei-team-social a:after {
  position: absolute;
  content: "";
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  z-index: -1;
  transition: 0.3s all ease-in-out;
  border-radius: 100%;
  background-image: -ms-linear-gradient(173deg, #a80202 0%, #4599fb 53%, #70fcff 99%);
}

.ei-team-section .ei-team-pic-text .ei-team-social a:nth-child(1) {
  color: #a80202;
}

.ei-team-section .ei-team-pic-text .ei-team-social a:nth-child(2) {
  transform: translateX(15px);
  color: #03a9f4;
}

.ei-team-section .ei-team-pic-text .ei-team-social a:nth-child(3) {
  transform: translateX(9px);
  margin-bottom: 5px;
  color: #a80202;
}

.ei-team-section .ei-team-pic-text .ei-team-social a:nth-child(4) {
  color: #0c6eff;
  transform: translateX(-20px);
}

.ei-team-section .ei-team-pic-text .ei-team-social a:hover {
  color: #fff;
}

.ei-team-section .ei-team-pic-text .ei-team-social a:hover:after {
  opacity: 1;
  visibility: visible;
}

.ei-team-section .ei-team-pic-text .ei-team-text {
  margin-top: 20px;
}

.ei-team-section .ei-team-pic-text .ei-team-text h3 {
  color: #000;
  font-size: 22px;
  font-weight: 700;
}

.ei-team-section .ei-team-pic-text .ei-team-text p {
  color: #383838;
  font-size: 15px;
}

.ei-team-section .ei-team-pic-text:hover .ei-team-img:after {
  opacity: 1;
  left: 102px;
  visibility: visible;
}

.ei-team-section .ei-team-pic-text:hover .ei-team-social {
  opacity: 1;
  right: 25px;
  visibility: visible;
}

.ei-team-section .ei-team-content {
  z-index: 1;
  margin: 0 auto;
  max-width: 1100px;
}

.ei-team-section .ei-team-content .owl-nav .owl-prev,
.ei-team-section .ei-team-content .owl-nav .owl-next {
  top: 50%;
  width: 52px;
  height: 52px;
  cursor: pointer;
  line-height: 52px;
  text-align: center;
  position: absolute;
  border-radius: 100%;
  display: inline-block;
  background-color: #fff;
  transform: translateY(-50%);
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 7px 18px 0px rgba(16, 31, 60, 0.25);
}

.ei-team-section .ei-team-content .owl-nav .owl-prev:hover,
.ei-team-section .ei-team-content .owl-nav .owl-next:hover {
  color: #fff;
  background-color: #a80202;
}

.ei-team-section .ei-team-content .owl-nav .owl-prev {
  left: -40px;
}

.ei-team-section .ei-team-content .owl-nav .owl-next {
  right: -50px;
}

.ei-team-section .ei-team-content .owl-item.active.center .ei-team-img:after {
  left: 102px;
  opacity: 1;
  visibility: visible;
}

.ei-team-section .ei-team-content .owl-item.active.center .ei-team-social {
  opacity: 1;
  right: 25px;
  visibility: visible;
}

/*---------------------------------------------------- */
/*faq  area*/
/*----------------------------------------------------*/
.ei-faq-section {
  background-color: #eceef6;
  padding: 145px 0px 110px;
  z-index: 1;
}

.ei-faq-section .ei-faq-content {
  position: relative;
  z-index: 5;
}

.ei-faq-section .ei-faq-content .ei-title-faq {
  margin: 0 auto;
  max-width: 885px;
}

.ei-faq-section .ei-faq-content .ei-title-faq p {
  color: #383838;
  font-size: 17px;
  margin-top: 35px;
  line-height: 1.647;
}

.ei-faq-section .eight-section-title {
  padding-bottom: 32px;
}

.ei-faq-section .eight-section-title .eg-title-tag {
  font-size: 15px;
  margin-left: 55px;
}

.ei-faq-section .ei-faq-queans {
  max-width: 1000px;
  margin: 0 auto;
}

.ei-faq-section .ei-faq-queans .ei-faq {
  padding: 0px 55px;
  margin-bottom: 25px;
}

.ei-faq-section .ei-faq-queans .ei-faq .ei-faq-body {
  margin-top: 15px;
}

.ei-faq-section .ei-faq-queans .ei-faq .ei-faq-body a {
  text-decoration: underline;
}

.ei-faq-section .ei-faq-queans .ei-faq.faq_bg {
  padding: 25px 55px;
  transition: 0.3s all ease-in-out;
  background-color: white;
  box-shadow: 0px 5px 15px -14px rgba(0, 0, 0, 0.75);
}

.ei-faq-section .ei-faq-queans .ei-faq-header button {
  border: none;
  font-size: 20px;
  font-weight: 700;
  position: relative;
  margin-bottom: 5px;
  padding: 0 0 0 50px;
  font-family: "Poppins";
  background-color: transparent;
}

.ei-faq-section .ei-faq-queans .ei-faq-header button:before {
  top: -6px;
  left: 0;
  color: #a80202;
  font-size: 30px;
  content: "";
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.ei-faq-section .ei-faq-queans .ei-faq-header .collapsed:before {
  content: "";
}

.ei-faq-section .ei-faq-shape {
  position: absolute;
  z-index: -1;
}

.ei-faq-section .fq-shape-style1 {
  top: -70px;
  left: 140px;
  z-index: 2;
}

.ei-faq-section .fq-shape-style2 {
  top: -70px;
  left: 75px;
  z-index: 1;
  opacity: 0.4;
  animation: spining 35s linear infinite;
}

.ei-faq-section .fq-shape-style3 {
  top: 50%;
  left: 165px;
  transform: translateX(-50%);
  animation: 3s ease 0s infinite alternate none running scale;
}

.ei-faq-section .fq-shape-style4 {
  top: 40%;
  right: 165px;
  transform: translateX(-50%);
}

.ei-faq-section .fq-shape-style5 {
  left: 20%;
  top: 115px;
  animation: spining 50s linear infinite;
}

.ei-faq-section .fq-shape-style6 {
  right: 0;
  z-index: -2;
  bottom: -30px;
}

.ei-faq-section .fq-shape-style8 {
  top: -50px;
  left: -50px;
  z-index: -3;
}

.ei-faq-section .fq-shape-style7 {
  top: 65px;
  right: 20%;
}

.ei-faq-section .fq-shape-style7 img {
  animation: spining 5s linear infinite;
  display: block;
  margin-top: 10px;
}

/*---------------------------------------------------- */
/*Screenshoot  area*/
/*----------------------------------------------------*/
.ei-screenshots-section {
  padding: 120px 0px;
  position: relative;
}

.ei-screenshots-section .eight-section-title {
  margin: 0 auto;
  max-width: 595px;
  padding-bottom: 10px;
}

.ei-screenshots-section .eight-section-title .eg-title-tag {
  font-size: 14px;
}

.ei-screenshots-section .swiper-slider-area .container {
  position: relative;
}

.ei-screenshots-section .swiper-wrapper {
  height: 630px;
  width: 320px;
}

.ei-screenshots-section .row.ei-appScreenshotCarousel-container.swiper-container-horizontal.swiper-container-3d.swiper-container-coverflow {
  position: relative;
}

.ei-screenshots-section .swiper-slide.swiper-slide-active {
  border-radius: 35px;
}

.ei-screenshots-section .ei-screen-mobile-image {
  background-image: url(../img/app-landing/screenshoot/mobile-1.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: block;
  height: 650px;
  left: calc(50% + 0.5px);
  position: absolute;
  top: 24px;
  transform: translateX(-50%);
  width: 301px;
  z-index: 2;
  border-radius: 40px;
}

.ei-screenshots-section .swiper-slide.swiper-slide-active {
  background-size: 100% 100%;
}

.ei-screenshots-section .swiper-wrapper {
  padding: 30px 0 40px;
}

.ei-screenshots-section .swiper-slide {
  box-shadow: -7px 10px 33px -23px rgba(0, 0, 0, 0.75);
}

.ei-screenshots-section .swiper-slide.swiper-slide-next {
  box-shadow: -7px 10px 33px -23px rgba(0, 0, 0, 0.75);
}

.ei-screenshots-section .swiper-slide.swiper-slide-prev {
  box-shadow: 9px 10px 33px -23px rgba(0, 0, 0, 0.75);
}

.ei-screenshots-section .screenshoot-shape1 {
  right: 0;
  bottom: 205px;
}

.ei-screenshots-section .screenshoot-shape2 {
  left: 0;
  top: 240px;
}

.ei-screenshots-section .banner-navigation {
  position: relative;
  text-align: center;
  margin-top: 0px;
  z-index: 2;
}

.ei-screenshots-section .banner-navigation .swiper-button-next,
.ei-screenshots-section .banner-navigation .swiper-button-prev {
  position: relative;
  font-size: 16px;
  color: #373a5b;
  width: 50px;
  height: 50px;
  line-height: 55px;
  text-align: center;
  border-radius: 50px;
  font-weight: 700;
  background-image: none;
  display: inline-block;
  margin: 0px 15px 0px 15px;
  background-color: #e7ecf2;
  transition: all 300ms ease;
}

.ei-screenshots-section .banner-navigation .swiper-button-prev:hover,
.ei-screenshots-section .banner-navigation .swiper-button-next:hover {
  color: #ffffff;
  background-color: #a80202;
}

/*---------------------------------------------------- */
/*testimonial  area*/
/*----------------------------------------------------*/
.ei-testimonial-section {
  padding: 120px 0px 115px;
}

.ei-testimonial-section .eight-section-title {
  margin: 0 auto;
  max-width: 490px;
  padding-bottom: 64px;
}

.ei-testimonial-section .eight-section-title .eg-title-tag {
  font-size: 14px;
}

.ei-testimonial-section .ei-testimonial-img-text {
  padding: 30px 40px 30px 0px;
  border-radius: 120px;
  background-color: #ffffff;
  box-shadow: 0px 10px 20px 0px rgba(0, 5, 50, 0.1);
}

.ei-testimonial-section .ei-testimonial-img-text:before {
  content: "";
  position: absolute;
  bottom: -10px;
  left: -35px;
  height: 100%;
  width: 100%;
  z-index: 0;
  background-repeat: no-repeat;
  background-image: url(../img/app-landing/team/t-shape2.png);
}

.ei-testimonial-section .ei-testimonial-img-text .test--quote-icon {
  position: absolute;
  top: -20px;
  right: 100px;
}

.ei-testimonial-section .ei-testimonial-img-text .test--quote-icon i {
  color: #cfd8e8;
  font-size: 40px;
}

.ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-img {
  height: 160px;
  width: 160px;
  position: relative;
  top: -25px;
  left: -25px;
  line-height: 157px;
  background-color: #fff;
  border-radius: 100%;
  box-shadow: 0px 0px 18px 0px rgba(0, 5, 50, 0.12);
  overflow: hidden;
}

.ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-img img {
  height: 130px;
  width: 130px;
  margin: 0 auto;
  border-radius: 100%;
  z-index: 1;
  position: relative;
}

.ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-img:before {
  content: "";
  position: absolute;
  top: -58px;
  right: 0;
  height: 100%;
  width: 100%;
  z-index: 0;
  background-repeat: no-repeat;
  background-image: url(../img/app-landing/team/t-shape1.png);
}

.ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-img:after {
  content: "";
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  position: absolute;
  background-color: #fff;
}

.ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-text .ei-testimonial-name {
  padding-bottom: 5px;
}

.ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-text .ei-testimonial-name h3 {
  color: #000;
  font-size: 20px;
  font-weight: 700;
  padding-bottom: 5px;
}

.ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-text .ei-testimonial-name span {
  font-size: 15px;
  color: #0887fc;
}

.ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-text p {
  line-height: 1.529;
  margin-bottom: 0;
  padding-bottom: 8px;
}

.ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-text .ei-testi-rate li {
  color: #ffba00;
}

#testimonial-scroller {
  max-width: 1100px;
  margin: 0 auto;
}

#testimonial-scroller .owl-stage-outer {
  overflow: visible;
}

#testimonial-scroller .owl-item img {
  display: inline;
}

#testimonial-scroller .owl-stage-outer {
  overflow: visible;
}

#testimonial-scroller .owl-item {
  opacity: 0;
  transition: opacity 500ms;
}

#testimonial-scroller .owl-item.active {
  opacity: 1;
}

#testimonial-scroller .owl-nav {
  display: none;
}

#testimonial-scroller .owl-dots {
  height: 13px;
  margin-top: 30px;
  text-align: center;
  border-radius: 30px;
}

#testimonial-scroller .owl-dot {
  width: 78px;
  height: 13px;
  border-radius: 30px;
  display: inline-block;
  background-color: #e9ebf7;
}

#testimonial-scroller .owl-dot.active {
  background-color: #a80202;
}

.testimonial-floatingimg li {
  height: 80px;
  width: 80px;
  position: absolute;
  overflow: hidden;
  border-radius: 100%;
  border: 10px solid #e7eaef;
  opacity: 0.5;
  animation: 1.5s ease 0s infinite alternate none running scale;
}

.testimonial-float-img1 li:nth-child(1) {
  top: 90px;
  left: 215px;
}

.testimonial-float-img1 li:nth-child(2) {
  top: 198px;
  left: 108px;
}

.testimonial-float-img1 li:nth-child(3) {
  top: 30%;
  left: 20%;
}

.testimonial-float-img1 li:nth-child(4) {
  top: 360px;
  left: 140px;
}

.testimonial-float-img2 li:nth-child(1) {
  bottom: 90px;
  right: 215px;
}

.testimonial-float-img2 li:nth-child(2) {
  bottom: 198px;
  right: 50px;
}

.testimonial-float-img2 li:nth-child(3) {
  bottom: 30%;
  right: 15%;
}

.testimonial-float-img2 li:nth-child(4) {
  bottom: 360px;
  right: 140px;
}

/*---------------------------------------------------- */
/*Pertner  area*/
/*----------------------------------------------------*/
.ei-partner-section {
  padding: 80px 0px;
}

.ei-partner-section .ei-partner-content {
  margin: 0 auto;
  max-width: 875px;
}

#ei-partner-slide .owl-nav {
  display: none;
}

.ei-partner-text {
  padding-top: 35px;
}

.ei-partner-text .ei-partner-icon {
  height: 30px;
  width: 30px;
  line-height: 35px;
  color: #fff;
  margin-right: 20px;
  border-radius: 100%;
  background-color: #eeb22d;
  box-shadow: 0px 8px 24px 0px rgba(238, 178, 45, 0.57);
}

.ei-partner-text h4 {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 5px;
}

.ei-partner-text p {
  font-size: 14px;
  color: #626161;
}

/*---------------------------------------------------- */
/*newslatter  area*/
/*----------------------------------------------------*/
.ei-newslatter-section {
  overflow: visible;
}

.ei-newslatter-section .ei-newslatter-mockup {
  position: absolute;
  top: -35%;
  right: 18%;
}

.ei-newslatter-section .ei-newslatter-box {
  overflow: hidden;
  padding: 80px 0px 85px;
  background: linear-gradient(-90deg, #a80202 0%, #750cf8 50%, #4ce7f3 100%);
}

.ei-newslatter-section .ei-newslatter-box .ei-news-vector1 {
  top: -40px;
  left: 250px;
  animation: 3s ease 0s infinite alternate none running scale;
}

.ei-newslatter-section .ei-newslatter-box .ei-news-vector2 {
  top: -30px;
  right: -70px;
  animation: 2s ease 0s infinite alternate none running scale;
}

.ei-newslatter-section .ei-newslatter-box .ei-news-vector3 {
  left: -75px;
  bottom: -235px;
}

.ei-newslatter-section .ei-newslatter-contnet {
  max-width: 650px;
  margin-left: 130px;
}

.ei-newslatter-section .ei-newslatter-contnet h3 {
  color: #fff;
  font-size: 26px;
  font-weight: 700;
}

.ei-newslatter-section .ei-newslatter-contnet h3 span {
  font-weight: 100;
}

.ei-newslatter-section .ei-newslatter-contnet .ei-newslatter-form {
  margin-top: 20px;
}

.ei-newslatter-section .ei-newslatter-contnet .ei-newslatter-form form input {
  width: 100%;
  border: none;
  height: 72px;
  border-radius: 50px;
  padding-left: 30px;
  margin-bottom: 15px;
}

.ei-newslatter-section .ei-newslatter-contnet .ei-newslatter-form form .nws-button {
  top: 12px;
  right: 12px;
}

.ei-newslatter-section .ei-newslatter-contnet .ei-newslatter-form form .nws-button button {
  height: 48px;
  width: 162px;
  border: none;
  color: #fff;
  border-radius: 30px;
  text-align: center;
  background-color: transparent;
  background-image: linear-gradient(90deg, #a80202 0%, #4599fb 53%, #70fcff 99%);
}

.ei-newslatter-section .ei-newslatter-contnet .ei-newslatter-form form p {
  color: #fff;
  font-size: 14px;
  margin-bottom: 0;
  padding-left: 10px;
}

/*---------------------------------------------------- */
/*Footer  area*/
/*----------------------------------------------------*/
.ei-footer-section {
  padding-top: 160px;
}

.ei-footer-widget .ei-widget-title {
  font-size: 16px;
  color: #373a5b;
  font-weight: 700;
  padding-bottom: 20px;
}

.ei-footer-widget .ei-footer-logo {
  margin-bottom: 25px;
}

.ei-footer-widget p {
  max-width: 220px;
  line-height: 1.875;
}

.ei-footer-widget .ei-payment-mathod {
  margin-top: 30px;
}

.ei-footer-widget ul li {
  width: 50%;
  float: left;
  font-size: 14px;
  padding-left: 15px;
  margin-bottom: 20px;
  font-family: "Poppins";
}

.ei-footer-widget ul li a {
  color: #373a5b;
  position: relative;
  transition: 0.3s all ease-in-out;
}

.ei-footer-widget ul li a:before {
  left: -15px;
  top: 3px;
  content: "";
  font-size: 12px;
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.ei-footer-widget ul li a:after {
  content: "";
  position: absolute;
  height: 1px;
  width: 0%;
  left: 0;
  bottom: 0;
  background-color: #0072fd;
  transition: 0.3s all ease-in-out;
}

.ei-footer-widget ul li a:hover {
  color: #0072fd;
  margin-left: 10px;
}

.ei-footer-widget ul li a:hover:after {
  width: 100%;
}

.ei-footer-widget h4 {
  width: 45%;
  float: left;
  font-size: 14px;
  font-weight: 700;
  padding-left: 28px;
  margin-right: 15px;
  position: relative;
  display: inline-block;
}

.ei-footer-widget h4 i {
  left: 0;
  top: 2px;
  color: #a80202;
  font-size: 20px;
  margin-right: 5px;
  position: absolute;
}

.ei-footer-widget h4 span {
  font-family: "Circular Std Book";
  font-weight: 400;
  margin-top: 10px;
  display: block;
  color: #818181;
}

.ei-footer-widget .download-btn {
  width: 100%;
  margin-top: 25px;
  display: inline-block;
}

.ei-footer-widget .download-btn a {
  margin-right: 10px;
}

.ei-footer-widget .ei-footer-social {
  margin-top: 15px;
}

.ei-footer-widget .ei-footer-social a {
  height: 30px;
  width: 30px;
  border-radius: 100%;
  background-color: #fff;
  line-height: 30px;
  text-align: center;
  margin-right: 5px;
  display: inline-block;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 0px 9px 0px rgba(15, 54, 131, 0.07);
}

.ei-footer-widget .ei-footer-social a:nth-child(1) {
  color: #16599b;
}

.ei-footer-widget .ei-footer-social a:nth-child(2) {
  color: #a80202;
}

.ei-footer-widget .ei-footer-social a:nth-child(3) {
  color: #0d6fff;
}

.ei-footer-widget .ei-footer-social a:nth-child(4) {
  color: #03a9f4;
}

.ei-footer-copyright {
  margin-top: 40px;
  font-size: 14px;
  font-family: "Poppins";
}

.ei-footer-copyright .ei-footer-copyright-content {
  padding: 25px 0px 15px;
  border-top: 2px solid #dee2ef;
}

.ei-footer-copyright .ei-copyright-menu {
  float: right;
}

.ei-footer-copyright .ei-copyright-menu a {
  margin-left: 35px;
}

@keyframes left-right-move {
  0% {
    transform: translateX(-100px);
  }
  50% {
    transform: translateX(-10px);
  }
  100% {
    transform: translateX(-100px);
  }
}
.ei-footer-shape1 {
  top: 120px;
  right: 130px;
  opacity: 0.5;
  z-index: -1;
}

.ei-footer-shape2 {
  top: 110px;
  left: -160px;
  opacity: 0.5;
  z-index: -1;
}

.ei-footer-shape3 {
  left: -50px;
  right: 0;
  opacity: 0.5;
  z-index: -1;
  bottom: 100px;
  margin: 0 auto;
  text-align: center;
  animation-name: left-right-move;
  animation-duration: 25s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

/*---------------------------------------------------- */
/*Mobile-Menu  area*/
/*----------------------------------------------------*/
.appi-ei-mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 280px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 40px 0px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s ease-in;
}

.appi-ei-mobile_menu_content .appi-ei-mobile-main-navigation {
  width: 100%;
}

.appi-ei-mobile_menu_content .appi-ei-mobile-main-navigation .navbar-nav {
  width: 100%;
}

.appi-ei-mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}

.appi-ei-mobile_menu_content .appi-ei-mobile-main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid #dcdcdc;
}

.appi-ei-mobile_menu_content .appi-ei-mobile-main-navigation .navbar-nav li:first-child {
  border-top: 1px solid #dcdcdc;
}

.appi-ei-mobile_menu_content .appi-ei-mobile-main-navigation .navbar-nav li a {
  color: #000;
  padding: 0;
  width: 100%;
  display: block;
  font-size: 14px;
  font-weight: 400;
  padding: 5px 30px;
  text-transform: uppercase;
}

.appi-ei-mobile_menu_content .m-brand-logo {
  width: 160px;
  margin: 0 auto;
  margin-bottom: 30px;
}

.appi-ei-mobile_menu_wrap.mobile_menu_on .appi-ei-mobile_menu_content {
  right: 0px;
  transition: all 0.7s ease-out;
}

.mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: 0%;
  height: 120vh;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.5s ease-in-out;
}

.mobile_menu_overlay_on {
  overflow: hidden;
}

.appi-ei-mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  opacity: 1;
  visibility: visible;
}

.appi-ei-mobile_menu_button {
  position: absolute;
  display: none;
  right: 20px;
  cursor: pointer;
  line-height: 40px;
  color: #fff;
  text-align: center;
  font-size: 30px;
  top: 2px;
  z-index: 5;
}

.appi-ei-mobile_menu .appi-ei-mobile-main-navigation .navbar-nav li a:after {
  display: none;
}

.appi-ei-mobile_menu .appi-ei-mobile-main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}

.appi-ei-mobile_menu .appi-ei-mobile_menu_content .appi-ei-mobile-main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
  width: 100%;
  border-top: 1px solid #dcdcdc;
}

.appi-ei-mobile_menu .appi-ei-mobile_menu_content .appi-ei-mobile-main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 0 20px;
  line-height: 1;
}

.appi-ei-mobile_menu .dropdown {
  position: relative;
}

.appi-ei-mobile_menu .dropdown .dropdown-btn {
  position: absolute;
  top: 0px;
  right: 0;
  height: 30px;
  padding: 5px 10px;
}

.appi-ei-mobile_menu .dropdown .dropdown-btn:before {
  content: "";
  position: absolute;
  height: 100%;
  width: 1px;
  top: 0;
  left: 0;
  background-color: #dcdcdc;
}

.appi-ei-mobile_menu .appi-ei-mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

/*---------------------------------------------------- */
/*Responsive  area*/
/*----------------------------------------------------*/
@media screen and (max-width: 1440px) {
  .main-header-eight .appheader-content {
    padding: 0px 40px;
  }
  .ei-service-slide-mbl {
    right: 0;
  }
  .main-header-eight .appheader-content .site-logo {
    margin-right: 230px;
  }

  .eg-fun-fact-section .eg-fun-fact-mockup .fn-shape-item1 {
    left: 70px;
  }

  .eg-fun-fact-section .fn-bg-shape {
    left: -195px;
  }

  .ei-faq-section .fq-shape-style1 {
    left: -20px;
  }

  .ei-faq-section .fq-shape-style3 {
    left: 60px;
  }

  .ei-faq-section .fq-shape-style2 {
    left: 0;
  }

  .ei-newslatter-section .ei-newslatter-mockup {
    right: 5%;
  }

  .ei-footer-shape1 {
    right: 0;
  }

  .eight-service-section .eight-service-slide .ei-service-slide-btn {
    margin-left: 60px;
  }

  .eight-service-section .eight-service-slide {
    width: 48.667%;
  }

  .eight-service-section .eight-service-text {
    width: 51.333333%;
    padding: 85px 0px 0px 0;
  }

  .eight-service-section .eight-service-text h2 {
    max-width: 285px;
  }
}
@media screen and (max-width: 1280px) {
  .main-header-eight .appheader-content .site-logo {
    margin-right: 155px;
  }
}
@media screen and (max-width: 1199px) {
  .main-header-eight .appheader-content .site-logo {
    margin-right: 115px;
  }

  .ei-screenshots-section .ei-screen-mobile-image {
    width: 322px;
  }
}
@media screen and (max-width: 1024px) { 
  .main-header-eight .appheader-content .site-logo  {
    margin-right: 40px
  }
  .main-header-eight .appheader-content .navigation-eight li {
    margin-right: 10px;
  }
  .main-header-eight .appheader-content .h-eight-social {
    margin-left: 0;
  }
  .eg-fun-fact-section .eg-fun-fact-mockup {
    left: -320px;
  }
  .ei-team-section .ei-team-content .owl-item.active.center .ei-team-img:after,
  .ei-team-section .ei-team-content .owl-item .ei-team-img:after {
    left: 70px;
  }
  .ei-team-section .ei-team-content .owl-nav .owl-prev,
  .ei-team-section .ei-team-content .owl-nav .owl-next {
    position: static;
    text-align: center;
    margin: 0px 8px;
    transform: translateY(0);
  }
  .ei-team-section .ei-team-content .owl-nav {
    text-align: center;
    margin-top: 20px;
  }
  .ei-newslatter-section .ei-newslatter-mockup {
    display: none;
  }
  .ei-footer-widget .download-btn a {
    margin-right: 0px;
  }
  .eight-service-section .eight-service-slide .ei-service-slide-btn {
    margin-left: 15px;
  }
  .eight-service-section .eight-service-text h2 {
    max-width: 245px;
  }
  .ei-testimonial-section .ei-testimonial-img-text {
    margin: 0 auto;
    max-width: 500px;
  }
}
@media screen and (max-width: 991px) {
  .main-header-eight .appheader-content .navigation-eight {
    display: none;
  }

  .main-header-eight .appheader-content .h-eight-social {
    display: none;
  }

  .appi-ei-mobile_menu_button {
    display: block;
  }

  .main-header-eight .appheader-content {
    padding: 0px 20px;
  }

  .main-header-eight .appheader-content .sign-up-btn-eight {
    margin-right: 50px;
  }

  .eight-banner-section .eight-banner-content .ei-banner-mbl-mockup {
    position: static;
    margin-top: 40px;
  }

  .eight-banner-section {
    padding-bottom: 100px;
  }

  .feature-eight-section {
    padding: 100px 0px;
  }

  .feature-eight-section .eight-feature-box {
    margin-bottom: 50px;
  }

  .eight-service-section .eight-service-slide {
    width: 100%;
  }

  .eight-service-section .eight-service-text {
    width: 100%;
  }

  .eight-service-section:after {
    display: none;
  }

  .eight-service-section:before {
    display: none;
  }

  .eight-service-section .s-shape-bg2 {
    display: none;
  }

  .eight-service-section .eight-service-text:before {
    display: none;
  }

  .eight-service-section .eight-service-slide a:nth-child(3) {
    transform: translateX(0);
  }

  .ei-service-slide-mbl {
    position: relative;
    right: 0;
    top: 0;
  }

  .eight-service-section .eight-service-slide .ei-service-slide-btn {
    max-width: 475px;
    margin: 0 auto;
  }

  .eight-service-section .eight-service-text h2 {
    margin: 0 auto;
    color: #000;
    max-width: 400px;
    padding-top: 30px;
    text-align: center;
  }

  .eg-fun-fact-section .eg-fun-fact-mockup {
    position: static;
    margin-bottom: 40px;
  }

  .eg-fun-fact-section .eg-funfact-text {
    float: none !important;
  }

  .eg-how-work-section .how-work-mockup {
    text-align: center;
  }

  .ei-team-section .ei-team-pic-text .ei-team-social {
    right: 13px;
  }

  .ei-screenshots-section .ei-screen-mobile-image {
    width: 270px;
  }

  #testimonial-scroller {
    max-width: 570px;
  }

  .ei-newslatter-section .ei-newslatter-box .ei-news-vector2 {
    display: none;
  }

  .ei-footer-section {
    padding-top: 100px;
  }

  .testimonial-floatingimg {
    display: none;
  }

  .eg-how-work-section .how-work-mockup .hw-shape1,
  .eg-how-work-section .how-work-mockup .hw-shape2 {
    display: none;
  }
  .ei-appdownload-section {
    padding-bottom: 60px;
  }
  .ei-team-section .ei-team-content .owl-item.active.center .ei-team-img:after,
  .ei-team-section .ei-team-content .owl-item .ei-team-img:after {
    display: none;
  }
  .ei-team-pic-text {
    max-width: 300px;
    margin: 0 auto;
  }
  .ei-team-section .ei-team-content .owl-item.active.center .ei-team-social {
    right: 0px;
  }
  .ei-footer-widget {
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 767px) {
  .ei-screenshots-section .ei-screen-mobile-image {
    width: 370px;
    height: 655px;
  }
}
@media screen and (max-width: 680px) {
  .eight-banner-section .eight-banner-content h1 {
    font-size: 65px;
  }

  .ei-team-section .ei-team-content .owl-item.active.center .ei-team-img:after {
    left: 75px;
  }
}
@media screen and (max-width: 580px) {
  .eight-banner-section .eight-banner-content h1 {
    font-size: 60px;
  }

  .eg-fun-fact-section .eg-fun-fact-mockup .fn-shape-item1 {
    display: none;
  }

  .eg-fun-fact-section .eg-fun-fact-mockup .fn-shape-item2 {
    display: none;
  }

  .eg-how-work-section .ei-how-work-content-item {
    padding-left: 50px;
  }

  .ei-team-pic-text {
    max-width: 270px;
    margin: 0 auto;
  }

  .ei-team-section .ei-team-pic-text .ei-team-social {
    right: -40px;
  }

  .ei-screenshots-section .ei-screen-mobile-image {
    width: 305px;
  }

  #testimonial-scroller {
    max-width: 450px;
  }

  #testimonial-scroller .ei-testimonial-text {
    display: block;
    overflow: hidden;
  }

  .ei-newslatter-section .ei-newslatter-contnet {
    margin-left: 0px;
  }
}
@media screen and (max-width: 480px) {
  .eight-banner-section .eight-banner-content h1 {
    text-align: center;
    font-size: 50px;
  }

  .cd-headline.clip .cd-words-wrapper::after {
    height: 40px;
  }

  .eight-banner-section .eight-banner-content p {
    text-align: center;
  }

  .eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn {
    text-align: center;
  }

  .ei-banner-review {
    text-align: center;
    max-width: 320px;
    margin: 0 auto;
  }

  .eight-banner-section .eight-banner-content .ei-banner-review ul {
    float: none;
    margin-right: 0;
    margin-bottom: 10px;
  }

  .ei-service-icon-text .ei-service-icon {
    margin-left: 30px;
  }

  .eg-fun-fact-section .eg-fun-fact-mockup .fn-shape {
    display: none;
  }

  .pera-content p {
    overflow: hidden;
  }

  .hw-mockup-img {
    margin-top: 20px;
  }

  .eg-how-work-section .how-work-mockup .hw-shape1,
  .eg-how-work-section .how-work-mockup .hw-shape2,
  .ei-faq-section .fq-shape-style3,
  .ei-faq-section .fq-shape-style4 {
    display: none;
  }

  .ei-team-section .ei-team-content .owl-nav {
    width: 150px;
    margin: 0 auto;
    margin-top: 40px;
    position: static;
    transform: translateY(0);
  }

  .ei-faq-section .ei-faq-queans {
    margin-top: 20px;
  }

  .ei-faq-section .ei-faq-queans .ei-faq-header button {
    text-align: left;
  }

  .ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-img {
    left: -10px;
  }
}
@media screen and (max-width: 420px) {
  .eight-banner-section .eight-banner-content h1 {
    font-size: 42px;
  }

  .cd-headline.clip .cd-words-wrapper::after {
    height: 30px;
  }

  .feature-eight-section {
    padding: 50px 0px;
  }

  .eight-section-title h2 {
    font-size: 28px;
  }

  .eight-service-slide {
    padding-right: 0;
  }

  .ei-service-icon-text .ei-service-icon {
    height: 80px;
    width: 80px;
    line-height: 80px;
  }

  .ei-service-icon-text .ei-service-icon i {
    font-size: 35px;
  }

  .eight-service-section {
    padding: 65px 0px;
  }

  .eight-service-section .eight-service-slide a:nth-child(1) {
    margin-bottom: 30px;
  }

  .eight-service-section .eight-service-slide a:nth-child(2) {
    margin-bottom: 30px;
    transform: translateX(0);
  }

  .eight-service-section .eight-service-text {
    padding-top: 40px;
  }

  .eight-service-section .eight-service-text h2 {
    font-size: 30px;
  }

  .eg-fun-fact-section {
    padding: 60px 0px;
  }

  .eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number .odometer {
    font-size: 36px;
  }

  .eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number strong {
    font-size: 30px;
    top: 4px;
  }

  .eg-fun-fact-section .eg-funfact-text .fun-fact-counter .eg-counter-number p {
    font-size: 20px;
    max-width: 100px;
  }

  .eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .eg-how-work-icon {
    margin-right: 0;
    margin-bottom: 15px;
    float: none !important;
  }

  .eg-how-work-section .ei-how-work-content-item {
    padding-left: 30px;
  }

  .eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .eg-how-work-text h3 {
    font-size: 20px;
  }

  .eg-how-work-section {
    padding-bottom: 30px;
  }

  .ei-app-down-text {
    padding: 50px 0px;
  }

  .ei-faq-section .ei-faq-queans .ei-faq.faq_bg {
    padding: 20px 30px;
  }

  .ei-faq-section .ei-faq-queans .ei-faq {
    padding: 0px 30px;
  }

  .ei-faq-section {
    padding: 60px 0px;
  }

  .ei-screenshots-section {
    padding: 60px 0px;
  }

  .ei-testimonial-section {
    padding: 60px 0px;
  }

  .ei-testimonial-section .ei-testimonial-img-text {
    padding: 25px 20px 25px 0px;
  }

  .ei-footer-widget {
    margin-bottom: 30px;
  }

  .ei-footer-copyright .ei-copyright-menu {
    float: left;
  }

  .ei-footer-copyright .ei-copyright-menu a {
    margin-left: 0;
    margin-right: 15px;
  }

  .eg-how-work-text {
    display: block;
    width: 100%;
  }

  .ei-team-section {
    padding: 60px 0px;
  }

  .eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn a:nth-child(1) {
    height: 45px;
    width: 150px;
    line-height: 45px;
  }

  .eight-banner-section .eight-banner-content .banner-content-box .ei-banner-btn {
    padding: 20px 0px 10px 0px;
  }

  .main-header-eight .appheader-content .site-logo {
    margin-right: 30px;
  }

  .ei-footer-section {
    padding-top: 60px;
  }

  .eg-how-work-section .how-work-mockup {
    padding-left: 0;
  }

  .ei-team-section .ei-team-content .owl-item.active.center .ei-team-img:after {
    left: 35px;
  }

  .ei-team-section .ei-team-content .owl-item.active.center .ei-team-social {
    right: -5px;
  }

  .swiper-container {
    width: 295px;
  }
}
@media screen and (max-width: 380px) {
  .eight-banner-section .eight-banner-content h1 {
    font-size: 36px;
  }

  .ei-appdownload-section .app-down-btn a {
    margin-bottom: 20px;
  }

  .ei-testimonial-section .ei-testimonial-img-text:before {
    display: none;
  }

  .ei-newslatter-section .ei-newslatter-contnet .ei-newslatter-form form .nws-button button {
    width: 150px;
    margin: 0 auto;
  }

  .ei-newslatter-section .ei-newslatter-contnet .ei-newslatter-form form .nws-button {
    position: static !important;
    text-align: center;
    margin-bottom: 20px;
  }

  .eg-how-work-section .mCustomScrollBox {
    left: 0;
  }
}
@media screen and (max-width: 320px) {
  .main-header-eight .appheader-content .sign-up-btn-eight {
    width: 80px;
  }

  .eight-banner-section .eight-banner-content h1 {
    font-size: 33px;
  }

  .eight-section-title h2 {
    font-size: 24px;
  }

  .eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .eg-how-work-text h3 {
    font-size: 16px;
  }

  .eg-how-work-section .ei-how-work-content-item .eg-how-work-icon-text .scroller-no {
    display: none;
  }

  .eg-how-work-section .eg-how-work-content {
    padding-left: 5px;
  }

  .ei-faq-section .ei-faq-queans .ei-faq-header button {
    font-size: 16px;
  }

  .ei-testimonial-section .ei-testimonial-img-text {
    border-radius: 0;
    padding: 20px;
  }

  .ei-testimonial-section .ei-testimonial-img-text .ei-testimonial-img {
    float: none !important;
    top: 0;
    margin-bottom: 15px;
  }

  .ei-footer-widget .download-btn a {
    margin-bottom: 20px;
    display: block;
  }

  .ei-footer-widget h4 {
    margin-bottom: 10px;
  }
}
/*---------------------------------------------------- */
/*---------------------------------------------------- */
/*----------------------------------------------------*/
.appseo-home {
  margin: 0;
  padding: 0;
  color: #415e8d;
  font-size: 16px;
  line-height: 1.765;
  overflow-x: hidden;
  font-family: "Poppins";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

.appseo-home::selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.appseo-home::-moz-selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.appseo-home .container {
  max-width: 1200px;
}

.appseo-preloader {
  background-color: #fff;
  background: #fff url("../img/seo/pre.svg") no-repeat center center;
}

.appseo-headline h1,
.appseo-headline h2,
.appseo-headline h3,
.appseo-headline h4,
.appseo-headline h5,
.appseo-headline h6 {
  margin: 0;
  font-family: "Poppins";
}

.appseo-scrollup {
  width: 55px;
  right: 20px;
  z-index: 5;
  height: 55px;
  bottom: 20px;
  display: none;
  position: fixed;
  border-radius: 100%;
  line-height: 55px;
  background-image: linear-gradient(45deg, #ff6626 15%, #ffa641 100%);
}

.appseo-scrollup i {
  color: #fff;
  font-size: 20px;
}

.appseo-section-title {
  padding-bottom: 35px;
}

.appseo-section-title:after {
  left: 0;
  right: 0;
  bottom: 0;
  content: "";
  width: 60px;
  height: 25px;
  margin: 0 auto;
  position: absolute;
  background-repeat: no-repeat;
  background-image: url(../img/seo/shape/ts1.png);
}

.appseo-section-title p {
  color: #415e8d;
  font-size: 18px;
  text-transform: capitalize;
}

.appseo-section-title h2 {
  font-size: 40px;
  font-weight: 800;
  text-transform: uppercase;
}
@keyframes borderplsseo {
  0% {
    box-shadow: 0 0 0 0 #f7d4c0;
  }
  70% {
    box-shadow: 0 0 0 10px #f7d4c0;
    opacity: 0;
  }
  to {
    box-shadow: 0 0 0 0 #f7d4c0;
    opacity: 0;
  }
}
.appseo-section-title2 p {
  color: #ff8533;
  font-size: 18px;
  font-weight: 600;
  padding-left: 35px;
  position: relative;
  padding-bottom: 18px;
}

.appseo-section-title2 p:before,
.appseo-section-title2 p:after {
  width: 8px;
  height: 8px;
  content: "";
  left: 6px;
  top: 12px;
  z-index: 2;
  position: absolute;
  border-radius: 100%;
  background-color: #ff8533;
}

.appseo-section-title2 p:after {
  left: 0;
  top: 6px;
  z-index: 1;
  width: 20px;
  height: 20px;
  background-color: #f7d4c0;
  animation: borderplsseo 1.5s infinite cubic-bezier(0.4, 0, 1, 1) both;
}

.appseo-section-title2 h2 {
  color: #213e6e;
  font-size: 40px;
  font-weight: 700;
  line-height: 1.125;
}
@keyframes appseoupdown {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(0px);
  }
}
@keyframes appseoupdown {
  0% {
    transform: translateY(1px);
  }
  100% {
    transform: translateY(-1px);
  }
}
@keyframes appseoupdown {
  0% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(10px);
  }
}
@keyframes videoup {
  70% {
    box-shadow: 0 0 0 30px rgba(255, 243, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 243, 234, 0);
  }
}
@keyframes animateBubble {
  0% {
    margin-top: 20%;
  }
  100% {
    margin-top: -30%;
  }
}
@keyframes sideWays {
  0% {
    margin-left: 0;
  }
  100% {
    margin-left: 25px;
  }
}
@keyframes flying {
  0% {
    transform: translate(2px, 2px);
  }
  50% {
    transform: translate(-2px, -2px);
  }
  100% {
    transform: translate(2px, 2px);
  }
}
@keyframes fadeFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeFromUp {
  animation-name: fadeFromUp;
}

.fadeFromRight {
  animation-name: fadeFromRight;
}

.fadeFromLeft {
  animation-name: fadeFromLeft;
}

.appseo-btn-hover {
  z-index: 1;
  overflow: hidden;
  position: relative;
}

.appseo-btn-hover:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  border-radius: 40px;
  transition: all 0.5s;
  opacity: 1;
  z-index: -1;
  transform: translate(-105%, 0);
  background-color: rgba(255, 255, 255, 0.8);
}

.appseo-btn-hover:hover:before {
  opacity: 0;
  transform: translate(0, 0);
}

/*---------------------------------------------------- */
/*Header area*/
/*----------------------------------------------------*/
.appseo-main-header {
  z-index: 5;
  width: 100%;
  padding-top: 60px;
  position: absolute;
}

.appseo-main-header .navbar-nav {
  display: inherit;
}

.appseo-main-header .appseo-logo {
  padding-top: 10px;
}

.appseo-main-header .appseo-main-navigation {
  padding-top: 13px;
  display: inline-block;
}

.appseo-main-header .appseo-main-navigation li {
  margin-left: 35px;
}

.appseo-main-header .appseo-main-navigation li a {
  color: #213e6e;
  font-size: 17px;
  font-weight: 600;
  padding-bottom: 20px;
}
.appseo-main-header .appseo-main-navigation li a.active {
  color:  #ff8533;
}

.appseo-main-header .appseo-main-navigation .dropdown {
  position: relative;
}

.appseo-main-header .appseo-main-navigation .dropdown:after {
  content: "";
  position: absolute;
  right: -12px;
  top: 2px;
  transition: 0.3s all ease-in-out;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.appseo-main-header .appseo-main-navigation .dropdown .dropdown-menu {
  top: 65px;
  left: 0;
  opacity: 0;
  z-index: 2;
  margin: 0px;
  padding: 0px;
  height: auto;
  width: 200px;
  border: none;
  display: block;
  border-radius: 0;
  overflow: hidden;
  visibility: hidden;
  position: absolute;
  border-radius: 10px;
  background-color: #fff;
  transition: all 0.4s ease-in-out;
  border-bottom: 2px solid #ff6626;
  box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
}

.appseo-main-header .appseo-main-navigation .dropdown .dropdown-menu li {
  width: 100%;
  margin-left: 0;
  border-bottom: 1px solid #e5e5e5;
}

.appseo-main-header .appseo-main-navigation .dropdown .dropdown-menu li a {
  width: 100%;
  color: #343434;
  display: block;
  font-size: 14px;
  padding: 10px 25px;
  position: relative;
  transition: 0.3s all ease-in-out;
}

.appseo-main-header .appseo-main-navigation .dropdown .dropdown-menu li a:before {
  display: none;
}

.appseo-main-header .appseo-main-navigation .dropdown .dropdown-menu li a:after {
  left: 10px;
  top: 18px;
  width: 8px;
  height: 8px;
  content: "";
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}

.appseo-main-header .appseo-main-navigation .dropdown .dropdown-menu li a:hover {
  background-color: #ff8533;
  color: #fff;
}

.appseo-main-header .appseo-main-navigation .dropdown .dropdown-menu li a:hover:after {
  transform: scale(1);
}

.appseo-main-header .appseo-main-navigation .dropdown .dropdown-menu li:last-child {
  border-bottom: none;
}

.appseo-main-header .appseo-main-navigation .dropdown:hover .dropdown-menu {
  top: 50px;
  opacity: 1;
  visibility: visible;
}

.appseo-main-header .appseo-sidebar-button {
  display: inline-block;
}

.appseo-main-header .appseo-sidebar-button .sidebar-appseo-toggle {
  width: 20px;
  height: 15px;
  cursor: pointer;
  margin-left: 65px;
}

.appseo-main-header .appseo-sidebar-button .sidebar-appseo-toggle span {
  position: relative;
}

.appseo-main-header .appseo-sidebar-button .sidebar-appseo-toggle span:before {
  content: "";
  position: absolute;
  background-color: #213e6e;
  transition: 0.3s all ease-in-out;
}

.appseo-main-header .appseo-sidebar-button .sidebar-appseo-toggle span:nth-child(1):before {
  top: 0;
  height: 3px;
  width: 14px;
}

.appseo-main-header .appseo-sidebar-button .sidebar-appseo-toggle span:nth-child(2):before {
  top: 4px;
  height: 3px;
  width: 19px;
}

.appseo-main-header .appseo-sidebar-button .sidebar-appseo-toggle span:nth-child(3):before {
  top: 8px;
  height: 3px;
  width: 15px;
}

.appseo-main-header .appseo-sidebar-button .sidebar-appseo-toggle span:nth-child(4):before {
  top: 12px;
  height: 3px;
  width: 20px;
}

.appseo-main-header .appseo-sidebar-button .sidebar-appseo-toggle:hover span:before {
  width: 20px;
}

.appseo-main-header .header-button {
  height: 58px;
  width: 185px;
  float: right;
  line-height: 58px;
  margin-left: 13px;
  border-radius: 32px;
  display: inline-block;
  background-size: 200% auto;
  transition: 0.3s all ease-in-out;
  box-shadow: 1.392px 9.903px 10px 0px rgba(66, 97, 234, 0.21);
  background-image: linear-gradient(45deg, #ff6626 15%, #ffa641 100%);
}

.appseo-main-header .header-button a {
  width: 100%;
  color: #fff;
  display: block;
  font-size: 14px;
  font-weight: 700;
}

.appseo-main-header .header-button:hover {
  transition: 0.3s all ease-in-out;
  background-position: right center;
}

.appseo-sticky-header-overlay {
  background-color: #fff;
  animation-duration: 0.7s;
  top: 0px;
  position: fixed;
  padding: 5px 0px;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
}
.appseo-main-header.appseo-sticky-header-overlay .appseo-logo {
  padding-top: 20px;
}
.appseo-main-header.appseo-sticky-header-overlay {
  z-index: 9;
  top: 0px;
  box-shadow: 0 0 20px -10px rgba(0, 0, 0, 0.8);
}
.appseo-main-header.appseo-sticky-header-overlay .header-button {
  margin-top: 6px;
}
.appseo-main-header.appseo-sticky-header-overlay .dropdown:hover .dropdown-menu {
  top: 45px;
}

.side_inner_content {
  top: 0px;
  bottom: 0;
  right: -320px;
  height: 110vh;
  z-index: 101;
  position: fixed;
  width: 300px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 50px 30px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s;
}

.side_inner_content p {
  text-align: left;
}

.side_inner_content .side_inner_logo {
  margin: 30px 0px;
}

.side_inner_content .side_contact {
  margin-bottom: 30px;
}

.side_inner_content .side_contact .social_widget h3 {
  font-size: 18px;
  padding: 10px 0px 20px 0px;
}

.side_inner_content .side_contact .social_widget li {
  margin: 0px 3px;
}

.side_inner_content .side_copywright {
  font-size: 14px;
}

.side_inner_content .close_btn {
  top: 30px;
  left: 20px;
  cursor: pointer;
  position: absolute;
}

.side_inner_content .close_btn i {
  font-size: 14px;
}

.wide_side_inner.wide_side_on .side_inner_content {
  right: -15px;
  z-index: 99;
  transition: all 0.7s;
}

.wide_side_inner {
  display: inline-block;
}

.wide_side_inner .side_overlay {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  width: 100%;
  z-index: 9;
  height: 110vh;
  visibility: hidden;
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  transition: all 0.3s ease-in-out;
}

.body_overlay_on {
  overflow: hidden;
}

.wide_side_inner.wide_side_on .side_overlay {
  opacity: 1;
  visibility: visible;
}

/*---------------------------------------------------- */
/*Banner area*/
/*----------------------------------------------------*/
@keyframes cd-rotate-3-in {
  0% {
    transform: rotateY(180deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}
@keyframes cd-rotate-3-out {
  0% {
    transform: rotateY(0);
  }
  100% {
    transform: rotateY(-180deg);
  }
}
.appseo-banner-section .cd-headline.rotate-3 .cd-words-wrapper {
  perspective: 300px;
  position: relative;
}

.appseo-banner-section .cd-headline.rotate-3 b {
  opacity: 0;
}

.appseo-banner-section .cd-headline.rotate-3 i {
  display: inline-block;
  transform: rotateY(180deg);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  font-style: normal;
  font-weight: 700;
}

.appseo-banner-section .cd-words-wrapper b {
  display: inline-block;
  position: absolute;
  white-space: nowrap;
  left: 0;
  top: 0;
}

.appseo-banner-section .is-visible .cd-headline.rotate-3 i {
  transform: rotateY(0deg);
}

.appseo-banner-section .cd-headline.rotate-3 i.in {
  animation: cd-rotate-3-in 0.6s forwards;
}

.appseo-banner-section .cd-headline.rotate-3 i.out {
  animation: cd-rotate-3-out 0.6s forwards;
}

.appseo-banner-section .no-csstransitions .cd-headline.rotate-3 i {
  transform: rotateY(0deg);
  opacity: 0;
}

.appseo-banner-section .no-csstransitions .cd-headline.rotate-3 .is-visible i {
  opacity: 1;
}

.appseo-banner-section .appseo-banner-content {
  max-width: 480px;
  padding: 380px 0px 70px 0px;
}

.appseo-banner-section .appseo-banner-content h1 {
  color: #213e6e;
  font-size: 50px;
  font-weight: 700;
  line-height: 1.3;
  padding-bottom: 15px;
}

.appseo-banner-section .appseo-banner-content .cd-words-wrapper b.is-visible {
  position: relative;
}

.appseo-banner-section .appseo-banner-content p {
  color: #213e6e;
  font-size: 20px;
  padding-bottom: 28px;
}

.appseo-banner-section .appseo-banner-content .banner-call-action {
  width: 100%;
  margin-bottom: 65px;
  display: inline-block;
}

.appseo-banner-section .appseo-banner-content .banner-call-action .call-action-icon {
  margin-right: 20px;
}

.appseo-banner-section .appseo-banner-content .banner-call-action .call-action-icon svg {
  height: 40px;
  width: 40px;
  fill: #213e6e;
}

.appseo-banner-section .appseo-banner-content .banner-call-action .call-action-text {
  color: #213e6e;
  font-size: 26px;
  font-weight: 300;
}

.appseo-banner-section .appseo-banner-content .banner-call-action .call-action-text span {
  color: #ff8533;
  font-weight: 600;
}

.appseo-banner-section .appseo-banner-content .banner-btn {
  display: flex;
}

.appseo-banner-section .appseo-banner-content .banner-btn .qut-btn {
  z-index: 1;
  width: 200px;
  height: 62px;
  position: relative;
  line-height: 62px;
  margin-right: 20px;
  border-radius: 52px;
  background-size: 200% auto;
  transition: 0.3s all ease-in-out;
  box-shadow: 1.392px 9.903px 10px 0px rgba(66, 97, 234, 0.21);
  background-image: linear-gradient(45deg, #2e42e2 15%, #4d71ee 100%);
}

.appseo-banner-section .appseo-banner-content .banner-btn .qut-btn a {
  display: block;
  width: 100%;
  color: #fff;
  font-size: 14px;
  font-weight: 700;
}

.appseo-banner-section .appseo-banner-content .banner-btn .qut-btn:hover {
  transition: 0.3s all ease-in-out;
  background-position: right center;
}

.appseo-banner-section .appseo-banner-content .banner-btn .banner-video-btn {
  z-index: 1;
  width: 70px;
  height: 70px;
  line-height: 72px;
  position: relative;
  border-radius: 100%;
  background-color: #fff;
}

.appseo-banner-section .appseo-banner-content .banner-btn .banner-video-btn:before {
  top: -15px;
  left: -15px;
  z-index: -1;
  content: "";
  width: 100px;
  height: 100px;
  position: absolute;
  border-radius: 100%;
  background-color: #fff3ea;
}

.appseo-banner-section .appseo-banner-content .banner-btn .banner-video-btn:after {
  top: 0;
  left: 0;
  z-index: -1;
  content: "";
  width: 70px;
  height: 70px;
  position: absolute;
  border-radius: 100%;
  background-color: #fff;
  box-shadow: 1.392px 9.903px 10px 0px rgba(66, 97, 234, 0.21);
}

.appseo-banner-section .appseo-banner-content .banner-btn .banner-video-btn a {
  width: 100%;
  display: block;
  color: #ff8533;
  font-size: 25px;
}

.appseo-banner-section .appseo-banner-content .banner-btn .banner-video-btn .video-border {
  position: absolute;
  z-index: -1;
  top: -15px;
  left: -15px;
  width: 100px;
  height: 100px;
  margin-left: 0.5px;
  border-radius: 50%;
  box-shadow: 0 0 0 0 #fff3ea;
  animation: videoup 3s infinite;
}

.appseo-banner-section .banner-shape {
  z-index: -1;
  position: absolute;
}

.appseo-banner-section .appseo-banner-shape1 {
  top: 0;
  left: -490px;
}

.appseo-banner-section .appseo-banner-shape2 {
  top: 300px;
  left: 140px;
}

.appseo-banner-section .appseo-banner-shape3 {
  top: 0;
  right: 0;
}

.appseo-banner-section .appseo-banner-shape4 {
  left: 50%;
  top: 260px;
  transform: translateX(-50%);
}

.appseo-banner-section .appseo-banner-vector {
  top: 200px;
  right: 140px;
  animation: appseoupdown 2s infinite alternate;
}

/*---------------------------------------------------- */
/*Servcie area*/
/*----------------------------------------------------*/
.appseo-service-section {
  padding: 85px 0px 120px;
}

.appseo-service-section .appseo-service-vector {
  position: absolute;
  bottom: 150px;
  left: 140px;
}

.appseo-service-content {
  padding-top: 40px;
}

.appseo-service-content .appseo-service-icon-text {
  z-index: 1;
  overflow: hidden;
  padding: 55px 65px;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 0px 35px 0px rgba(164, 177, 235, 0.13);
}

.appseo-service-content .appseo-service-icon-text .appseo-service-icon {
  width: 142px;
  height: 142px;
  display: flex;
  margin: 0 auto;
  margin-bottom: 42px;
  align-items: center;
  border-radius: 100%;
  justify-content: center;
  box-shadow: 1.392px 9.903px 10px 0px rgba(66, 97, 234, 0.21);
  background-image: linear-gradient(45deg, #6cc8e0 15%, #afe1ee 100%);
}

.appseo-service-content .appseo-service-icon-text .appseo-service-icon svg {
  height: 70px;
  fill: #fff;
}

.appseo-service-content .appseo-service-icon-text .appseo-service-icon:before,
.appseo-service-content .appseo-service-icon-text .appseo-service-icon:after {
  top: 15px;
  left: -25px;
  z-index: -1;
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-image: url(../img/seo/shape/sericon2.png);
}

.appseo-service-content .appseo-service-icon-text .appseo-service-icon:after {
  top: 0;
  left: auto;
  right: -45px;
  background-image: url(../img/seo/shape/sericon1.png);
}

.appseo-service-content .appseo-service-icon-text .appseo-service-text h3 {
  color: #213e6e;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 12px;
}

.appseo-service-content .appseo-service-icon-text .appseo-service-text p {
  color: #415e8d;
  line-height: 1.75;
}

.appseo-service-content .appseo-service-icon-text:before {
  top: -135px;
  right: -120px;
  z-index: -1;
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background-repeat: no-repeat;
  transition: 0.3s all ease-in-out;
  background-image: url(../img/seo/shape/s-shape1.png);
}

.appseo-service-content .appseo-service-box {
  transition: 0.3s all ease-in-out;
  background-color: #fff;
}

.appseo-service-content .appseo-service-box:after {
  left: 0;
  right: 0;
  content: "";
  height: 8px;
  bottom: -4px;
  width: 240px;
  margin: 0 auto;
  position: absolute;
  border-radius: 30px;
  background-color: #8fd5e7;
  transition: 0.3s all ease-in-out;
}

.appseo-service-content .appseo-service-box:hover {
  transform: translateY(40px);
}

.appseo-service-content .appseo-service-box:hover:after {
  width: 270px;
}

.appseo-service-content .appseo-service-box:hover .appseo-service-icon-text:before {
  top: -145px;
  right: -130px;
}

.appseo-service-content .col-lg-4:nth-child(2) .appseo-service-box:after {
  background-color: #ff8833;
}

.appseo-service-content .col-lg-4:nth-child(2) .appseo-service-box .appseo-service-icon {
  background-image: linear-gradient(45deg, #ff6626 15%, #ffa641 100%);
}

.appseo-service-content .col-lg-4:nth-child(3) .appseo-service-box:after {
  background-color: #3c58e8;
}

.appseo-service-content .col-lg-4:nth-child(3) .appseo-service-box .appseo-service-icon {
  background-image: linear-gradient(45deg, #2b3fe1 15%, #4f73ef 100%);
}

/*---------------------------------------------------- */
/*about area*/
/*----------------------------------------------------*/
.appseo-about-section {
  padding: 260px 0px 105px;
}

.appseo-about-section:before {
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url(../img/seo/shape/abbg.png);
}

.appseo-about-section .partner-slide-area {
  padding-top: 110px;
}

.appseo-about-section .partner-slide-area .owl-item img {
  width: inherit;
}

.appseo-about-section .partner-slide-area .owl-nav {
  display: none;
}

.appseo-about-section .appseo-about-img {
  top: 0;
  left: -110px;
  position: absolute;
}

.appseo-about-section .appseo-about-content {
  float: right;
  max-width: 580px;
  padding-right: 100px;
}

.appseo-about-section .appseo-about-content .appseo-section-title2 h2 {
  padding-bottom: 15px;
}

.appseo-about-section .appseo-about-content .appseo-about-quote {
  position: relative;
  margin: 25px 0px 46px;
}

.appseo-about-section .appseo-about-content .appseo-about-quote .quote-icon-about {
  top: 0;
  left: 0;
  position: absolute;
}

.appseo-about-section .appseo-about-content .appseo-about-quote .quote-icon-about svg {
  height: 40px;
  fill: #ff8533;
}

.appseo-about-section .appseo-about-content .appseo-about-quote .quote-text-about {
  color: #ff8533;
  font-weight: 600;
  padding-left: 65px;
  font-style: italic;
}

.appseo-about-section .appseo-about-content .appseo-about-btn {
  color: #fff;
  height: 60px;
  width: 220px;
  font-size: 14px;
  font-weight: 700;
  line-height: 60px;
  border-radius: 40px;
  background-size: 200% auto;
  transition: 0.3s all ease-in-out;
  box-shadow: 1.392px 9.903px 10px 0px rgba(66, 97, 234, 0.21);
  background-image: linear-gradient(45deg, #2e42e2 15%, #4d71ee 100%);
}

.appseo-about-section .appseo-about-content .appseo-about-btn a {
  width: 100%;
  display: block;
}

.appseo-about-section .appseo-about-content .appseo-about-btn:hover {
  transition: 0.3s all ease-in-out;
  background-position: right center;
}

/*---------------------------------------------------- */
/*ready-go area*/
/*----------------------------------------------------*/
.ready-to-grow-section {
  overflow: visible;
  padding: 120px 0px 130px;
  z-index: 1;
  background-color: #00183e;
}

.ready-to-grow-section:before {
  left: 0;
  bottom: 0;
  content: "";
  z-index: -1;
  width: 100%;
  height: 100%;
  position: absolute;
  background-size: cover;
  background-image: url(../img/seo/shape/r-shape.png);
}

.ready-to-grow-section .ready-vector {
  top: -20px;
  left: 50px;
  position: absolute;
  animation: appseoupdown 2s infinite alternate;
}

.ready-to-grow-section .ready-go-title {
  color: #fff;
  padding-bottom: 50px;
}

.ready-to-grow-section .ready-go-title span {
  font-size: 24px;
  letter-spacing: 3px;
  text-transform: uppercase;
}

.ready-to-grow-section .ready-go-title h2 {
  font-size: 40px;
  font-weight: 700;
}

.ready-to-grow-section .ready-go-form {
  margin: 0 auto;
  max-width: 955px;
}

.ready-to-grow-section .ready-go-form .ready-go-input-area {
  height: 60px;
  overflow: hidden;
  position: relative;
  border-radius: 40px;
}

.ready-to-grow-section .ready-go-form .ready-go-input-area:before {
  top: 0;
  left: 41%;
  width: 2px;
  content: "";
  height: 100px;
  position: absolute;
  background-color: #dddddd;
}

.ready-to-grow-section .ready-go-form .ready-go-input-area input {
  float: left;
  width: 395px;
  height: 60px;
  border: none;
  padding-left: 30px;
}

.ready-to-grow-section .ready-go-form .sub-btn {
  position: absolute;
  top: 0;
  right: 0;
}

.ready-to-grow-section .ready-go-form .sub-btn button {
  color: #fff;
  height: 60px;
  width: 220px;
  border: none;
  font-size: 14px;
  font-weight: 700;
  line-height: 60px;
  border-radius: 40px;
  text-transform: uppercase;
  background-image: linear-gradient(45deg, #ff6525 15%, #ffa942 100%);
}

/*---------------------------------------------------- */
/*appseo-mission area*/
/*----------------------------------------------------*/
.appseo-mission-area-section {
  padding: 125px 0px 0px 0px;
  position: relative;
  z-index: 1;
}

.appseo-mission-area-section:after {
  top: -30%;
  right: -55%;
  content: "";
  width: 100%;
  height: 100%;
  z-index: -1;
  position: absolute;
  background-repeat: no-repeat;
  background-image: url(../img/seo/shape/rg-shape1.png);
}

.appseo-mission-area-section .appseo-mission-vector {
  right: 0;
  top: 120px;
  position: absolute;
}

.appseo-mission-area-text {
  max-width: 480px;
}

.appseo-mission-area-text .appseo-section-title2 {
  padding-bottom: 85px;
}

.appseo-mission-area-text .appseo-mission-area-item .appseo-mission-item-content {
  width: 100%;
  margin-bottom: 40px;
  display: inline-block;
}

.appseo-mission-area-text .appseo-mission-area-item .appseo-mission-item-content .appseo-mission-item-icon {
  z-index: 1;
  top: 30px;
  float: left;
  width: 115px;
  height: 112px;
  margin-left: 30px;
  position: relative;
}

.appseo-mission-area-text .appseo-mission-area-item .appseo-mission-item-content .appseo-mission-item-icon svg {
  height: 50px;
  fill: #fff;
}

.appseo-mission-area-text .appseo-mission-area-item .appseo-mission-item-content .appseo-mission-item-icon:before {
  top: -40px;
  left: -35px;
  content: "";
  z-index: -1;
  width: 130px;
  height: 130px;
  position: absolute;
  background-repeat: no-repeat;
  background-image: url(../img/seo/shape/ms1.png);
}

.appseo-mission-area-text .appseo-mission-area-item .appseo-mission-item-content:nth-child(2) .appseo-mission-item-icon:before {
  background-image: url(../img/seo/shape/ms2.png);
}

.appseo-mission-area-text .appseo-mission-area-item .appseo-mission-item-content:nth-child(3) .appseo-mission-item-icon:before {
  background-image: url(../img/seo/shape/ms3.png);
}

.appseo-mission-area-text .appseo-mission-area-item .appseo-mission-item-content .appseo-mission-area-text h3 {
  color: #213e6e;
  font-size: 24px;
  font-weight: 700;
  padding-bottom: 15px;
}

/*---------------------------------------------------- */
/*pricing area*/
/*----------------------------------------------------*/
.appseo-pricing-section {
  position: relative;
  padding-top: 80px;
}

.appseo-pricing-section:after {
  top: 35%;
  left: 0;
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url(../img/seo/shape/abbg.png);
}

.appseo-pricing-section .appseo-pricing-title {
  margin: 0 auto;
  max-width: 675px;
  padding-bottom: 125px;
}

.appseo-pricing-section .appseo-pricing-title h2 {
  color: #213e6e;
  font-size: 40px;
  font-weight: 700;
}

.appseo-pricing-section .appseo-pricing-item {
  padding-bottom: 80px;
  position: relative;
  z-index: 1;
}

.appseo-pricing-section .appseo-pricing-item:after {
  content: "";
  height: 275px;
  width: 275px;
  bottom: 15px;
  right: -100px;
  z-index: -1;
  background-image: url(../img/seo/shape/pr-shape.png);
  position: absolute;
}

.appseo-pricing-section .appseo-pricing-inner-box {
  z-index: 1;
  margin: 0 auto;
  max-width: 350px;
  position: relative;
  border-radius: 10px;
  background-color: #fff;
  padding: 125px 45px 50px 45px;
  box-shadow: 0px 0px 35px 0px rgba(164, 177, 235, 0.13);
}

.appseo-pricing-section .appseo-pricing-inner-box:after {
  right: 0;
  bottom: 0;
  z-index: -1;
  content: "";
  width: 210px;
  height: 125px;
  position: absolute;
  background-image: url(../img/seo/shape/prs.png);
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-img {
  top: -90px;
  left: 0;
  right: 0;
  position: absolute;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-price-title h3 {
  font-size: 20px;
  font-weight: 600;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-price {
  font-weight: 600;
  line-height: 1;
  padding: 20px 0px 5px;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-price span {
  color: #3d59e8;
  font-size: 50px;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-price strong {
  line-height: 1;
  color: #213e6e;
  font-size: 60px;
  font-weight: 600;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-module {
  margin-bottom: 20px;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-module span {
  font-weight: 600;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-list li {
  padding: 8px 0px;
  border-bottom: 2px dashed #e9e9e9;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-list li:last-child {
  border-bottom: none;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-btn {
  color: #fff;
  height: 60px;
  width: 260px;
  font-size: 14px;
  margin-top: 15px;
  font-weight: 700;
  line-height: 60px;
  border-radius: 40px;
  text-transform: uppercase;
  transition: 0.3s all ease-in-out;
  background-size: 200% auto;
  box-shadow: 1.392px 9.903px 10px 0px rgba(66, 97, 234, 0.21);
  background-image: linear-gradient(45deg, #2e42e2 15%, #4d71ee 100%);
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-btn a {
  display: block;
  width: 100%;
}

.appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-btn:hover {
  transition: 0.3s all ease-in-out;
  background-position: right center;
}

.appseo-pricing-section .appseo-pricing-inner-box.popular-appseo {
  top: 35px;
  position: relative;
}

.appseo-pricing-section .appseo-pricing-inner-box.popular-appseo .appseo-pricing-btn {
  background-image: linear-gradient(45deg, #ff6626 15%, #ffa641 100%);
}

/*---------------------------------------------------- */
/*testimonial area*/
/*----------------------------------------------------*/
.appseo-testimonial-section {
  padding: 40px 0px 95px;
  background-color: #f4f8ff;
}

.appseo-testimonial-section .testi-circle-shape {
  position: absolute;
  top: 0;
  left: -30%;
}

.appseo-testimonial-content {
  z-index: 1;
  margin: 0 auto;
  max-width: 705px;
  margin-top: 30px;
  position: relative;
}

.appseo-testimonial-content:before {
  left: 0;
  right: 0;
  top: -15px;
  z-index: -1;
  line-height: 1;
  color: #e9f0fb;
  content: "";
  font-weight: 900;
  font-size: 205px;
  position: absolute;
  text-align: center;
  font-family: "Font Awesome 5 Free";
}

.appseo-testimonial-content .slick-dots {
  display: none;
}

.appseo-testimonial-content .slick-dotted.slick-slider {
  margin-bottom: 0;
}

.appseo-testimonial-content .appseo-testimonial-quote {
  position: relative;
  padding-bottom: 110px;
}

.appseo-testimonial-content .appseo-testimonial-quote:before {
  left: 0;
  right: 0;
  bottom: 70px;
  height: 5px;
  content: "";
  width: 290px;
  margin: 0 auto;
  position: absolute;
  background-color: #ff8533;
}

.appseo-testimonial-content .appseo-testimonial-text {
  font-size: 24px;
  font-style: italic;
}

.appseo-testimonial-content .appseo-testimonial-trigger {
  position: relative;
}

.appseo-testimonial-content .appseo-testimonial-trigger:before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  top: -70px;
  left: -24px;
  right: 0;
  margin: 0 auto;
  border-left: 50px solid transparent;
  border-right: 0px solid transparent;
  border-top: 30px solid #ff8533;
}

.appseo-testimonial-content .appseo-testimonial-trigger:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  right: 0;
  top: -75px;
  left: -28px;
  margin: 0 auto;
  border-left: 45px solid transparent;
  border-right: 0px solid transparent;
  border-top: 25px solid #fff;
}

.appseo-testimonial-content .appseo-testimonial-trigger .slick-list {
  margin: 0 auto;
  width: 645px !important;
  padding: 0px !important;
}

.appseo-testimonial-content .appseo-testimonial-trigger .appseo-testi-name-degi {
  margin: 0px 30px;
}

.appseo-testimonial-content .appseo-testimonial-trigger .appseo-testi-name-degi h3 {
  font-size: 30px;
  font-weight: 700;
  color: rgba(61, 89, 232, 0.4);
  transition: 0.3s all ease-in-out;
}

.appseo-testimonial-content .appseo-testimonial-trigger .appseo-testi-name-degi span {
  opacity: 0.5;
  color: #999999;
  text-transform: uppercase;
  transition: 0.3s all ease-in-out;
}

.appseo-testimonial-content .appseo-testimonial-trigger .appseo-testi-name-degi.slick-current.slick-center h3 {
  color: #ff8533;
}

.appseo-testimonial-content .appseo-testimonial-trigger .appseo-testi-name-degi.slick-current.slick-center span {
  opacity: 1;
}

/*---------------------------------------------------- */
/*Case area*/
/*----------------------------------------------------*/
.appseo-case-study-section {
  padding: 110px 0px 70px;
  max-width: 1920px;
  margin: 0 auto;
}

.appseo-case-study-section .appseo-section-title {
  margin-bottom: 40px;
}

.appseo-case-study-content {
  display: flex;
  margin: 0 -15px;
  flex-wrap: nowrap;
}

.appseo-case-study-img-text {
  width: 390px;
  margin: 5px;
}

.appseo-case-study-img-text .appseo-case-img {
  overflow: hidden;
  position: relative;
  border-radius: 15px;
}

.appseo-case-study-img-text .appseo-case-img:after {
  top: 0;
  left: 0;
  opacity: 0;
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  transform: scale(0.5);
  border-radius: 15px;
  transition: 0.5s all ease-in-out;
  background-image: linear-gradient(45deg, #ff6626 15%, #ffa641 100%);
}

.appseo-case-study-img-text .appseo-case-img img {
  border-radius: 15px;
  transition: 0.5s all ease-in-out;
}

.appseo-case-study-img-text .appseo-case-text {
  top: -45px;
  left: 25px;
  padding: 25px 30px;
  max-width: 352px;
  border-radius: 20px;
  position: relative;
  background-color: #fff;
  border-top-right-radius: 0;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 0px 35px 0px rgba(164, 177, 235, 0.13);
}

.appseo-case-study-img-text .appseo-case-text span {
  color: #ff8533;
  font-size: 14px;
  letter-spacing: 3px;
  text-transform: uppercase;
}

.appseo-case-study-img-text .appseo-case-text h3 {
  padding-top: 10px;
  color: #213e6e;
  font-size: 22px;
  line-height: 1.4;
  font-weight: 700;
  transition: 0.3s all ease-in-out;
}

.appseo-case-study-img-text:hover .appseo-case-text {
  background-color: #244479;
}

.appseo-case-study-img-text:hover .appseo-case-text h3 {
  color: #fff;
}

.appseo-case-study-img-text:hover .appseo-case-img img {
  transform: scale(1.2);
}

.appseo-case-study-img-text:hover .appseo-case-img:after {
  opacity: 0.8;
  transform: scale(1);
}

/*---------------------------------------------------- */
/*CTA area*/
/*----------------------------------------------------*/
.appseo-cta-section {
  z-index: 1;
  overflow: visible;
  padding: 120px 0px 105px;
}

.appseo-cta-section .appseo-call-vector {
  top: 45px;
  right: 220px;
  position: absolute;
  animation: flying 1.5s linear infinite;
}

.appseo-cta-section:before {
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background-image: url(../img/seo/banner/ctabg.jpg);
}

.appseo-cta-section .overlay-bg {
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  opacity: 0.9;
  position: absolute;
  background-image: linear-gradient(45deg, #05297d 15%, #0e75e6 100%);
}

.appseo-cta-section .appseo-cta-content {
  padding-left: 125px;
}

.appseo-cta-section .appseo-cta-content h2 {
  color: #fff;
  font-size: 40px;
  font-weight: 700;
}

.appseo-cta-section .appseo-call-btn-area {
  padding-left: 90px;
}

.appseo-cta-section .appseo-cta-btn {
  color: #fff;
  z-index: 5;
  height: 70px;
  width: 255px;
  font-size: 14px;
  font-weight: 700;
  line-height: 70px;
  position: relative;
  border-radius: 40px;
  text-transform: uppercase;
  background-image: linear-gradient(45deg, #ff6525 15%, #ffa942 100%);
}

.appseo-cta-section .appseo-cta-btn a {
  width: 100%;
  display: block;
}

.bubble-dotted {
  left: 0;
  top: 0;
  right: 0;
  width: 100%;
  z-index: 1;
  height: 100%;
  overflow: hidden;
  position: absolute;
  pointer-events: none;
}

.bubble-dotted .dotted {
  position: absolute;
  border-radius: 50%;
}

.bubble-dotted .dotted-1 {
  width: 11px;
  height: 11px;
  left: 100px;
  top: 50%;
  animation: animateBubble 15s linear infinite, sideWays 2s ease-in-out infinite alternate;
}

.bubble-dotted .dotted-2 {
  width: 7px;
  height: 7px;
  left: 240px;
  top: 40%;
  animation: animateBubble 10s linear infinite, sideWays 4s ease-in-out infinite alternate;
}

.bubble-dotted .dotted-3 {
  width: 11px;
  height: 11px;
  left: 460px;
  top: 30%;
  animation: animateBubble 18s linear infinite, sideWays 2s ease-in-out infinite alternate;
}

.bubble-dotted .dotted-4 {
  width: 16px;
  height: 16px;
  left: 430px;
  top: 90%;
  animation: animateBubble 12s linear infinite, sideWays 3s ease-in-out infinite alternate;
}

.bubble-dotted .dotted-5 {
  width: 6px;
  height: 6px;
  left: 50%;
  top: 50%;
  animation: animateBubble 19s linear infinite, sideWays 4s ease-in-out infinite alternate;
}

.bubble-dotted .dotted-6 {
  width: 9px;
  height: 9px;
  left: 70%;
  top: 230px;
  animation: animateBubble 11s linear infinite, sideWays 2s ease-in-out infinite alternate;
}

.bubble-dotted .dotted-7 {
  width: 6px;
  height: 6px;
  left: 65%;
  top: 30%;
  animation: animateBubble 10s linear infinite, sideWays 2s ease-in-out infinite alternate;
}

.bubble-dotted .dotted-8 {
  width: 6px;
  height: 6px;
  left: 85%;
  top: 35%;
  animation: animateBubble 12s linear infinite, sideWays 3s ease-in-out infinite alternate;
}

.bubble-dotted .dotted-9 {
  width: 13px;
  height: 13px;
  left: 90%;
  top: 40%;
  animation: animateBubble 19s linear infinite, sideWays 4s ease-in-out infinite alternate;
}

.bubble-dotted .dotted-10 {
  width: 12px;
  height: 12px;
  left: 80%;
  top: 70%;
  animation: animateBubble 16s linear infinite, sideWays 2s ease-in-out infinite alternate;
}

.appseo-cta-section .bubble-dotted .dotted {
  position: absolute;
  background: #fff;
  border-radius: 50%;
}

.appseo-cta-section .bubble-dotted .dotted-1 {
  width: 6px;
  height: 6px;
  left: 270px;
  top: 100px;
}

.appseo-cta-section .bubble-dotted .dotted-2 {
  width: 6px;
  height: 6px;
  left: 190px;
  top: 210px;
}

.appseo-cta-section .bubble-dotted .dotted-3 {
  width: 11px;
  height: 11px;
  left: 150px;
  top: 90%;
}

.appseo-cta-section .bubble-dotted .dotted-4 {
  width: 11px;
  height: 11px;
  left: 25%;
  top: 100px;
}

.appseo-cta-section .bubble-dotted .dotted-5 {
  width: 11px;
  height: 11px;
  left: 45%;
  top: 60px;
}

.appseo-cta-section .bubble-dotted .dotted-6 {
  width: 6px;
  height: 6px;
  left: 50%;
  top: 370px;
}

.appseo-cta-section .bubble-dotted .dotted-7 {
  width: 9px;
  height: 9px;
  left: 75%;
  top: 25px;
}

.appseo-cta-section .bubble-dotted .dotted-8 {
  width: 8px;
  height: 8px;
  left: 77%;
  top: 170px;
}

.appseo-cta-section .bubble-dotted .dotted-9 {
  width: 6px;
  height: 6px;
  left: 85%;
  top: 50%;
}

.appseo-cta-section .bubble-dotted .dotted-10 {
  width: 6px;
  height: 6px;
  left: 90%;
  top: 80%;
}

/*---------------------------------------------------- */
/*blog area*/
/*----------------------------------------------------*/
.appseo-blog-section {
  padding: 110px 0px 120px;
  background-color: #f4f8ff;
}

.appseo-blog-section .appseo-section-title {
  margin-bottom: 50px;
}

.appseo-blog-img-text {
  z-index: 1;
  position: relative;
}

.appseo-blog-img-text:before {
  left: -5px;
  z-index: -1;
  content: "";
  width: 100%;
  top: 150px;
  height: 300px;
  border-radius: 10px;
  position: absolute;
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}

.appseo-blog-img-text .appseo-blog-img .appseo-post-meta {
  right: 25px;
  height: 88px;
  width: 85px;
  bottom: -10px;
  color: #fff;
  height: 90px;
  line-height: 1;
  font-size: 15px;
  padding-top: 15px;
  position: absolute;
  border-radius: 10px;
  box-shadow: 1.392px 9.903px 10px 0px rgba(66, 97, 234, 0.21);
  background-image: linear-gradient(45deg, #2e42e2 15%, #4d71ee 100%);
}

.appseo-blog-img-text .appseo-blog-img .appseo-post-meta span {
  line-height: 1;
  display: block;
  font-size: 40px;
  font-weight: 700;
}

.appseo-blog-img-text .appseo-blog-text {
  padding: 30px 45px;
}

.appseo-blog-img-text .appseo-blog-text h3 {
  line-height: 1.364;
  color: #213e6e;
  font-size: 22px;
  font-weight: 600;
  transition: 0.3s all ease-in-out;
}

.appseo-blog-img-text .appseo-blog-text .appseo-blog-meta span {
  color: #415e8d;
  transition: 0.3s all ease-in-out;
}

.appseo-blog-img-text .appseo-blog-text .appseo-blog-meta a {
  color: #ff8533;
}

.appseo-blog-img-text:hover:before {
  background-color: #1d3762;
}

.appseo-blog-img-text:hover .appseo-blog-img .appseo-post-meta {
  background-image: linear-gradient(45deg, #ff6626 15%, #ffa641 100%);
}

.appseo-blog-img-text:hover .appseo-blog-text h3 {
  color: #fff;
}

.appseo-blog-img-text:hover .appseo-blog-meta span {
  color: #fff;
}

/*---------------------------------------------------- */
/*newsletter area*/
/*----------------------------------------------------*/
.appseo-newsletter-section {
  padding: 100px 0px;
}

.appseo-newsletter-section .newsletter-vector {
  left: -125px;
  bottom: -150px;
  position: absolute;
}

.appseo-newsletter-text h2 {
  color: #213e6e;
  font-size: 40px;
  font-weight: 800;
}

.appseo-newsletter-box {
  padding-left: 100px;
}

.appseo-newsletter-box input {
  width: 100%;
  height: 70px;
  border: none;
  padding-left: 30px;
  border-radius: 40px;
  box-shadow: 0px 5px 24px 0px rgba(0, 0, 0, 0.09);
}

.appseo-newsletter-box .nws-button {
  top: 8px;
  right: 15px;
}

.appseo-newsletter-box .nws-button button {
  color: #fff;
  height: 55px;
  width: 160px;
  border: none;
  font-size: 14px;
  font-weight: 700;
  border-radius: 30px;
  text-transform: uppercase;
  background-color: transparent;
  box-shadow: 1.392px 9.903px 10px 0px rgba(66, 97, 234, 0.21);
  background-image: linear-gradient(45deg, #ff6525 15%, #ffa942 100%);
}

/*---------------------------------------------------- */
/*footer area*/
/*----------------------------------------------------*/
.appseo-footer-area-section {
  padding: 120px 0px 110px;
}

.appseo-footer-area-section:before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-color: rgba(0, 5, 46, 0.9);
  top: 0;
  left: 0;
}

.appseo-footer-widget .appseo-widget-title {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 0;
  padding-bottom: 35px;
}

.appseo-footer-widget .footer-logo-widget .appseo-footer-logo {
  margin-bottom: 20px;
}

.appseo-footer-widget .footer-logo-widget .appseo-footer-about p {
  color: #dddddd;
  max-width: 490px;
  line-height: 1.875;
  padding-bottom: 15px;
}

.appseo-footer-widget .footer-logo-widget .appseo-footer-about strong {
  color: #6d83f9;
  font-size: 14px;
  font-weight: 700;
}

.appseo-footer-widget .footer-logo-widget .appseo-footer-about span {
  color: #fff;
  display: block;
  font-size: 14px;
  max-width: 170px;
}

.appseo-footer-widget .appseo-footer-menu-widget li {
  padding-left: 30px;
  position: relative;
  margin-bottom: 5px;
}

.appseo-footer-widget .appseo-footer-menu-widget li:before,
.appseo-footer-widget .appseo-footer-menu-widget li:after {
  left: 0;
  top: 10px;
  width: 7px;
  content: "";
  height: 7px;
  position: absolute;
  border-radius: 100%;
  background-color: #ff8533;
}

.appseo-footer-widget .appseo-footer-menu-widget li:after {
  left: 10px;
}

.appseo-footer-widget .appseo-footer-menu-widget li a {
  color: #c9c9c9;
  transition: 0.3s all ease-in-out;
}

.appseo-footer-widget .appseo-footer-menu-widget li a:hover {
  color: #ff8533;
}

.appseo-copyright {
  margin-top: 30px;
  width: 100%;
  position: relative;
  display: inline-block;
  z-index: 1;
}

.appseo-copyright:after {
  top: 18px;
  content: "";
  height: 2px;
  width: 450px;
  right: 20%;
  z-index: -1;
  margin: 0 auto;
  position: absolute;
  background-color: #333a66;
}

.appseo-copyright .appseo-copyright-text {
  float: left;
}

.appseo-copyright .appseo-copyright-text span {
  color: #dddddd;
}

.appseo-copyright .appseo-copyright-text span a {
  color: #ff8533;
}

.appseo-copyright .appseo-footer-social {
  float: right;
}

.appseo-copyright .appseo-footer-social a {
  width: 45px;
  color: #fff;
  height: 45px;
  margin-left: 5px;
  line-height: 45px;
  text-align: center;
  border-radius: 5px;
  display: inline-block;
}

.appseo-copyright .appseo-footer-social a:nth-child(1) {
  background-color: #2662db;
}

.appseo-copyright .appseo-footer-social a:nth-child(2) {
  background-color: #e2463d;
}

.appseo-copyright .appseo-footer-social a:nth-child(3) {
  background-color: #28c5f4;
}

.appseo-copyright .appseo-footer-social a:nth-child(4) {
  background-color: #206096;
}

.appseo-copyright .appseo-footer-social a:nth-child(5) {
  background-color: #d0101f;
}

/*---------------------------------------------------- */
/*mobile menu area*/
/*----------------------------------------------------*/
.appseo-mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 280px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 40px 0px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s ease-in;
}

.appseo-mobile_menu_content .appseo-mobile-main-navigation {
  width: 100%;
}

.appseo-mobile_menu_content .appseo-mobile-main-navigation .navbar-nav {
  width: 100%;
}

.appseo-mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}

.appseo-mobile_menu_content .appseo-mobile-main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid #dcdcdc;
}

.appseo-mobile_menu_content .appseo-mobile-main-navigation .navbar-nav li:first-child {
  border-top: 1px solid #dcdcdc;
}

.appseo-mobile_menu_content .appseo-mobile-main-navigation .navbar-nav li a {
  color: #000;
  padding: 0;
  width: 100%;
  display: block;
  font-size: 14px;
  font-weight: 400;
  padding: 5px 30px;
  text-transform: uppercase;
}

.appseo-mobile_menu_content .m-brand-logo {
  width: 160px;
  margin: 0 auto;
  margin-bottom: 30px;
}

.appseo-mobile_menu_wrap.mobile_menu_on .appseo-mobile_menu_content {
  right: 0px;
  transition: all 0.7s ease-out;
}

.mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: 0%;
  height: 120vh;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.5s ease-in-out;
}

.mobile_menu_overlay_on {
  overflow: hidden;
}

.appseo-mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  opacity: 1;
  visibility: visible;
}

.appseo-mobile_menu_button {
  position: absolute;
  display: none;
  right: 0;
  cursor: pointer;
  line-height: 40px;
  color: #ff8833;
  text-align: center;
  font-size: 30px;
  top: -40px;
  z-index: 5;
}

.appseo-mobile_menu .appseo-mobile-main-navigation .navbar-nav li a:after {
  display: none;
}

.appseo-mobile_menu .appseo-mobile-main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}

.appseo-mobile_menu .appseo-mobile_menu_content .appseo-mobile-main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
  width: 100%;
  border-top: 1px solid #dcdcdc;
}

.appseo-mobile_menu .appseo-mobile_menu_content .appseo-mobile-main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 0 20px;
  line-height: 1;
}

.appseo-mobile_menu .dropdown {
  position: relative;
}

.appseo-mobile_menu .dropdown .dropdown-btn {
  position: absolute;
  top: 0px;
  right: 0;
  height: 30px;
  padding: 5px 10px;
}

.appseo-mobile_menu .dropdown .dropdown-btn:before {
  content: "";
  position: absolute;
  height: 100%;
  width: 1px;
  top: 0;
  left: 0;
  background-color: #dcdcdc;
}

.appseo-mobile_menu .appseo-mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

/*---------------------------------------------------- */
/*responsive area*/
/*----------------------------------------------------*/
/* ==================================================
* 01 - media screen and (max-width: 1440px)
* 02 - media screen and (max-width: 1280px)
* 03 - media screen and (max-width: 1199px)
* 04 - media screen and (max-width: 991px)
* 05 - media screen and (max-width: 767px)
* 06 - media screen and (max-width: 680px)
* 07 - media screen and (max-width: 580px)
* 08 - media screen and (max-width: 480px)
* 09 - media screen and (max-width: 380px)
* 10 - media screen and (max-width: 320px)
================================================== */
@media screen and (max-width: 1440px) {
  .appseo-banner-section .appseo-banner-shape2 {
    left: 0;
  }

  .appseo-banner-section .appseo-banner-vector {
    right: -65px;
  }

  .appseo-service-section .appseo-service-vector {
    left: -20px;
  }

  .ready-to-grow-section .ready-vector {
    left: -160px;
  }

  .appseo-mission-area-section .appseo-mission-vector {
    right: -24%;
  }

  .appseo-case-study-img-text .appseo-case-text h3 {
    font-size: 18px;
  }

  .appseo-case-study-img-text .appseo-case-text {
    left: 11px;
    padding: 20px 20px;
    max-width: 255px;
  }

  .appseo-cta-section .appseo-call-vector {
    right: 45px;
  }
}
@media screen and (max-width: 1280px) {
  .appseo-case-study-content {
    flex-wrap: wrap;
  }

  .appseo-case-study-img-text .appseo-case-text {
    left: 25px;
    padding: 25px 30px;
    max-width: 365px;
  }
}
@media screen and (max-width: 1199px) {
  .appseo-banner-section .appseo-banner-vector {
    right: -125px;
  }

  .appseo-mission-area-section .appseo-mission-vector {
    right: -35%;
  }

  .appseo-testimonial-section .testi-circle-shape {
    display: none;
  }

  .appseo-about-section .appseo-about-img {
    left: -260px;
  }
}
@media screen and (max-width: 1045px) {
  .appseo-main-header .header-button {
    width: 150px;
  }

  .appseo-banner-section .appseo-banner-shape2 {
    display: none;
  }

  .appseo-banner-section .appseo-banner-vector {
    right: -310px;
  }

  .appseo-service-section .appseo-service-vector {
    z-index: -1;
  }

  .appseo-mission-area-section .appseo-mission-vector {
    right: -50%;
  }

  .appseo-case-study-content {
    justify-content: center;
  }

  .appseo-cta-section .appseo-cta-content {
    padding-left: 0;
  }
}
@media screen and (max-width: 991px) {
  .appseo-banner-section .appseo-banner-vector {
    position: static;
  }

  .appseo-banner-section .appseo-banner-content {
    padding: 200px 0px 50px;
  }

  .appseo-service-section {
    padding: 60px 0px 20px;
  }

  .appseo-service-content .appseo-service-box {
    margin: 0 auto;
    max-width: 370px;
    background-color: #fff;
    margin-bottom: 60px;
  }

  .appseo-about-section .appseo-about-img {
    position: static;
    margin-bottom: 40px;
  }

  .ready-to-grow-section .ready-vector {
    display: none;
  }

  .ready-to-grow-section .ready-go-form .ready-go-input-area {
    height: auto;
    border-radius: 0;
  }

  .ready-to-grow-section .ready-go-form .ready-go-input-area:before {
    display: none;
  }

  .ready-to-grow-section .ready-go-form .ready-go-input-area input {
    width: 100%;
    border-radius: 40px;
    margin-bottom: 30px;
  }

  .ready-to-grow-section .ready-go-form .sub-btn {
    position: static;
  }

  .appseo-mission-area-section .appseo-mission-vector {
    display: none;
  }

  .appseo-pricing-section .appseo-pricing-inner-box {
    margin-bottom: 140px;
  }

  .appseo-case-study-content {
    justify-content: center;
  }

  .appseo-cta-section .appseo-cta-btn {
    margin: 0 auto;
  }

  .appseo-cta-section .appseo-cta-content {
    padding-bottom: 30px;
  }

  .appseo-cta-section .appseo-call-vector {
    display: none;
  }

  .appseo-blog-img-text {
    max-width: 370px;
    margin: 0 auto;
    margin-bottom: 40px;
  }

  .appseo-newsletter-box {
    padding-left: 0;
    margin-top: 20px;
  }

  .appseo-newsletter-box .nws-button {
    top: 28px;
  }

  .appseo-copyright:after {
    display: none;
  }

  .appseo-menu-wrapper {
    display: none;
  }

  .appseo-mobile_menu_button {
    display: block;
  }

  .appseo-footer-widget .appseo-widget-title {
    padding-bottom: 20px;
  }

  .appseo-footer-widget {
    margin-bottom: 40px;
  }

  .appseo-main-header {
    padding-top: 30px;
  }

  .appseo-main-header.appseo-sticky-header-overlay {
    padding: 15px 0px 10px;
  }
  .appseo-about-section .appseo-about-content {
    float: none;
  }
  .appseo-main-header.appseo-sticky-header-overlay .appseo-logo {
    padding-top: 0;
  }
  .appseo-pricing-section {
    padding-top: 0;
  }
}
@media screen and (max-width: 580px) {
  .appseo-banner-section .appseo-banner-shape2 {
    display: none;
  }

  .appseo-about-section {
    padding-top: 20px;
  }

  .appseo-cta-section .appseo-cta-content h2 {
    font-size: 32px;
  }

  .appseo-blog-section {
    padding-bottom: 40px;
  }
}
@media screen and (max-width: 480px) {
  .appseo-banner-section .appseo-banner-content h1 {
    font-size: 40px;
  }

  .appseo-banner-section .appseo-banner-content .banner-call-action .call-action-text {
    font-size: 22px;
  }

  .appseo-section-title h2 {
    font-size: 32px;
  }

  .appseo-section-title2 h2 {
    font-size: 32px;
  }

  .ready-to-grow-section .ready-go-title h2 {
    font-size: 32px;
  }

  .ready-to-grow-section .ready-go-title span {
    font-size: 18px;
  }

  .ready-to-grow-section .ready-go-title {
    padding-bottom: 30px;
  }

  .appseo-pricing-section .appseo-pricing-item:after {
    display: none;
  }

  .appseo-case-study-img-text .appseo-case-text h3 {
    font-size: 22px;
  }

  .appseo-cta-section .appseo-cta-content {
    padding-left: 0;
  }

  .appseo-cta-section .appseo-cta-btn {
    margin: 0;
  }

  .appseo-cta-section .appseo-call-btn-area {
    padding-left: 0;
  }
}
@media screen and (max-width: 420px) {
  .appseo-banner-section .appseo-banner-content {
    padding: 180px 0px 50px;
  }

  .appseo-banner-section .appseo-banner-content .banner-call-action {
    margin-bottom: 40px;
  }

  .appseo-banner-section .appseo-banner-content .banner-btn .qut-btn {
    line-height: 55px;
    width: 175px;
    height: 55px;
  }

  .appseo-about-section .appseo-about-content {
    padding-right: 0;
  }

  .appseo-pricing-section .appseo-pricing-title h2 {
    font-size: 30px;
  }

  .ready-to-grow-section .ready-go-form .sub-btn button {
    width: 185px;
    height: 50px;
    line-height: 50px;
  }

  .appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-price strong {
    font-size: 50px;
  }

  .appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-price span {
    font-size: 30px;
  }

  .appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-btn {
    width: 190px;
    margin: 0 auto;
    margin-top: 15px;
  }

  .appseo-testimonial-content .appseo-testimonial-trigger:after,
  .appseo-testimonial-content .appseo-testimonial-trigger:before {
    display: none;
  }

  .appseo-cta-section .appseo-cta-btn {
    width: 180px;
    height: 60px;
    line-height: 60px;
  }

  .appseo-newsletter-box .nws-button button {
    width: 120px;
  }

  .appseo-copyright .appseo-footer-social,
  .appseo-copyright .appseo-copyright-text {
    float: none;
    display: block;
    margin-bottom: 20px;
  }

  .appseo-about-section {
    padding-bottom: 70px;
  }

  .ready-to-grow-section {
    padding: 50px 0px 60px;
  }

  .appseo-mission-area-section {
    padding: 60px 0px;
  }

  .appseo-mission-area-text .appseo-section-title2 {
    padding-bottom: 40px;
  }

  .appseo-pricing-section .appseo-pricing-item {
    padding-bottom: 0;
  }

  .appseo-pricing-section .appseo-pricing-inner-box .appseo-pricing-img {
    position: static;
    margin-bottom: 20px;
  }

  .appseo-pricing-section .appseo-pricing-inner-box {
    padding-top: 50px;
  }

  .appseo-pricing-section .appseo-pricing-inner-box {
    margin-bottom: 40px;
  }

  .appseo-pricing-section .appseo-pricing-inner-box.popular-appseo {
    top: 0;
  }

  .appseo-testimonial-content .appseo-testimonial-text {
    font-size: 20px;
  }

  .appseo-testimonial-section {
    padding: 40px 0px 75px;
  }

  .appseo-testimonial-content .appseo-testimonial-quote {
    padding-bottom: 65px;
  }

  .appseo-testimonial-content .appseo-testimonial-trigger .appseo-testi-name-degi h3 {
    font-size: 24px;
  }

  .appseo-case-study-section {
    padding: 60px 0px 20px;
  }

  .appseo-cta-section {
    padding: 60px 0px 50px;
  }

  .appseo-blog-section {
    padding: 50px 0px;
  }

  .appseo-footer-area-section {
    padding: 60px 0px;
  }

  .appseo-pricing-section .appseo-pricing-title {
    padding-bottom: 40px;
  }

  .appseo-newsletter-text h2 {
    font-size: 32px;
  }

  .ready-to-grow-section .ready-go-form .ready-go-input-area input {
    height: 50px;
    margin-bottom: 20px;
  }
}
@media screen and (max-width: 380px) {
  .appseo-mission-area-text p {
    font-size: 15px;
    overflow: hidden;
  }

  .appseo-testimonial-content .appseo-testimonial-quote:before {
    display: none;
  }

  .appseo-newsletter-box input {
    padding-left: 15px;
  }

  .appseo-newsletter-box input::placeholder {
    font-size: 14px;
  }
  .appseo-banner-section .appseo-banner-content h1 {
    font-size: 36px;
  }
}
@media screen and (max-width: 360px) {
  .appseo-banner-section .appseo-banner-content h1 {
    font-size: 35px;
  }

  .appseo-service-content .appseo-service-icon-text {
    padding: 55px 45px;
  }
}
@media screen and (max-width: 320px) {
  .appseo-banner-section .appseo-banner-content h1 {
    font-size: 30px;
  }

  .appseo-banner-section .appseo-banner-content .banner-call-action .call-action-text {
    font-size: 20px;
  }

  .appseo-section-title h2 {
    font-size: 30px;
  }

  .appseo-service-content .appseo-service-icon-text {
    padding: 55px 30px;
  }

  .appseo-section-title2 h2 {
    font-size: 28px;
  }

  .appseo-about-section .appseo-about-content .appseo-about-quote .quote-text-about {
    padding-left: 50px;
  }

  .ready-to-grow-section .ready-go-title h2 {
    font-size: 28px;
  }

  .ready-to-grow-section .ready-go-title span {
    font-size: 16px;
  }

  .appseo-mission-area-text .appseo-mission-area-item .appseo-mission-item-content .appseo-mission-area-text h3 {
    font-size: 20px;
  }
}
/*---------------------------------------------------- */
/*----------------------------------------------------*/
.theme_feature_area .read_btn,
.compare_content_item .compare_btn {
  height: 45px;
  width: 145px;
  font-size: 15px;
  margin-top: 35px;
  line-height: 45px;
  font-weight: 600;
  border-radius: 25px;
  background-color: #161616;
  transition: 0.3s all ease-in-out;
  font-family: "Poppins";
  overflow: hidden;
  z-index: 1;
  position: relative;
  transition: 0.3s all ease-in-out;
}

.theme_feature_area .read_btn a,
.compare_content_item .compare_btn a {
  width: 100%;
  display: block;
  color: #fff;
}

.theme_feature_area .read_btn:before,
.compare_content_item .compare_btn:before {
  top: 0;
  left: 0;
  opacity: 0;
  content: "";
  width: 100%;
  height: 100%;
  z-index: -1;
  position: absolute;
  transition: 0.3s all ease-in-out;
  background-image: linear-gradient(-38deg, #20fdee 0%, #0478e9 100%);
}

.theme_feature_area .read_btn:hover,
.compare_content_item .compare_btn:hover {
  box-shadow: 0px 14px 18px 0px rgba(1, 105, 228, 0.25);
}

.theme_feature_area .read_btn:hover:before,
.compare_content_item .compare_btn:hover:before {
  opacity: 1;
}
@keyframes fadeFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.fadeFromRight {
  animation-name: fadeFromRight;
}

.fadeFromLeft {
  animation-name: fadeFromLeft;
}

/*global area*/
/*----------------------------------------------------*/
.saas-modern {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-size: 16px;
  line-height: 1.4;
  font-family: "Roboto";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

.saas-modern::selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.saas-modern::-moz-selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.saas-modern-preloader {
  background-color: #fff;
  background: #fff url("../img/saas-m/pre.svg") no-repeat center center;
}

.decoration-wrapper {
  overflow: hidden;
  position: relative;
}
@keyframes zooming {
  0% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.05, 1.05);
  }
  100% {
    transform: scale(1, 1);
  }
}
.zooming {
  animation: zooming 18s infinite both;
}

.saas-modern-headline h1,
.saas-modern-headline h2,
.saas-modern-headline h3,
.saas-modern-headline h4,
.saas-modern-headline h5,
.saas-modern-headline h6 {
  margin: 0;
  font-family: "Poppins";
}

.section_title {
  margin: 0 auto;
  max-width: 530px;
}

.section_title .title_tag {
  font-size: 14px;
  font-family: "Poppins";
  display: inline-block;
  border-radius: 20px;
  margin-bottom: 20px;
  line-height: 35px;
  background-color: #fff;
  padding-right: 20px;
  box-shadow: 0px 0px 27px 0px rgba(0, 0, 0, 0.15);
}

.section_title .title_tag .tag_icon {
  height: 35px;
  width: 35px;
  line-height: 35px;
  margin-right: 13px;
  border-radius: 100%;
  background-image: linear-gradient(-38deg, #20fdee 0%, #0478e9 100%);
}

.section_title .title_tag .tag_icon i {
  color: #fff;
  font-size: 18px;
}

.section_title .section_title_text h2 {
  font-size: 36px;
  font-weight: 700;
  line-height: 1.306;
  padding-bottom: 15px;
}

.section_title .section_title_text h2 span {
  font-weight: 400;
}

.section_title .section_title_text p {
  font-size: 18px;
  line-height: 1.667;
}
@keyframes line_animation {
  0% {
    top: 0px;
    opacity: 1;
  }
  50% {
    top: 50%;
  }
  100% {
    top: 100%;
    opacity: 1;
  }
}
@keyframes scroll2 {
  0% {
    opacity: 1;
    bottom: 0px;
  }
  50% {
    bottom: 50%;
  }
  100% {
    bottom: 100%;
    opacity: 1;
  }
}
@keyframes slide {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 1920px 0;
  }
}
.line_animation {
  top: 0px;
  left: 50%;
  width: 80%;
  bottom: 0px;
  z-index: -1;
  display: block;
  position: absolute;
  transform: translateX(-50%);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.line_animation:before {
  width: 10px;
  left: -2px;
  content: "";
  height: 10px;
  border-radius: 100%;
  position: absolute;
  animation: line_animation 15s ease-out infinite;
  background-color: #59adfe;
}

.line_animation .line_area {
  width: 20%;
  float: left;
  height: 100%;
  position: relative;
  display: inline-block;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.line_animation .line_area:before {
  width: 10px;
  right: -2px;
  content: "";
  height: 10px;
  border-radius: 100%;
  position: absolute;
  background-color: #59adfe;
}

.line_animation .line_area:nth-child(even):before {
  animation: line_animation 15s ease-out infinite;
}

.line_animation .line_area:nth-child(odd):before {
  animation: scroll2 15s ease-out infinite;
}

.saas-modern-scrollup {
  width: 55px;
  right: 30px;
  z-index: 5;
  height: 55px;
  bottom: 100px;
  display: none;
  position: fixed;
  border-radius: 100%;
  line-height: 55px;
  background-image: linear-gradient(-38deg, #20fdee 0%, #0478e9 100%);
}

.saas-modern-scrollup:after {
  position: absolute;
  content: "";
  width: 0px;
  height: 0px;
  left: 20px;
  top: 15px;
  right: 0px;
  box-shadow: 0 0 15px 10px rgba(255, 255, 255, 0.75);
  border-radius: 50%;
}

.saas-modern-scrollup i {
  color: #fff;
  font-size: 20px;
}

/*---------------------------------------------------- */
/*Saas-Modern header area*/
/*----------------------------------------------------*/
.main_header {
  z-index: 1;
  padding-top: 80px;
  width: 100%;
  position: absolute;
}

.main-navigation {
  display: inline-block;
}

.main-navigation .navbar-nav {
  display: inherit;
}

.main-navigation .navbar-nav li {
  position: relative;
}

.main-navigation .navbar-nav li a {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  padding: 35px 10px;
  display: inline;
  font-family: "Poppins";
  position: relative;
}
.main-navigation .navbar-nav li a.active {
  color: #30f8ef;
}
.main-navigation .navbar-nav li a:after {
  left: 0;
  width: 0%;
  height: 2px;
  content: "";
  bottom: 25px;
  position: absolute;
  background-color: #31f9ef;
  transition: 0.3s all ease-in-out;
}

.main-navigation .navbar-nav li a:hover:after {
  width: 100%;
}

.sign_up_btn {
  height: 38px;
  width: 90px;
  color: #000000;
  font-weight: 600;
  line-height: 38px;
  margin-left: 38px;
  border-radius: 25px;
  font-family: "Poppins";
  display: inline-block;
  background-color: #30f8ef;
  transition: 400ms all ease;
  box-shadow: 0px 14px 18px 0px rgba(1, 105, 228, 0.25);
}
.sign_up_btn:hover {
  color: #000;
  background-color: #fff;
}
.menu-bg-overlay {
  background-color: #000;
  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  position: fixed;
  top: -90px;
  padding: 25px 0px;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
}

.main_header.menu-bg-overlay {
  z-index: 9;
  top: 0px;
  box-shadow: 0 0 20px -10px rgba(0, 0, 0, 0.8);
}

.main_header.menu-bg-overlay .main-navigation .navbar-nav li a:after {
  display: none;
}

.main-navigation .dropdown {
  position: relative;
}

.main-navigation .dropdown > .dropdown-menu {
  top: 65px;
  left: 0;
  opacity: 0;
  z-index: 2;
  margin: 0px;
  padding: 0px;
  height: auto;
  width: 150px;
  display: block;
  padding: 10px 15px 10px;
  visibility: hidden;
  position: absolute;
  border-radius: 0;
  background-color: #fff;
  transition: all 0.2s ease-in-out;
  border: none;
  box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
}

.main-navigation .dropdown > .dropdown-menu li {
  padding-bottom: 5px;
}

.main-navigation .dropdown > .dropdown-menu li a {
  color: #000;
  font-size: 14px;
}

.main-navigation .dropdown > .dropdown-menu li a:after {
  display: none;
}

.main-navigation .dropdown:hover .dropdown-menu {
  top: 55px;
  opacity: 1;
  visibility: visible;
}

.mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 280px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 40px 0px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s cubic-bezier(0.9, 0.03, 0, 0.96) 0.6s;
}

.mobile_menu_content .main-navigation {
  width: 100%;
}

.mobile_menu_content .main-navigation .navbar-nav {
  width: 100%;
}

.mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}

.mobile_menu_content .main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  padding: 5px 30px;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid #dcdcdc;
}

.mobile_menu_content .main-navigation .navbar-nav li:first-child {
  border-top: 1px solid #dcdcdc;
}

.mobile_menu_content .main-navigation .navbar-nav li a {
  color: #000;
  padding: 0;
  width: 100%;
  display: block;
  font-size: 14px;
  font-weight: 400;
}

.mobile_menu_content .m-brand-logo {
  margin-bottom: 30px;
}

.mobile_menu_wrap.mobile_menu_on .mobile_menu_content {
  right: -15px;
  transition: all 0.7s cubic-bezier(0.9, 0.03, 0, 0.96) 0.4s;
}

.mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: -100%;
  height: 120vh;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.8s ease-in 0.8s;
}

.mobile_menu_overlay_on {
  overflow: hidden;
}

.mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  right: 0;
  transition: all 0.8s ease-out 0s;
}

.mobile_menu_button {
  position: absolute;
  display: none;
  right: 0;
  cursor: pointer;
  line-height: 40px;
  color: #fff;
  text-align: center;
  font-size: 25px;
  top: -38px;
}

.mobile_menu .main-navigation .navbar-nav li a:after {
  display: none;
}

.mobile_menu .main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}

.mobile_menu .mobile_menu_content .main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 0;
}

.mobile_menu .mobile_menu_content .main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 3px 20px;
  line-height: 1;
}

.mobile_menu .mobile_menu_content .main-navigation .navbar-nav .dropdown-menu li a {
  color: #000;
}

.mobile_menu .dropdown {
  position: relative;
}

.mobile_menu .dropdown:before {
  top: 7px;
  right: 30px;
  font-size: 12px;
  font-weight: 700;
  content: "";
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.mobile_menu .mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

/*---------------------------------------------------- */
/*Saas-Modern  banner area*/
/*----------------------------------------------------*/
.banner_section {
  overflow: visible;
  margin-bottom: 90px;
  z-index: 0;
  padding: 215px 0 325px 0;
}

.banner_text {
  margin: 0 auto;
  max-width: 550px;
}

.banner_text h1 {
  color: #fff;
  font-size: 50px;
  line-height: 1.2;
  font-weight: 700;
  padding-bottom: 20px;
}

.banner_text p {
  color: #fff;
  margin: 0 auto;
  font-size: 20px;
  max-width: 500px;
  line-height: 1.35;
  padding-bottom: 40px;
}

.banner_text .download_btn {
  color: #fff;
  width: 175px;
  height: 55px;
  font-size: 15px;
  margin: 0 auto;
  font-weight: 700;
  line-height: 55px;
  border-radius: 30px;
  background-color: #000;
  font-family: "Poppins";
  transition: 400ms all ease;
}

.banner_text .download_btn a {
  display: block;
  width: 100%;
}
.banner_text .download_btn:hover {
  background-color: #fff;
  color: #000;
}

.banner_screen {
  left: 0;
  right: 0;
  bottom: -155px;
  margin: 0 auto;
  max-width: 765px;
  position: absolute;
}

.banner_screen .screen_img .middle_screen {
  box-shadow: 0px 0px 27px 0px rgba(0, 0, 0, 0.14);
}

.banner_screen .screen_img .side_screen1 {
  top: -30px;
  left: -102px;
  position: absolute;
  box-shadow: 0px 0px 27px 0px rgba(0, 0, 0, 0.14);
}

.banner_screen .screen_img .side_screen2 {
  top: 65px;
  right: -150px;
  position: absolute;
  box-shadow: 0px 0px 27px 0px rgba(0, 0, 0, 0.14);
}

.banner_shape1,
.banner_shape2,
.banner_shape3 {
  position: absolute;
}

.banner_shape1 {
  top: 80px;
  left: 30%;
}

.banner_shape2 {
  top: 220px;
  left: 12%;
}

.banner_shape3 {
  top: 40%;
  right: 15%;
}

/*---------------------------------------------------- */
/*Saas-Modern Feature area*/
/*----------------------------------------------------*/
.featured_service_section {
  padding: 120px 0px 0px;
}

.featured_service_section .section_title {
  padding-bottom: 55px;
}

.featured_service_section .section_title h2 {
  margin: 0 auto;
  max-width: 400px;
}

.featured_content li {
  float: left;
  list-style: none;
  padding-bottom: 40px;
}

.featured_content .featured_icon_text {
  z-index: 1;
  width: 285px;
  height: 425px;
  padding: 0 10px;
  margin: 0px auto;
  overflow: hidden;
  padding-top: 60px;
  border-radius: 15px;
  transition: 0.3s all ease-in-out;
}

.featured_content .featured_icon_text:before {
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  content: "";
  height: 100%;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s all ease-in-out;
  background-image: linear-gradient(-38deg, #0478e9 0%, #20fdee 100%);
}

.featured_content .featured_icon_text .featured_icon {
  width: 110px;
  height: 110px;
  margin: 0 auto;
  line-height: 110px;
  margin-bottom: 25px;
  border-radius: 100%;
  box-shadow: 0px 14px 18px 0px rgba(1, 105, 228, 0.25);
  background-image: linear-gradient(-38deg, #20fdee 0%, #0478e9 100%);
}

.featured_content .featured_icon_text .featured_icon i {
  color: #fff;
  font-size: 50px;
}

.featured_content .featured_icon_text .featured_icon:before {
  position: absolute;
  content: "";
  width: 0px;
  height: 0px;
  left: 40px;
  top: 25px;
  right: 0px;
  box-shadow: 0 0 25px 25px rgba(255, 255, 255, 0.75);
  border-radius: 50%;
}

.featured_content .featured_icon_text .featured_text {
  padding: 0 20px;
  margin-bottom: 30px;
}

.featured_content .featured_icon_text .featured_text h3 {
  color: #161616;
  font-size: 20px;
  font-weight: 600;
  padding-bottom: 20px;
  transition: 0.3s all ease-in-out;
}

.featured_content .featured_icon_text .featured_text p {
  line-height: 1.625;
  transition: 0.3s all ease-in-out;
}

.featured_content .featured_icon_text .feature_btn {
  opacity: 0;
  visibility: hidden;
  margin-right: 25px;
  transition: 0.3s all ease-in-out;
}

.featured_content .featured_icon_text .feature_btn a {
  color: #30f7ee;
  font-size: 15px;
  font-weight: 700;
  font-family: "Poppins";
  position: relative;
}

.featured_content .featured_icon_text .feature_btn a:after {
  font-weight: 900;
  content: "";
  position: absolute;
  right: -20px;
  font-family: "Font Awesome 5 Free";
}

.featured_content .featured_icon_text:hover {
  box-shadow: 0px 14px 18px 0px rgba(1, 105, 228, 0.25);
}

.featured_content .featured_icon_text:hover:before {
  opacity: 1;
  visibility: visible;
}

.featured_content .featured_icon_text:hover .featured_text p,
.featured_content .featured_icon_text:hover .featured_text h3 {
  color: #fff;
}

.featured_content .featured_icon_text:hover .feature_btn {
  opacity: 1;
  visibility: visible;
}

#featured_scroll {
  padding-bottom: 40px;
}

#featured_scroll .mCSB_scrollTools.mCSB_scrollTools_horizontal {
  margin: 0 auto;
  height: 30px;
  max-width: 570px;
}

#featured_scroll .mCSB_scrollTools .mCSB_draggerRail {
  height: 20px;
  background-color: #ececec;
}

#featured_scroll .mCSB_scrollTools {
  opacity: 1;
}

#featured_scroll .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  height: 20px;
  box-shadow: 0px 6px 18px -5px rgba(2, 8, 38, 0.35);
  background-color: #22b0e7;
}

#featured_scroll .mCustomScrollbar,
#featured_scroll .mCustomScrollbar.mCS_touch_action,
#featured_scroll .mCustomScrollbar.mCS_touch_action .mCS-aviva.mCSB_scrollTools {
  touch-action: auto;
}

#featured_scroll .mCustomScrollBox {
  touch-action: pan-x pinch-zoom;
}

#featured_scroll .mCustomScrollBox.mCSB_vertical_horizontal {
  touch-action: pinch-zoom;
}

#featured_scroll .mCustomScrollBox.mCSB_horizontal {
  touch-action: pan-y pinch-zoom;
}

.theme_feature_section {
  padding-top: 55px;
}

.theme_feature_area {
  padding-bottom: 60px;
}

.theme_feature_area .theme_feature_content {
  padding: 58px 35px;
}

.theme_feature_area .theme_feature_content h3 {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  padding-bottom: 30px;
}

.theme_feature_area .theme_feature_content p {
  color: #fff;
  font-size: 18px;
  line-height: 1.444;
  padding-bottom: 25px;
}

.theme_feature_area .theme_feature_content .theme_feature_list li {
  width: 100%;
  color: #fff;
  font-size: 17px;
  margin-bottom: 22px;
}

.theme_feature_area .theme_feature_content .theme_feature_list li i {
  float: left;
  color: #18ff00;
  font-size: 16px;
  margin-right: 8px;
}

.theme_feature_area .theme_feature_content .theme_feature_list li span {
  overflow: hidden;
}

.theme_feature_area .features_one {
  margin-left: 100px;
  background-color: #138afd;
}

.theme_feature_area .features_two {
  margin-right: 100px;
  background-color: #000000;
}

.theme_feature_area .features_two .theme_feature_list li i {
  color: #20fcee;
}

.theme_feature_area .features_two .read_btn {
  background-color: #20fcee;
}

.theme_feature_area .features_two .read_btn a {
  color: #000000;
  font-weight: 600;
  transition: 0.3s all ease-in-out;
}

.theme_feature_area .features_two .read_btn:hover a {
  color: #fff;
}

/*---------------------------------------------------- */
/*Saas-Modercompare area*/
/*----------------------------------------------------*/
.compare_section {
  padding-bottom: 15px;
  overflow: visible;
}

.compare_section .compare_title h2 {
  color: #161616;
  font-size: 30px;
  font-weight: 700;
  line-height: 1.324;
  padding-bottom: 18px;
}

.compare_section .compare_title p {
  color: #383838;
  font-size: 18px;
  padding-bottom: 28px;
}

.compare_content_item {
  padding-top: 30px;
}

.compare_content_item .compare_list {
  padding-left: 30px;
}

.compare_content_item .compare_list li {
  margin-bottom: 20px;
}

.compare_content_item .compare_list li i {
  float: left;
  color: #27c317;
  margin-right: 12px;
}

.compare_content_item .compare_list li span {
  overflow: hidden;
}

.compare_content_item .compare_content {
  padding-top: 30px;
}

.compare_img {
  z-index: 4;
  max-width: 525px;
}

.compare_img .compare_shape1,
.compare_img .compare_shape2 {
  z-index: -1;
  position: absolute;
}

.compare_img .compare_shape1 {
  top: 35px;
  left: 0;
}

.compare_img .compare_shape2 {
  top: 15px;
  right: 0;
}

.compare_section_two .compare_shape1 {
  top: 20px;
  left: -65px;
}

.compare_section_two .compare_shape2 {
  top: 0;
  right: 65px;
}

.compare_section_two .compare_content {
  padding-left: 30px;
}

.compare_section_two .compare_content .compare_title {
  padding-bottom: 15px;
}

.compare_section_two .compare_content_item .compare_list {
  padding-left: 0;
}

/*---------------------------------------------------- */
/*user area*/
/*----------------------------------------------------*/
.trusted_user_section {
  padding: 80px 0px 140px;
}

.trusted_content_img {
  padding-left: 45px;
}

.trusted_content_img li {
  width: 95px;
  height: 95px;
  line-height: 102px;
  border-radius: 100%;
  margin: 0 20px 20px 0px;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 4px 15px 0px rgba(23, 23, 23, 0.13);
}

.trusted_content_img li:nth-child(1) {
  transform: scale(1.1);
}

.trusted_content_img li:nth-child(2) {
  transform: translateY(30px);
}

.trusted_content_img li:nth-child(2):hover {
  transform: translateY(30px) scale(1.1);
}

.trusted_content_img li:nth-child(3) {
  transform: translateY(60px);
}

.trusted_content_img li:nth-child(3):hover {
  transform: translateY(60px) scale(1.1);
}

.trusted_content_img li:nth-child(4) {
  transform: translateY(-35px);
}

.trusted_content_img li:nth-child(4):hover {
  transform: translateY(-35px) scale(1.1);
}

.trusted_content_img li:nth-child(5) {
  transform: translateY(0px);
}

.trusted_content_img li:nth-child(5):hover {
  transform: translateY(0px) scale(1.1);
}

.trusted_content_img li:nth-child(6) {
  transform: translateY(25px);
}

.trusted_content_img li:nth-child(6):hover {
  transform: translateY(25px) scale(1.1);
}

.trusted_content_img li:nth-child(7) {
  transform: translateY(55px);
}

.trusted_content_img li:nth-child(7):hover {
  transform: translateY(55px) scale(1.1);
}

.trusted_content_img li:nth-child(8) {
  transform: translateY(-35px);
}

.trusted_content_img li:nth-child(8):hover {
  transform: translateY(-35px) scale(1.1);
}

.trusted_content_img li:nth-child(9) {
  transform: translateY(0px);
}

.trusted_content_img li:nth-child(9):hover {
  transform: translateY(0px) scale(1.1);
}

.trusted_content_img li:nth-child(10) {
  transform: translateY(20px);
}

.trusted_content_img li:nth-child(10):hover {
  transform: translateY(20px) scale(1.1);
}

.trusted_content_img li:nth-child(11) {
  transform: translateY(50px);
}

.trusted_content_img li:nth-child(11):hover {
  transform: translateY(50px) scale(1.1);
}

.trusted_content_img li:nth-child(12) {
  transform: translateY(-35px);
}

.trusted_content_img li:nth-child(12):hover {
  transform: translateY(-35px) scale(1.1);
}

.trusted_content_img li:hover {
  box-shadow: 0px 4px 35px 0px rgba(23, 23, 23, 0.13);
}

.user_content {
  padding-top: 10px;
}

.user_content .section_title {
  margin-left: 0;
}

.user_content .user_text {
  color: #383838;
  font-size: 18px;
  padding-top: 25px;
}

/*---------------------------------------------------- */
/*partner area*/
/*----------------------------------------------------*/
.partner_section {
  z-index: 1;
  padding: 105px 0px;
  background-color: #edf1f5;
}
.partner_member {
  padding-top: 40px;
}

.partner_section .section_title {
  margin: 0 auto;
  max-width: 550px; 
  padding: 0px 0px 25px 85px;
}

.partner_section .mem_img_text .mem_pic {
  width: 230px;
  height: 230px;
  margin: 0 auto;
  overflow: hidden;
  border-radius: 100%;
}

.partner_section .mem_img_text .mem_pic:before {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.3);
}

.partner_section .mem_img_text .mem_pic:after {
  content: "";
  height: 100%;
  width: 100%;
  bottom: -95px;
  opacity: 0;
  visibility: hidden;
  left: -18px;
  transition: 0.3s all ease-in-out;
  background-image: url(../img/saas-m/partner/pbg.png);
  background-repeat: no-repeat;
  position: absolute;
}

.partner_section .mem_img_text .member_social {
  bottom: 20px;
  z-index: 3;
  left: 0;
  right: 0;
  opacity: 0;
  visibility: hidden;
  text-align: center;
  position: absolute;
  transition: 0.3s all ease-in-out;
}

.partner_section .mem_img_text .member_social li {
  color: #fff;
  font-size: 18px;
  margin: 0px 7px;
}

.partner_section .mem_img_text .mem_img_line {
  z-index: 1;
}

.partner_section .mem_img_text .mem_img_line .line_shape1,
.partner_section .mem_img_text .mem_img_line .line_shape2 {
  height: 325px;
  position: absolute;
  width: 5px;
  top: -40px;
  left: 120px;
  z-index: -1;
  transform: rotate(50deg);
  opacity: 0;
  visibility: hidden;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 14px 18px 0px rgba(1, 105, 228, 0.25);
  background-image: linear-gradient(116deg, #0478e9 0%, #20fdee 100%);
}

.partner_section .mem_img_text .mem_img_line .line_shape2 {
  top: -35px;
  height: 285px;
  left: 140px;
}

.partner_section .mem_img_text .mem_name_designation {
  margin-top: 45px;
}

.partner_section .mem_img_text .mem_name_designation h4 {
  color: #0e0e0e;
  font-size: 20px;
  font-weight: 700;
  padding-bottom: 10px;
}

.partner_section .mem_img_text .mem_name_designation p {
  font-size: 15px;
  color: #383838;
}

.partner_section .mem_img_text:hover .member_social {
  opacity: 1;
  bottom: 35px;
  visibility: visible;
}

.partner_section .mem_img_text:hover .mem_pic:after {
  opacity: 1;
  visibility: visible;
}

.partner_section .mem_img_text:hover .line_shape1,
.partner_section .mem_img_text:hover .line_shape2 {
  height: 285px;
  opacity: 1;
  visibility: visible;
}

.partner_section .mem_img_text:hover .line_shape2 {
  height: 325px;
}

.partner_text {
  font-size: 18px;
  max-width: 480px;
  padding-top: 20px;
  padding-left: 35px;
  line-height: 1.667;
}

/*---------------------------------------------------- */
/*testimonial area*/
/*----------------------------------------------------*/
.testimonial_section {
  padding: 105px 0px 70px 0px;
}

.testimonial_section .section_title {
  padding-bottom: 50px;
}

.testimonial_content {
  padding: 20px;
  display: inline-block;
}

.testimonial_content .testimonial_rating {
  margin-top: 10px;
}

.testimonial_content .testimonial_rating li {
  font-size: 14px;
  color: #f6b91c;
}

.testimonial_content .testimonial_text {
  z-index: 1;
  padding: 50px 40px;
  border-radius: 10px;
  border-bottom-left-radius: 0px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0px 7px 24px 0px rgba(2, 55, 156, 0.2);
}

.testimonial_content .testimonial_text p {
  line-height: 1.875;
}

.testimonial_content .testimonial_text:before {
  content: "";
  height: 100%;
  width: 40%;
  position: absolute;
  right: -45px;
  z-index: -1;
  bottom: -95px;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s all ease-in-out;
  background-image: url(../img/saas-m/testimonial/tshape1.png);
  background-repeat: no-repeat;
}

.testimonial_content .testimonial_text:hover:before {
  bottom: -85px;
  opacity: 1;
  visibility: visible;
}

.qoute_mark:after {
  content: "";
  position: absolute;
  left: 0px;
  bottom: -45px;
  z-index: 1;
  border-left: 0px solid transparent;
  border-right: 50px solid transparent;
  border-top: 45px solid #fff;
  filter: drop-shadow(0px 20px 13px rgba(2, 55, 156, 0.2));
}

.testimonial_name_designation {
  margin-top: 30px;
  padding-left: 30px;
}

.testimonial_name_designation .testimonial_img {
  height: 65px;
  width: 65px;
  border-radius: 100%;
  margin-right: 18px;
  overflow: hidden;
  border: 5px solid #fff;
  box-shadow: 0px 7px 6px 0px rgba(0, 15, 44, 0.19), inset -1px 0px 27px 0px rgba(0, 0, 0, 0.29);
}

.testimonial_name_designation .testimonial_meta {
  margin-top: 10px;
  display: inline-block;
}

.testimonial_name_designation .testimonial_meta h4 {
  font-size: 22px;
  font-weight: 700;
  color: #161616;
}

.testimonial_name_designation .testimonial_meta p {
  font-size: 14px;
  color: #444444;
}

.testimonial_slider .carousel-control-prev,
.testimonial_slider .carousel-control-next {
  top: auto;
  left: 190px;
  bottom: 40px;
  font-size: 18px;
  color: #373a5b;
  width: 50px;
  height: 50px;
  opacity: 1;
  line-height: 58px;
  text-align: center;
  border-radius: 50px;
  font-weight: 700;
  display: inline-block;
  margin: 0px 15px 0px 0px;
  background-color: #fff;
  transition: all 300ms ease;
  box-shadow: 0px 7px 7px 0px rgba(0, 15, 44, 0.18);
}

.testimonial_slider .carousel-control-prev:before,
.testimonial_slider .carousel-control-next:before {
  position: absolute;
  content: "";
  left: 0px;
  top: 0px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50px;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.testimonial_slider .carousel-control-prev:hover,
.testimonial_slider .carousel-control-next:hover {
  background-color: #000;
  color: #fff;
}

.testimonial_slider .carousel-control-prev {
  left: 120px;
}

.testimonial_slider .carousel-control-prev:before {
  content: "";
}

.carousel-indicators {
  position: static;
}

.testimonial_indicator {
  margin: 20px 20px 0 0;
  display: block;
  height: 510px;
  max-width: 100%;
  position: relative;
  background-repeat: no-repeat;
  background-image: url(../img/saas-m/testimonial/in-bg.png);
}

.testimonial_indicator .carousel-indicators li {
  position: absolute;
  width: 60px;
  height: 60px;
  text-align: center;
  color: #202120;
  border-radius: 50%;
  border: 5px solid #fff;
  overflow: hidden;
  transition: all 500ms ease;
  box-shadow: 0px 7px 6px 0px rgba(0, 15, 44, 0.19), inset -1px 0px 27px 0px rgba(0, 0, 0, 0.29);
}

.testimonial_indicator .carousel-indicators li img {
  display: block;
}

.testimonial_indicator .carousel-indicators li:nth-child(1) {
  bottom: 105px;
  right: 100px;
}

.testimonial_indicator .carousel-indicators li:nth-child(2) {
  left: 90px;
  bottom: 45px;
}

.testimonial_indicator .carousel-indicators li:nth-child(3) {
  top: 40%;
  left: 0px;
}

.testimonial_indicator .carousel-indicators li:nth-child(4) {
  top: 90px;
  left: 100px;
}

.testimonial_indicator .carousel-indicators li:nth-child(5) {
  top: 35%;
  left: 0;
  right: 0;
  margin: 0 auto;
}

.testimonial_indicator .carousel-indicators li:nth-child(6) {
  right: 120px;
  top: -30px;
}

.testimonial_indicator .active {
  transform: scale(1.5);
}

/*---------------------------------------------------- */
/*testimonial area*/
/*----------------------------------------------------*/
.newslatter_content {
  left: 0;
  left: 0;
  right: 0;
  top: -165px;
  width: 100%;
  margin: 0 auto;
  position: absolute;
  max-width: 1170px;
  border-radius: 20px;
  padding: 65px 0px 70px;
  overflow: hidden;
  z-index: 2;
  box-shadow: inset 0px 2px 8px 0px rgba(0, 0, 0, 0.29);
  background-image: linear-gradient(-115deg, #0478e9 0%, #20fdee 100%);
}

.newslatter_content:after {
  content: "";
  position: absolute;
  height: 300px;
  width: 300px;
  border-radius: 100%;
  top: -40px;
  right: -160px;
  border: 30px solid #3f91e0;
}

.newslatter_content .newslatter_title {
  margin: 0 auto;
  max-width: 380px;
  padding-bottom: 45px;
}

.newslatter_content .newslatter_title h2 {
  color: #fff;
  font-size: 36px;
  font-weight: 700;
  padding-bottom: 20px;
}

.newslatter_content .newslatter_title p {
  color: #fff;
  line-height: 1.75;
}

.newslatter_content .newslatter-form {
  margin: 0 auto;
  max-width: 650px;
  padding-left: 30px;
}

.newslatter_content .newslatter-form input {
  width: 100%;
  height: 65px;
  border: none;
  padding: 0px 25px;
  border-radius: 5px;
  box-shadow: 0px 6px 40px 0px rgba(5, 41, 140, 0.25);
}

.newslatter_content .newslatter-form .nws-button {
  position: absolute;
  top: 8px;
  right: 8px;
}

.newslatter_content .newslatter-form .nws-button button {
  height: 50px;
  width: 130px;
  border: none;
  color: #fff;
  font-size: 15px;
  font-weight: 700;
  border-radius: 5px;
  font-family: "Poppins";
  background-image: linear-gradient(-38deg, #0478e9 0%, #20fdee 100%);
}

.newslatter_content .newslatter-form .nws-button button:hover {
  background-image: linear-gradient(-38deg, #20fdee 0%, #0478e9 100%);
}

.newslatter_content .img_bg {
  top: 15px;
  position: absolute;
  left: 15px;
}

.newslatter_content .subs_icon {
  position: absolute;
  left: 0px;
  top: 50px;
  right: 0px;
  max-width: 1920px;
  width: 100%;
  height: 100%;
  z-index: -1;
  margin: 0 auto;
}

.newslatter_content .subs_icon .subs_iconitem-1 {
  width: 100%;
  height: 46px;
  background-repeat: no-repeat;
  left: 0px;
  top: 150px;
  animation: slide 60s linear infinite;
  -webkit-animation: slide 60s linear infinite;
}

.newslatter_content .subs_icon .subs_iconitem-2 {
  width: 100%;
  height: 46px;
  background-repeat: no-repeat;
  left: 0px;
  top: 70px;
  animation: slide 50s linear infinite;
  -webkit-animation: slide 50s linear infinite;
}

.newslatter_content .subs_icon .subs_iconitem-3 {
  width: 100%;
  height: 46px;
  background-repeat: no-repeat;
  left: 0px;
  top: 310px;
  animation: slide 90s linear infinite;
  -webkit-animation: slide 90s linear infinite;
}

/*---------------------------------------------------- */
/*Footer area*/
/*----------------------------------------------------*/
.footer_section {
  background-color: #edf2f6;
  padding-top: 260px;
  margin-top: 165px;
}

.footer_section .footer_widget_content {
  padding-bottom: 75px;
  border-bottom: 2px solid #d2d1d2;
}

.footer_widget .footer_logo {
  margin-bottom: 30px;
}

.footer_widget .footer_text {
  font-size: 14px;
  line-height: 1.714;
  padding-bottom: 35px;
}

.footer_widget .footer_social li {
  color: #8c8c8c;
  font-size: 17px;
  margin-right: 18px;
  transition: 0.3s all ease-in-out;
}

.footer_widget .footer_social li:hover {
  transform: translateY(-4px);
}

.footer_widget .footer_social li:hover i {
  transition: 0.3s all ease-in-out;
  background-image: linear-gradient(-38deg, #0478e9 0%, #20fdee 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.footer_widget .quick_link {
  max-width: 205px;
}

.footer_widget .quick_link li {
  font-size: 14px;
  color: #383838;
  margin-right: 10px;
  margin-bottom: 10px;
  position: relative;
}

.footer_widget .quick_link li:before {
  height: 1px;
  content: "";
  position: absolute;
  width: 0%;
  right: 0;
  left: auto;
  bottom: 0;
  transition: 0.3s all ease-in-out;
  background-image: linear-gradient(116deg, #20fdee 0%, #0478e9 100%);
}

.footer_widget .quick_link li:hover:before {
  width: 100%;
  right: auto;
  left: 0;
}

.widget_title {
  color: #1a0a38;
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 48px;
  font-family: "Poppins";
}

.widget_title:after {
  left: 0;
  width: 35px;
  content: "";
  height: 3px;
  bottom: -9px;
  position: absolute;
  background-image: linear-gradient(116deg, #20fdee 0%, #0478e9 100%);
}

.footer_address li {
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.875;
  font-family: "Poppins";
}

.footer_address span {
  color: #180b36;
  font-weight: 600;
  margin-right: 5px;
}

.insta_feed li {
  float: left;
  width: 75px;
  height: 75px;
  margin-right: 10px;
  margin-bottom: 7px;
  position: relative;
  background-color: #000;
  transition: 0.3s all ease-in-out;
}

.insta_feed li:before {
  position: absolute;
  top: 0;
  content: "";
  height: 100%;
  width: 100%;
  opacity: 0;
  background-color: #000;
  transition: 0.3s all ease-in-out;
}

.insta_feed li i {
  left: 0;
  right: 0;
  top: 50%;
  position: absolute;
  text-align: center;
  color: #fff;
  transform: translateY(-50%);
  opacity: 0;
  transition: 0.3s all ease-in-out;
}

.insta_feed li:hover:before {
  opacity: 0.7;
}

.insta_feed li:hover i {
  opacity: 1;
}

.copyright_text {
  padding: 30px 0px;
}

.copyright_text p,
.copyright_text span {
  color: #666666;
  font-size: 14px;
  font-family: "Poppins";
}

.copyright_text span {
  float: right;
}

/*---------------------------------------------------- */
/*responsive area*/
/*----------------------------------------------------*/
@media screen and (max-width: 1140px) {
  .compare_section {
    overflow: hidden;
    padding-bottom: 30px;
  }

  .theme_feature_area .theme_feature_content h3 {
    font-size: 22px;
  }
}
@media screen and (max-width: 1024px) { 
  .banner_screen .screen_img .side_screen2 {
    right: -40px;
  }
  .trusted_content_img li:nth-child(2) ,
  .trusted_content_img li:nth-child(2):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(3),
  .trusted_content_img li:nth-child(3):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(4),
  .trusted_content_img li:nth-child(4):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(5),
  .trusted_content_img li:nth-child(5):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(6),
  .trusted_content_img li:nth-child(6):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(7),
  .trusted_content_img li:nth-child(7):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(8),
  .trusted_content_img li:nth-child(8):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(9),
  .trusted_content_img li:nth-child(9):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(10),
  .trusted_content_img li:nth-child(10):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(11),
  .trusted_content_img li:nth-child(11):hover {
    transform: translateY(0px);
  }

  .trusted_content_img li:nth-child(12),
  .trusted_content_img li:nth-child(12):hover {
    transform: translateY(0px);
  }
  .newslatter_content .img_bg {
    left: -60px;
  }
}
@media screen and (max-width: 991px) {
  .banner_shape3,
  .banner_shape2 {
    width: 100px;
  }


  .main_header {
    padding-top: 50px;
  }

  .main_header.menu-bg-overlay {
    padding-top: 30px;
  }

  .theme_feature_area .features_one {
    margin-left: 0;
  }

  .theme_feature_area .features_two {
    margin-right: 0;
  }

  .trusted_content_img {
    max-width: 500px;
    padding-left: 0;
  }

  .user_content {
    padding-top: 60px;
  }

  .banner_screen {
    max-width: 510px;
  }

  .main_menu_list .main-navigation {
    display: none;
  }

  .mobile_menu_button {
    display: block;
  }

  .sign_up_btn {
    top: -5px;
    right: 80px;
    position: absolute;
  }

  .banner_shape1,
  .banner_shape2,
  .banner_shape3 {
    z-index: -1;
  }

  .main_header {
    padding-top: 35px;
  }

  .compare_img {
    margin: 0 auto;
  }

  .partner_section .section_title {
    padding: 0;
  }

  .partner_text {
    padding-left: 0;
    margin-bottom: 50px;
  }

  .testimonial_indicator {
    margin: 0 auto;
    margin-top: 20px;
    max-width: 550px;
  }

  .testimonial_slider .carousel-control-prev,
  .testimonial_slider .carousel-control-next {
    bottom: -50px;
  }

  .testimonial_section {
    padding-bottom: 110px;
  }

  .mem_img_text {
    max-width: 270px;
    margin: 0 auto;
    margin-bottom: 30px;
  }

  .newslatter_content .img_bg {
    display: none;
  }

  .newslatter_content:after {
    display: none;
  }

  .widget_title {
    margin-bottom: 30px;
  }

  .footer_widget .footer_logo {
    margin-bottom: 18px;
  }

  .footer_widget .footer_text {
    padding-bottom: 20px;
  }

  .footer_widget {
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 767px) {
  .sign_up_btn {
    top: -38px;
  }

  .banner_screen {
    max-width: 450px;
  }

  .banner_screen .screen_img .side_screen2 {
    width: 200px;
  }

  .banner_screen .screen_img .side_screen1 {
    width: 150px;
  }

  .banner_text h1 {
    font-size: 36px;
  }

  .banner_section {
    margin-bottom: 200px;
    padding: 185px 0 150px 0;
  }

  .banner_shape2 {
    top: 180px;
    left: 3%;
    width: 70px;
  }

  .banner_shape3 {
    top: 45%;
    right: 8%;
    width: 70px;
  }

  .banner_shape1 {
    width: 40px;
  }

  .main_header.menu-bg-overlay {
    padding: 20px 0px 15px 0px;
  }

  .banner_section {
    margin-bottom: 135px;
  }

  .theme_feature_area .features_one {
    margin-bottom: 30px;
  }

  .trusted_user_section {
    padding: 70px 0px 60px;
  }

  .partner_section {
    padding: 70px 0px 40px;
  }

  .copyright_text span {
    float: none;
  }

  .copyright_text {
    text-align: center;
  }
}
@media screen and (max-width: 580px) {
  .banner_screen {
    max-width: 385px;
  }

  .banner_screen .screen_img .side_screen1 {
    width: 125px;
    left: -75px;
  }

}
@media screen and (max-width: 480px) {
  .banner_screen {
    display: none;
  }

  .banner_section {
    margin-bottom: 0;
    padding: 150px 0 130px 0;
  }

  .banner_shape1,
  .banner_shape2,
  .banner_shape3 {
    z-index: -1;
    opacity: 0.5;
  }

  .banner_text {
    position: relative;
    z-index: 2;
  }

  .section_title .section_title_text h2 {
    font-size: 30px;
  }

  .section_title .section_title_text p {
    font-size: 16px;
  }

  .compare_img .compare_shape1,
  .compare_img .compare_shape2 {
    max-width: 70%;
  }


  .testimonial_indicator {
    background-size: 395px 465px;
  }

  .newslatter_content .newslatter-form {
    padding-left: 0;
    margin: 0 15px;
  }

  .footer_section .footer_widget_content {
    padding-bottom: 20px;
  }
}
@media screen and (max-width: 420px) {
  .banner_text p {
    font-size: 18px;
  }

  .featured_content .featured_icon_text {
    width: 300px;
  }

  .compare_content_item .compare_content {
    padding-top: 0;
    margin-bottom: 30px;
  }

  .compare_section .compare_title h2 {
    font-size: 26px;
  }

  .theme_feature_section {
    padding-top: 20px;
  }

  .theme_feature_area .theme_feature_content .theme_feature_list li {
    margin-bottom: 15px;
  }

  .banner_text .download_btn {
    width: 160px;
    height: 50px;
  }

  .sign_up_btn {
    height: 35px;
    line-height: 35px;
    width: 85px;
    font-size: 14px;
  }

  .sign_up_btn {
    right: 60px;
  }

  .trusted_content_img li {
    margin: 0 10px 10px 0;
  }

  .featured_service_section .section_title {
    padding-bottom: 20px;
  }

  .compare_section_two .compare_content,
  .compare_content_item .compare_list {
    padding-left: 0;
  }

  .testimonial_content {
    padding: 10px;
  }

  .testimonial_content .testimonial_text {
    padding: 25px 20px;
  }

  .newslatter_content .newslatter_title {
    padding: 0px 15px 25px;
  }

  .newslatter_content .newslatter_title h2 {
    font-size: 25px;
    padding-bottom: 15px;
  }

  .newslatter_content .newslatter-form .nws-button button {
    width: 90px;
    font-size: 14px;
  }

  .newslatter_content .newslatter-form input {
    padding: 0 15px;
  }

  .newslatter_content .newslatter_title p {
    font-size: 14px;
  }

  .newslatter_content {
    position: static;
    padding: 55px 0px 55px;
    margin-bottom: 50px;
  }

  .footer_section {
    padding-top: 0;
  }

  .footer_section {
    margin-top: 0;
  }

  .testimonial_section {
    padding-top: 70px;
  }

  .testimonial_name_designation .testimonial_meta h4 {
    font-size: 22px;
  }

  .scrollup {
    width: 45px;
    right: 15px;
    height: 45px;
    bottom: 20px;
    line-height: 45px;
  }

  .theme_feature_area .theme_feature_content .theme_feature_list li {
    font-size: 16px;
  }

  .theme_feature_area .theme_feature_content p {
    font-size: 16px;
  }
}
@media screen and (max-width: 380px) {
  .banner_text h1 {
    font-size: 32px;
  }

  .banner_text p {
    font-size: 16px;
  }

  .compare_section .compare_title h2 {
    font-size: 24px;
  }

  .trusted_user_section {
    padding: 20px 0px 60px;
  }

  .section_title .section_title_text h2 {
    font-size: 24px;
  }

  .testimonial_indicator .carousel-indicators li:nth-child(4) {
    top: 60px;
    left: 55px;
  }

  .testimonial_indicator .carousel-indicators li:nth-child(1) {
    bottom: 85px;
    right: 50px;
  }

  .testimonial_indicator {
    background-size: 325px 460px;
  }

  .testimonial_indicator .carousel-indicators li:nth-child(1) {
    bottom: 135px;
    right: 25px;
  }
}
/*---------------------------------------------------- */
/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*SaaS Classic*/
/*----------------------------------------------------*/
.saas-classic {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-size: 16px;
  line-height: 1.4;
  font-family: "Roboto";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

.saas-classic::selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.saas-classic::-moz-selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.saas-classic-preloader {
  background-color: #fff;
  background: #fff url("../img/saas-c/pre.svg") no-repeat center center;
}

.saas2-headline h1,
.saas2-headline h2,
.saas2-headline h3,
.saas2-headline h4,
.saas2-headline h5,
.saas2-headline h6 {
  margin: 0;
  font-family: "Poppins";
}

.saas-classic-scrollup {
  width: 55px;
  right: 20px;
  z-index: 5;
  height: 55px;
  bottom: 20px;
  display: none;
  position: fixed;
  border-radius: 100%;
  line-height: 55px;
  background-image: linear-gradient(-45deg, #a80202 32%, #6b2c94 100%);
}

.saas-classic-scrollup i {
  color: #fff;
  font-size: 20px;
}

.saas_two_section_title {
  margin: 0 auto;
  max-width: 465px;
}

.saas_two_section_title .title_tag {
  padding: 5px 30px;
}

.saas_two_section_title h2 {
  font-size: 40px;
  padding-top: 15px;
}

.saas_btn a {
  font-size: 15px;
  font-weight: 700;
  border-radius: 30px;
  display: inline-block;
  border: 2px solid #6c2b95;
}

.saas_btn:before {
  top: 8px;
  left: 32px;
  font-weight: 900;
  font-size: 22px;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

/*---------------------------------------------------- */
/*global area*/
/*----------------------------------------------------*/
@keyframes fadeFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeFromUp {
  animation-name: fadeFromUp;
}

.fadeFromRight {
  animation-name: fadeFromRight;
}

.fadeFromLeft {
  animation-name: fadeFromLeft;
}

.saas_two_section_title .title_tag,
.saas_two_banner_section .s2-banner_content .s2-tilte_tag,
.saas_two_feature_section .s2-feature_text .feature_tag,
.integration_section .integration_text .feature_tag {
  color: #951fb3;
  font-size: 18px;
  font-weight: 500;
  padding: 3px 30px;
  border-radius: 8px;
  display: inline-block;
  background-color: #ede0f4;
}

.saas_two_section_title h2,
.saas_two_banner_section .s2-banner_content h1,
.saas_two_about_section .s2-about_text_icon .s2-about_text h3,
.saas_two_feature_section .s2-feature_text h2,
.integration_section .integration_text h2,
.saas_two_team_section .s2-team_img_text .s2_name_info h3 {
  font-weight: 700;
  color: #010101;
}

.saas_two_section_title h2 span,
.saas_two_banner_section .s2-banner_content h1 span,
.saas_two_about_section .s2-about_text_icon .s2-about_text h3 span,
.saas_two_feature_section .s2-feature_text h2 span,
.integration_section .integration_text h2 span,
.saas_two_team_section .s2-team_img_text .s2_name_info h3 span {
  font-weight: 300;
}

.saas_btn:before,
.service_read_more a:after,
.saas_two_about_section .s2-about_text_icon .s2-about_text a:after,
.integration_section .integration_text a:after,
.saas_two_banner_section .s2-banner_content .banner_btn a i,
.saas_two_feature_section .s2-feature_text .saas_btn a i,
.s2-pricing_section .s2-pricing_item .s2-pricing_btn a i {
  background: linear-gradient(-45deg, #6b2c94 32%, #fc01fd 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.saas_two_about_section .s2-about_text_icon .s2-about_text p,
.saas_two_feature_section .s2-feature_text p,
.integration_section .integration_text p {
  color: #353535;
  font-size: 17px;
  line-height: 1.588;
  padding-bottom: 20px;
}

.service_read_more a,
.saas_two_about_section .s2-about_text_icon .s2-about_text a,
.integration_section .integration_text a {
  color: #0066ff;
  font-size: 15px;
  font-weight: 600;
  position: relative;
  font-family: "Poppins";
}

.service_read_more a:after,
.saas_two_about_section .s2-about_text_icon .s2-about_text a:after,
.integration_section .integration_text a:after {
  top: 0px;
  right: -35px;
  content: "";
  font-size: 20px;
  font-weight: 900;
  position: absolute;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s all ease-in-out;
  font-family: "Font Awesome 5 Free";
}

.service_read_more a:before,
.saas_two_about_section .s2-about_text_icon .s2-about_text a:before,
.integration_section .integration_text a:before {
  height: 2px;
  width: 0%;
  bottom: -3px;
  content: "";
  position: absolute;
  background-color: #0066ff;
  transition: 0.4s all ease-in-out;
}

.service_read_more a:hover:before,
.saas_two_about_section .s2-about_text_icon .s2-about_text a:hover:before,
.integration_section .integration_text a:hover:before {
  width: 100%;
}

.service_read_more a:hover:after,
.saas_two_about_section .s2-about_text_icon .s2-about_text a:hover:after,
.integration_section .integration_text a:hover:after {
  opacity: 1;
  right: -25px;
  transition-delay: 0.3s;
  visibility: visible;
}

.saas_two_banner_section .s2-banner_content .banner_btn a,
.saas_two_feature_section .s2-feature_text .saas_btn a,
.s2-pricing_section .s2-pricing_item .s2-pricing_btn a {
  transition: 0.3s all ease-in-out;
  position: relative;
  overflow: hidden;
  border-radius: 50px;
  display: inline-block;
}

.saas_two_banner_section .s2-banner_content .banner_btn a:before,
.saas_two_feature_section .s2-feature_text .saas_btn a:before,
.s2-pricing_section .s2-pricing_item .s2-pricing_btn a:before {
  position: absolute;
  content: "";
  width: 0px;
  height: 0px;
  right: 0;
  top: 45px;
  right: 15px;
  box-shadow: 0 0 30px 31px rgba(213, 8, 217, 0.9);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s all ease-in-out;
}

.saas_two_banner_section .s2-banner_content .banner_btn a:after,
.saas_two_feature_section .s2-feature_text .saas_btn a:after,
.s2-pricing_section .s2-pricing_item .s2-pricing_btn a:after {
  height: 100%;
  width: 100%;
  position: absolute;
  content: "";
  top: -100%;
  right: 0;
  z-index: -2;
  transition: 0.3s all ease-in-out;
  background-color: #6c2b95;
}

.saas_two_banner_section .s2-banner_content .banner_btn a:hover,
.saas_two_feature_section .s2-feature_text .saas_btn a:hover,
.s2-pricing_section .s2-pricing_item .s2-pricing_btn a:hover {
  color: #fff;
}

.saas_two_banner_section .s2-banner_content .banner_btn a:hover i,
.saas_two_feature_section .s2-feature_text .saas_btn a:hover i,
.s2-pricing_section .s2-pricing_item .s2-pricing_btn a:hover i {
  background: none;
  -webkit-text-fill-color: inherit;
}

.saas_two_banner_section .s2-banner_content .banner_btn a:hover:before,
.saas_two_feature_section .s2-feature_text .saas_btn a:hover:before,
.s2-pricing_section .s2-pricing_item .s2-pricing_btn a:hover:before {
  opacity: 1;
  right: 25px;
  visibility: visible;
}

.saas_two_banner_section .s2-banner_content .banner_btn a:hover:after,
.saas_two_feature_section .s2-feature_text .saas_btn a:hover:after,
.s2-pricing_section .s2-pricing_item .s2-pricing_btn a:hover:after {
  top: 0;
}

/*---------------------------------------------------- */
/*Header area*/
/*----------------------------------------------------*/
.saas_two_main_header {
  width: 100%;
  z-index: 9;
  padding: 15px 0px;
  position: absolute;
  background-color: #fff;
  box-shadow: 0px 11px 68px 0px rgba(0, 0, 0, 0.14);
}

.saas_two_main_header .sign_up_btn {
  display: inline-block;
}

.saas_two_main_header .brand_logo {
  padding-top: 10px;
}

.saas_two_main_header .s2-main-navigation {
  display: inline-block;
  padding-top: 20px;
}

.saas_two_main_header .s2-main-navigation .navbar-nav {
  display: inherit;
}

.saas_two_main_header .s2-main-navigation .navbar-nav li {
  font-size: 15px;
  font-weight: 600;
  margin-right: 55px;
  font-family: "Poppins";
}

.saas_two_main_header .s2-main-navigation .navbar-nav li a {
  display: inline;
  position: relative;
  padding-bottom: 30px;
  transition: 0.3s all ease-in-out;
}

.saas_two_main_header .s2-main-navigation .navbar-nav li .active,
.saas_two_main_header .s2-main-navigation .navbar-nav li a:hover {
  color: #d10edd;
}

.saas_two_main_header .s2-main-navigation .navbar-nav li a:hover:before,
.saas_two_main_header .s2-main-navigation .navbar-nav li a.active:before {
  width: 100%;
}

.saas_two_main_header .s2-main-navigation .navbar-nav li a:before {
  content: "";
  position: absolute;
  height: 2px;
  width: 0%;
  background-color: #d10edd;
  left: 2px;
  transition: 0.3s all ease-in-out;
  bottom: 25px;
}

.saas_two_main_header .s2-main-navigation .dropdown {
  position: relative;
}

.saas_two_main_header .s2-main-navigation .dropdown:after {
  content: "";
  position: absolute;
  right: -11px;
  top: 2px;
  transition: 0.3s all ease-in-out;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.saas_two_main_header .s2-main-navigation .dropdown:hover:after {
  color: #d10edd;
}

.saas_two_main_header .saas_sign_up_btn {
  height: 55px;
  width: 175px;
  float: right;
  line-height: 55px;
  border-radius: 30px;
  background-color: #73299a;
}

.saas_two_main_header .saas_sign_up_btn a {
  color: #fff;
  font-weight: 600;
  padding-left: 15px;
  position: relative;
}

.saas_two_main_header .saas_sign_up_btn a:before {
  left: -5px;
  top: 4px;
  color: #fff;
  font-size: 13px;
  line-height: 1;
  content: "";
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.saas_two_main_header .dropdown .dropdown-menu {
  top: 65px;
  left: 0;
  opacity: 0;
  z-index: 2;
  margin: 0px;
  padding: 0px;
  height: auto;
  width: 200px;
  display: block;
  border: none;
  padding: 10px 0px 0px;
  visibility: hidden;
  position: absolute;
  border-radius: 0;
  background-color: #fff;
  transition: all 0.4s ease-in-out;
  border-bottom: 2px solid #73299a;
  box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
}

.saas_two_main_header .dropdown .dropdown-menu li {
  width: 100%;
  padding: 10px 15px;
  border-bottom: 1px solid #e5e5e5;
}

.saas_two_main_header .dropdown .dropdown-menu li a {
  color: #343434;
  font-size: 14px;
  padding: 10px 0px;
  position: relative;
  transition: 0.3s all ease-in-out;
}

.saas_two_main_header .dropdown .dropdown-menu li a:before {
  display: none;
}

.saas_two_main_header .dropdown .dropdown-menu li a:after {
  left: 0;
  top: 15px;
  width: 8px;
  height: 8px;
  content: "";
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  background-color: #73299a;
  transition: 0.3s all ease-in-out;
}

.saas_two_main_header .dropdown .dropdown-menu li a:hover {
  padding-left: 15px;
}

.saas_two_main_header .dropdown .dropdown-menu li a:hover:after {
  transform: scale(1);
}

.saas_two_main_header .dropdown:hover .dropdown-menu {
  top: 50px;
  opacity: 1;
  visibility: visible;
}

.saas_2-menu-bg-overlay {
  background-color: #fff;
  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  position: fixed;
  top: -90px;
  padding: 10px 0px;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
}

.saas_two_main_header.saas_2-menu-bg-overlay {
  z-index: 9;
  top: 0px;
  box-shadow: 0 0 20px -10px rgba(0, 0, 0, 0.8);
}

.saas_two_main_header.saas_2-menu-bg-overlay .dropdown:hover .dropdown-menu {
  top: 45px;
}

/*---------------------------------------------------- */
/*Banner area*/
/*----------------------------------------------------*/
@keyframes floatY {
  0% {
    transform: translatey(0px);
  }
  50% {
    transform: translatey(-20px);
  }
  100% {
    transform: translatey(0px);
  }
}
@keyframes floatX {
  0% {
    transform: translateX(80%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(80%);
  }
}
.saas_two_banner_section {
  margin: 0 auto;
  max-width: 1920px;
  background-color: #fafafa;
  padding: 235px 0px 140px 0px;
}

.saas_two_banner_section:before {
  right: 0;
  width: 80%;
  content: "";
  top: -10px;
  height: 115%;
  position: absolute;
  background-repeat: no-repeat;
  background-image: url(../img/saas-c/banner/b-shape1.png);
}

.saas_two_banner_section .s2-banner_content {
  position: relative;
  z-index: 1;
  max-width: 450px;
}

.saas_two_banner_section .s2-banner_content .s2-tilte_tag {
  padding: 5px 30px;
}

.saas_two_banner_section .s2-banner_content h1 {
  font-size: 55px;
  padding: 18px 0px 20px 0px;
}

.saas_two_banner_section .s2-banner_content p {
  font-size: 18px;
  padding-bottom: 35px;
}

.saas_two_banner_section .s2-banner_content .banner_btn a {
  height: 50px;
  width: 175px;
  text-align: center;
  line-height: 45px;
  border-radius: 50px;
  margin-right: 20px;
  font-weight: 700;
  color: #010101;
  font-family: "Poppins";
  display: inline-block;
  z-index: 1;
  border: 2px solid #6c2b95;
}

.saas_two_banner_section .s2-banner_content .banner_btn a i {
  font-size: 20px;
  margin-right: 8px;
}

.saas_two_banner_section .s2-banner_content .banner_btn span {
  color: #6a6a6a;
  display: block;
  font-size: 14px;
  margin-top: 5px;
  margin-left: 10px;
}

.saas_two_banner_section .s2-banner_area .banner_mockup {
  position: absolute;
  top: -40px;
  right: -70px;
  z-index: 1;
}

.saas_two_banner_section .s2-banner_shape1 {
  left: 0;
  top: 85px;
}

.saas_two_banner_section .s2-banner_shape2 {
  top: 0;
  right: 0;
  animation: floatY 3s ease-in-out infinite;
}

.saas_two_banner_section .s2-banner_shape3 {
  top: 0;
  right: 50%;
  transform: translateX(80%);
  animation: floatX 10s ease-in-out infinite;
}

/*---------------------------------------------------- */
/*Service area*/
/*----------------------------------------------------*/
.saas_two_service_section {
  padding: 90px 0px 110px;
}

.saas_two_service_section .service_content {
  padding-top: 70px;
}

.saas_two_service_section .service_content_box {
  border-radius: 10px;
  margin-bottom: 50px;
  padding: 25px 30px 30px 35px;
  z-index: 1;
  background-color: #fff;
  transition: 0.5s all ease-in-out;
  box-shadow: 0px 20px 81px 0px rgba(8, 0, 20, 0.14);
}

.saas_two_service_section .service_content_box:before {
  content: "";
  width: 100%;
  top: 0;
  left: 0;
  height: 100%;
  z-index: -3;
  border-radius: 10px;
  position: absolute;
  transition: 0.5s all ease-in-out;
  background-image: linear-gradient(45deg, #fc01fd 32%, #6b2c94 100%);
}

.saas_two_service_section .service_content_box:after {
  content: "";
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -2;
  border-radius: 10px;
  background-color: #fff;
}

.saas_two_service_section .service_content_box:hover {
  transform: translate(-7px, -7px);
  box-shadow: 0px 20px 81px 0px rgba(8, 0, 20, 0.3);
}

.saas_two_service_section .service_content_box:hover:before {
  transform: translate(7px, 7px);
}

.saas_two_service_section .service_content_box .service_icon_box {
  width: 60px;
  margin-bottom: 18px;
}

.saas_two_service_section .service_content_box .service_icon_box .upper_icon svg {
  height: 48px;
  width: 48px;
  fill: #30347b;
}

.saas_two_service_section .service_content_box .service_icon_box .lower_icon {
  position: absolute;
  top: 10px;
  z-index: -1;
  right: 0;
}

.saas_two_service_section .service_content_box .service_icon_box .lower_icon svg {
  width: 45px;
  height: 45px;
  fill: #c4a2fc;
}

.saas_two_service_section .service_content_box .service_text_box h3 {
  color: #010101;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 15px;
}

.saas_two_service_section .service_content_box .service_text_box p {
  line-height: 1.5;
  color: #666666;
}

/*---------------------------------------------------- */
/*Service area*/
/*----------------------------------------------------*/
.saas_two_about_section {
  padding: 70px 0px;
  background-color: #ebedf2;
}

.saas_two_about_section:before {
  content: "";
  position: absolute;
  top: 0;
  right: -400px;
  height: 100%;
  width: 800px;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url(../img/saas-c/banner/abshape.png);
}

.saas_two_about_section:after {
  content: "";
  height: 600px;
  width: 600px;
  right: -380px;
  top: 285px;
  border-radius: 100%;
  position: absolute;
  background-repeat: no-repeat;
  background-image: url(../img/saas-c/banner/abshape2.png);
}

.saas_two_about_section .about_content_s2 {
  padding: 50px 70px 50px 110px;
}

.saas_two_about_section .s2-about_text_icon {
  padding-top: 35px;
}

.saas_two_about_section .s2-about_text_icon .s2-about_icon {
  width: 70px;
  height: 70px;
  line-height: 70px;
  padding-top: 15px;
  border-radius: 100%;
  margin-bottom: 16px;
  background-color: #6c2b95;
}

.saas_two_about_section .s2-about_text_icon .s2-about_icon svg {
  fill: #fff;
  width: 40px;
  height: 40px;
}

.saas_two_about_section .s2-about_text_icon .s2-about_text h3 {
  font-size: 36px;
  line-height: 1.278;
  padding-bottom: 15px;
}

/*---------------------------------------------------- */
/*Feature area*/
/*----------------------------------------------------*/
.saas_two_feature_section {
  padding: 105px 0px 85px;
}

.saas_two_feature_section .s2-feature_text {
  max-width: 520px;
  padding: 40px 0 0 50px;
}

.saas_two_feature_section .s2-feature_text h2 {
  font-size: 40px;
  line-height: 1.25;
  padding: 15px 0px 15px;
}

.saas_two_feature_section .s2-feature_text p {
  padding-bottom: 43px;
}

.saas_two_feature_section .s2-feature_text .saas_btn a {
  padding: 10px 25px 10px 25px;
}

.saas_two_feature_section .s2-feature_text .saas_btn a i {
  font-size: 20px;
  margin-right: 5px;
}

.saas_two_feature_section .s2-feature_right {
  padding-left: 35px;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list {
  margin-bottom: 10px;
  padding: 22px 32px 20px;
  border-radius: 10px;
  display: inline-block;
  transition: 0.4s all ease-in-out;
  position: relative;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:before {
  position: absolute;
  content: "";
  height: 0px;
  width: 5px;
  left: 0;
  top: 50%;
  border-radius: 50px;
  transform: translateY(-50%);
  transition: 0.3s all ease-in-out;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:hover:before {
  height: 60px;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list .s2-feature_icon {
  width: 95px;
  float: left;
  height: 95px;
  border-radius: 100%;
  line-height: 105px;
  margin-right: 30px;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list .s2-feature_icon:after {
  left: 0;
  right: 0;
  top: 5px;
  width: 90%;
  height: 90%;
  content: "";
  margin: 0 auto;
  position: absolute;
  border: 5px solid #fff;
  border-radius: 100%;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list .s2-feature_icon i {
  font-size: 30px;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list .s2-feature_text_box {
  max-width: 310px;
  display: inline-block;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list .s2-feature_text_box h3 {
  color: #010101;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 15px;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:hover {
  margin-left: 30px;
  box-shadow: 0px 3px 43px 0px rgba(0, 0, 0, 0.13);
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:nth-child(1) .s2-feature_icon {
  background-color: #d9f3de;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:nth-child(1) .s2-feature_icon i {
  color: #049507;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:nth-child(1):before {
  background-color: #049507;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:nth-child(2) .s2-feature_icon {
  background-color: #e1e1f5;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:nth-child(2) .s2-feature_icon i {
  color: #6c2b95;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:nth-child(2):before {
  background-color: #6c2b95;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:nth-child(3) .s2-feature_icon {
  background-color: #ebdcf4;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:nth-child(3) .s2-feature_icon i {
  color: #d912dc;
}

.saas_two_feature_section .s2-feature_right .s2-feature_list:nth-child(3):before {
  background-color: #d912dc;
}

/*---------------------------------------------------- */
/*Intergation area*/
/*----------------------------------------------------*/
.integration_section {
  background-color: #f0f2f7;
  padding: 120px 0px 55px;
}

.integration_section .integration_img {
  padding-top: 10px;
}

.integration_section .integration_text {
  padding-right: 50px;
}

.integration_section .integration_text h2 {
  font-size: 40px;
  line-height: 1.25;
  padding: 15px 0px 20px;
}

.integration_section .integration_text p {
  color: #666666;
  padding-bottom: 18px;
}

/*---------------------------------------------------- */
/*Team area*/
/*----------------------------------------------------*/
.saas_two_team_section {
  padding: 118px 0px;
}

.saas_two_team_section .s2-team_membar {
  padding-top: 130px;
}

.saas_two_team_section .s2-team_img_text {
  width: 100%;
  border-radius: 10px;
  display: inline-block;
  padding: 70px 15px 20px;
  transition: 0.4s all ease-in-out;
  box-shadow: 0px 12px 43px 0px rgba(0, 0, 0, 0.13);
}

.saas_two_team_section .s2-team_img_text:hover {
  box-shadow: 0px 23px 43px 0px rgba(0, 0, 0, 0.26);
}

.saas_two_team_section .s2-team_img_text:hover .s2_tean_img:before,
.saas_two_team_section .s2-team_img_text:hover .s2_tean_img:after {
  opacity: 1;
  visibility: visible;
}

.saas_two_team_section .s2-team_img_text .s2_tean_img {
  width: 130px;
  height: 130px;
  overflow: hidden;
  border-radius: 100%;
  position: absolute;
  top: -75px;
  left: 15px;
}

.saas_two_team_section .s2-team_img_text .s2_tean_img:before,
.saas_two_team_section .s2-team_img_text .s2_tean_img:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
  border-radius: 100%;
  border: 3px solid #cc0fda;
  z-index: 1;
  transition: 0.3s all ease-in-out;
}

.saas_two_team_section .s2-team_img_text .s2_tean_img:after {
  height: 95%;
  width: 95%;
  top: 50%;
  right: 0;
  margin: 0 auto;
  border: 3px solid #fff;
  transform: translateY(-50%);
}

.saas_two_team_section .s2-team_img_text .s2_name_info {
  float: left;
}

.saas_two_team_section .s2-team_img_text .s2_name_info h3 {
  font-size: 22px;
  padding-bottom: 5px;
}

.saas_two_team_section .s2-team_img_text .s2_name_info .s2-designation {
  font-size: 15px;
  font-weight: 500;
  color: #53117f;
}

.saas_two_team_section .s2-team_img_text .s2_name_info .s2_mem_contact {
  padding-top: 25px;
}

.saas_two_team_section .s2-team_img_text .s2_name_info .s2_mem_contact span {
  font-size: 15px;
  color: #737272;
  display: block;
  margin-bottom: 5px;
}

.saas_two_team_section .s2-team_img_text .s2_name_info .s2_mem_contact span i {
  color: #cc0fda;
  margin-right: 8px;
}

.saas_two_team_section .s2-team_img_text .s2-mem_social {
  bottom: 30px;
  right: 15px;
  position: absolute;
}

.saas_two_team_section .s2-team_img_text .s2-mem_social ul {
  opacity: 0;
  margin-bottom: 15px;
  font-size: 14px;
  padding-top: 10px;
  visibility: hidden;
  position: relative;
  top: -50px;
  transition: 0.3s all ease-in-out;
}

.saas_two_team_section .s2-team_img_text .s2-mem_social li {
  color: #fff;
  text-align: center;
  margin-top: 15px;
  transition: 0.3s all ease-in-out;
}

.saas_two_team_section .s2-team_img_text .s2-mem_social li:hover {
  transform: translateX(5px);
}

.saas_two_team_section .s2-team_img_text .s2-mem_social .s2-share_btn {
  height: 50px;
  width: 50px;
  line-height: 50px;
  border-radius: 100%;
  transition: 0.3s all ease-in-out;
  background-color: #53117f;
}

.saas_two_team_section .s2-team_img_text .s2-mem_social .s2-share_btn i {
  color: #fff;
}

.saas_two_team_section .s2-team_img_text .s2-mem_social:after {
  bottom: 20px;
  z-index: -1;
  content: "";
  width: 50px;
  height: 0%;
  opacity: 0;
  border-radius: 35px;
  position: absolute;
  visibility: hidden;
  background-color: #53117f;
  transition: 0.5s all ease-in-out;
}

.saas_two_team_section .s2-team_img_text:hover .s2-mem_social:after {
  height: 100%;
  bottom: 0px;
  opacity: 1;
  visibility: visible;
}

.saas_two_team_section .s2-team_img_text:hover .s2-share_btn {
  background-color: #cc0fda;
}

.saas_two_team_section .s2-team_img_text:hover ul {
  top: 0;
  transition-delay: 0.5s;
  opacity: 1;
  visibility: visible;
}

#s2_team_slide .owl-stage-outer {
  overflow: visible;
}

#s2_team_slide .owl-item {
  opacity: 0;
  transition: opacity 500ms;
}

#s2_team_slide .owl-item.active {
  opacity: 1;
}

#s2_team_slide .owl-nav {
  text-align: center;
  display: table;
  margin: 35px auto 0;
  padding-top: 3px;
  border-radius: 20px;
  background-color: #beaacb;
}

#s2_team_slide .owl-nav .owl-next,
#s2_team_slide .owl-nav .owl-prev {
  margin: 0px 10px;
  font-size: 22px;
  line-height: 10px;
  display: inline-block;
}

#s2_team_slide .owl-nav .owl-next i,
#s2_team_slide .owl-nav .owl-prev i {
  color: #fff;
  transition: 0.3s all ease-in-out;
}

#s2_team_slide .owl-nav .owl-next i:hover,
#s2_team_slide .owl-nav .owl-prev i:hover {
  color: #53117f;
}

/*---------------------------------------------------- */
/* Faq area*/
/*----------------------------------------------------*/
.s2-faq_section {
  z-index: 1;
  padding: 115px 0px 85px;
  background-color: #ebedf2;
}

.s2-faq_section:after {
  top: 0;
  width: 100%;
  content: "";
  height: 100%;
  z-index: -1;
  position: absolute;
  background-image: url(../img/saas-c/banner/fbg.png);
}

.s2-faq_section .s2_faq_content {
  padding-top: 80px;
}

.s2-faq_section .s2_faq_content button {
  border: none;
  color: #010101;
  font-size: 19px;
  font-weight: 700;
  font-family: "Poppins";
  padding: 0px 0px 20px;
  position: relative;
  width: 100%;
  text-align: left;
  background-color: transparent;
}

.s2-faq_section .s2_faq_content button:after {
  top: 5px;
  right: 0;
  color: #951fb3;
  font-size: 14px;
  content: "";
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.s2-faq_section .s2_faq_content .collapsed:after {
  content: "";
}

.s2-faq_section .s2_faq_content .s2_faq {
  padding-bottom: 10px;
}

.s2-faq_section .s2_faq_content .s2_faq .s2_faq-body {
  padding-bottom: 30px;
  text-align: justify;
  max-width: 530px;
  color: #383838;
  line-height: 1.625;
}

/*---------------------------------------------------- */
/* Pricing area*/
/*----------------------------------------------------*/
@keyframes flying {
  0% {
    transform: translate(2px, 2px);
  }
  50% {
    transform: translate(-2px, -2px);
  }
  100% {
    transform: translate(2px, 2px);
  }
}
.s2-pricing_section {
  padding: 120px 0px 100px;
}

.s2-pricing_section .s2-pricing_content {
  padding-top: 80px;
}

.s2-pricing_section .s2-pricing_item {
  margin: 0 auto;
  overflow: hidden;
  max-width: 330px;
  border-radius: 15px;
  transition: 0.4s all ease-in-out;
  box-shadow: 0px 23px 43px 0px rgba(0, 0, 0, 0.13);
}

.s2-pricing_section .s2-pricing_item .s2-pricing_price {
  background-color: #818181;
  padding: 40px 50px 25px;
  margin-bottom: 25px;
  z-index: 1;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_price .pricing_icon {
  height: 65px;
  width: 60px;
  margin-right: 25px;
  border-radius: 8px;
  background-color: #fff;
  padding-top: 12px;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_price .pricing_icon svg {
  height: 40px;
  width: 40px;
  fill: #000;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_price .pricing_icon:after {
  top: 0;
  left: 0;
  z-index: -1;
  content: "";
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: visible;
  position: absolute;
  border-radius: 8px;
  transform: scale(0.5);
  transition: 0.4s all ease-in-out;
  background-image: linear-gradient(-45deg, #a80202 32%, #6b2c94 100%);
}

.s2-pricing_section .s2-pricing_item .s2-pricing_price .s2-pricing_text {
  font-family: "Poppins";
  color: #fff;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_price .s2-pricing_text span {
  font-size: 20px;
  font-weight: 600;
  display: block;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_price .s2-pricing_text strong {
  font-size: 50px;
  line-height: 1;
  font-weight: 700;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_price .s2-icon_bg {
  position: absolute;
  right: 20px;
  top: 40px;
  opacity: 0;
  visibility: hidden;
  z-index: -1;
  transition: 0.5s all ease-in-out;
  animation: flying 1.5s linear infinite;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_price .s2-icon_bg svg {
  height: 80px;
  width: 80px;
  fill: #aaaaaa;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_list li {
  border-bottom: 1px solid #e6e8e9;
  padding: 18px 25px 16px 45px;
  font-family: "Poppins";
  font-weight: 500;
  font-size: 14px;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_list li:last-child {
  border: none;
  padding-bottom: 0;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_list .s2-checked,
.s2-pricing_section .s2-pricing_item .s2-pricing_list .s2-unchecked {
  width: 22px;
  height: 22px;
  margin-right: 10px;
  border-radius: 100%;
  line-height: 22px;
  position: relative;
  box-shadow: 0px 6px 18px 0px rgba(0, 197, 0, 0.5);
  background-image: linear-gradient(0deg, #00f500 1%, #00db00 53%, #00c000 100%);
}

.s2-pricing_section .s2-pricing_item .s2-pricing_list .s2-checked:before,
.s2-pricing_section .s2-pricing_item .s2-pricing_list .s2-unchecked:before {
  top: 0;
  left: 0;
  right: 0;
  color: #fff;
  font-size: 12px;
  content: "";
  font-weight: 900;
  text-align: center;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.s2-pricing_section .s2-pricing_item .s2-pricing_list .s2-checked i,
.s2-pricing_section .s2-pricing_item .s2-pricing_list .s2-unchecked i {
  color: #fff;
  font-size: 12px;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_list .s2-unchecked {
  background-color: #ff0000;
  background-image: none;
  box-shadow: none;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_list .s2-unchecked:before {
  content: "";
}

.s2-pricing_section .s2-pricing_item .s2-pricing_btn {
  padding: 25px 0px;
  text-align: center;
  margin-top: 8px;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_btn a {
  height: 45px;
  width: 160px;
  text-align: center;
  line-height: 40px;
  border-radius: 50px;
  margin-right: 20px;
  font-weight: 700;
  color: #010101;
  font-family: "Poppins";
  display: inline-block;
  border: 2px solid #6c2b95;
}

.s2-pricing_section .s2-pricing_item .s2-pricing_btn a i {
  font-size: 20px;
  margin-right: 8px;
}

.s2-pricing_section .s2-pricing_item:hover {
  box-shadow: 0px 23px 43px 0px rgba(0, 0, 0, 0.26);
}

.s2-pricing_section .s2-pricing_item:hover .s2-icon_bg {
  opacity: 1;
  top: 30px;
  right: 15px;
  visibility: visible;
  transition-delay: 0.2s;
}

.s2-pricing_section .s2-pricing_item:hover .pricing_icon:after {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.s2-pricing_section .s2-pricing_item:hover .pricing_icon svg {
  fill: #fff;
}

/*---------------------------------------------------- */
/* Footer area*/
/*----------------------------------------------------*/
.saas_two_footer_section {
  z-index: 1;
  margin-top: 140px;
  padding: 0px 0px 65px;
}

.saas_two_footer_section:before {
  top: 0;
  left: 0;
  width: 100%;
  content: "";
  height: 100%;
  position: absolute;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url(../img/saas-c/banner/fobg.png);
}

.saas_two_footer_section .s2-newslatter_section {
  top: -90px;
  margin: 0 auto;
  max-width: 1170px;
  position: relative;
  border-radius: 20px;
  padding: 45px 0px 60px;
  background-color: #6c2b95;
  box-shadow: 0px 0px 16px 0px rgba(27, 2, 52, 0.34);
}

.saas_two_footer_section .s2-newslatter_section .s2-newslatter_title h2 {
  color: #fff;
  font-size: 36px;
  font-weight: 700;
  padding-bottom: 10px;
}

.saas_two_footer_section .s2-newslatter_section .s2-newslatter_title p {
  font-family: "Poppins";
  color: #fff;
}

.saas_two_footer_section .s2-newslatter_section .s2-newslatter-form {
  max-width: 660px;
  margin: 30px auto 0;
}

.saas_two_footer_section .s2-newslatter_section .s2-newslatter-form .nws-button {
  top: 0;
  right: 0;
}

.saas_two_footer_section .s2-newslatter_section .s2-newslatter-form input {
  height: 62px;
  width: 100%;
  border: none;
  max-width: 490px;
  padding-left: 30px;
  border-radius: 5px;
}

.saas_two_footer_section .s2-newslatter_section .s2-newslatter-form button {
  border: none;
  height: 62px;
  width: 155px;
  color: #fff;
  border-radius: 5px;
  background-color: #f104f5;
  position: absolute;
  top: 0;
  right: 0;
}

.saas_two_footer_section .s2-newslatter_section .newsletter_pattern_1 {
  top: -25px;
  left: 80px;
  z-index: 1;
  position: absolute;
}

.saas_two_footer_section .s2_footer_widget {
  font-family: "Poppins";
  font-size: 14px;
}

.saas_two_footer_section .s2_footer_widget .s2-footer_logo {
  margin-bottom: 25px;
}

.saas_two_footer_section .s2_footer_widget .footer_about {
  color: #fff;
  max-width: 235px;
  margin-bottom: 20px;
  line-height: 1.714;
}

.saas_two_footer_section .s2_footer_widget p {
  color: #fff;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_about p {
  width: 165px;
  margin-top: 8px;
  line-height: 1.714;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_about span {
  color: #f104f5;
  font-weight: 700;
  margin-bottom: 8px;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_menu {
  max-width: 340px;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_menu li {
  width: 50%;
  float: left;
  max-width: 320px;
  margin-bottom: 18px;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_menu li a {
  color: #fff;
  margin-left: 15px;
  position: relative;
  transition: 0.3s all ease-in-out;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_menu li a:before {
  top: 0;
  top: 0;
  left: -15px;
  color: #fff;
  font-size: 12px;
  content: "";
  font-weight: 900;
  position: absolute;
  transition: 0.3s all ease-in-out;
  font-family: "Font Awesome 5 Free";
}

.saas_two_footer_section .s2_footer_widget .s2_footer_menu li a:after {
  content: "";
  position: absolute;
  height: 1px;
  width: 0%;
  left: 0px;
  bottom: 0;
  transition: 0.3s all ease-in-out;
  background-color: #f104f5;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_menu li a:hover {
  margin-left: 25px;
  color: #f104f5;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_menu li a:hover:before {
  color: #f104f5;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_menu li a:hover:after {
  width: 100%;
}

.saas_two_footer_section .s2_footer_widget .s2_widget_title {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  padding-bottom: 35px;
}

.saas_two_footer_section .s2_footer_widget .s2_widget_title span {
  display: inline-block;
  margin-right: 20px;
}

.saas_two_footer_section .s2_footer_widget .s2_widget_title i {
  width: 100%;
  height: 1.1px;
  position: relative;
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.2);
}

.saas_two_footer_section .s2_footer_widget .s2_footer_social a {
  height: 30px;
  width: 30px;
  border-radius: 100%;
  background-color: #fff;
  line-height: 30px;
  text-align: center;
  margin-right: 5px;
  display: inline-block;
  transition: 0.3s all ease-in-out;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_social a:hover {
  transform: scale(1.1);
}

.saas_two_footer_section .s2_footer_widget .s2_footer_social form {
  margin: 18px 0px 30px;
  position: relative;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_social form input {
  height: 45px;
  background-color: #503d63;
  border: none;
  width: 100%;
  padding-left: 30px;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_social form button {
  color: #fff;
  width: 62px;
  border: none;
  text-align: center;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  background-color: #6c2b95;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_social .fb-bg {
  color: #16599b;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_social .tw-bg {
  color: #03a9f4;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_social .dr-bg {
  color: #a80202;
}

.saas_two_footer_section .s2_footer_widget .s2_footer_social .bh-bg {
  color: #0067ff;
}

.s2-copyright {
  color: #fff;
  padding: 18px 0px;
  font-size: 14px;
  font-family: "Poppins";
  background-color: #000000;
}

.s2-copyright a {
  color: #d772fe;
}

@keyframes slide {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 1920px 0;
  }
}
.cloud_anim {
  position: absolute;
  left: 0px;
  top: 50px;
  right: 0px;
  max-width: 1170px;
  width: 100%;
  height: 100%;
  z-index: 0;
  margin: 0 auto;
}

.newsletter_pattern_2 {
  width: 100%;
  height: 46px;
  background-repeat: no-repeat;
  left: 0px;
  top: 50px;
  animation: slide 60s linear infinite;
  -webkit-animation: slide 60s linear infinite;
}

.newsletter_pattern_3 {
  width: 100%;
  height: 46px;
  background-repeat: no-repeat;
  left: 100px;
  top: 70px;
  animation: slide 50s linear infinite;
  -webkit-animation: slide 50s linear infinite;
}

.newsletter_pattern_4 {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 150px;
  width: 150px;
  background-size: cover;
  transform: rotate(90deg);
  background-repeat: no-repeat;
}

.newsletter_pattern_5 {
  position: absolute;
  right: 0;
  height: 90%;
  width: 200px;
  top: 28px;
}

/*---------------------------------------------------- */
/* Footer area*/
/*----------------------------------------------------*/
.s2-mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 280px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 40px 0px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s ease-in;
}

.s2-mobile_menu_content .s2-mobile-main-navigation {
  width: 100%;
}

.s2-mobile_menu_content .s2-mobile-main-navigation .navbar-nav {
  width: 100%;
}

.s2-mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}

.s2-mobile_menu_content .s2-mobile-main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid #dcdcdc;
}

.s2-mobile_menu_content .s2-mobile-main-navigation .navbar-nav li:first-child {
  border-top: 1px solid #dcdcdc;
}

.s2-mobile_menu_content .s2-mobile-main-navigation .navbar-nav li a {
  color: #000;
  padding: 0;
  width: 100%;
  display: block;
  font-size: 14px;
  font-weight: 400;
  padding: 5px 30px;
  text-transform: uppercase;
}

.s2-mobile_menu_content .m-brand-logo {
  width: 160px;
  margin: 0 auto;
  margin-bottom: 30px;
}

.s2-mobile_menu_wrap.mobile_menu_on .s2-mobile_menu_content {
  right: 0px;
  transition: all 0.7s ease-out;
}

.mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: 0%;
  height: 120vh;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.5s ease-in-out;
}

.mobile_menu_overlay_on {
  overflow: hidden;
}

.s2-mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  opacity: 1;
  visibility: visible;
}

.s2-mobile_menu_button {
  position: absolute;
  display: none;
  right: 0;
  cursor: pointer;
  line-height: 40px;
  color: #73299a;
  text-align: center;
  font-size: 30px;
  top: -40px;
  z-index: 5;
}

.s2-mobile_menu .s2-mobile-main-navigation .navbar-nav li a:after {
  display: none;
}

.s2-mobile_menu .s2-mobile-main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}

.s2-mobile_menu .s2-mobile_menu_content .s2-mobile-main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
  width: 100%;
  border-top: 1px solid #dcdcdc;
}

.s2-mobile_menu .s2-mobile_menu_content .s2-mobile-main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 0 20px;
  line-height: 1;
}

.s2-mobile_menu .dropdown {
  position: relative;
}

.s2-mobile_menu .dropdown .dropdown-btn {
  position: absolute;
  top: 0px;
  right: 0;
  height: 30px;
  padding: 5px 10px;
}

.s2-mobile_menu .dropdown .dropdown-btn:before {
  content: "";
  position: absolute;
  height: 100%;
  width: 1px;
  top: 0;
  left: 0;
  background-color: #dcdcdc;
}

.s2-mobile_menu .s2-mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

/*---------------------------------------------------- */
/* Responsive area*/
/*----------------------------------------------------*/
@media screen and (max-width: 1140px) {
  .saas_two_footer_section .s2-newslatter_section {
    top: 0;
    max-width: inherit;
    margin-bottom: 50px;
  }

  .saas_two_footer_section {
    overflow: hidden;
    margin-top: 0;
    padding: 25px 0px 65px;
  }

  .saas_two_footer_section:before {
    height: 75%;
    top: auto;
    bottom: 0;
  }
}
@media screen and (max-width: 1120px) {
  .saas_two_feature_section .s2-feature_right {
    padding-left: 0;
  }

  .saas_two_feature_section .s2-feature_right .s2-feature_list .s2-feature_icon {
    margin-right: 10px;
  }
}
@media screen and (max-width: 1110px) {
  .saas_two_main_header .s2-main-navigation .navbar-nav li {
    margin-right: 25px;
  }

  .saas_two_banner_section .s2-banner_area .banner_mockup {
    max-width: 630px;
    top: 30px;
  }
}
@media screen and (max-width: 1015px) {
  .saas_two_main_header .s2-main-navigation .navbar-nav li {
    margin-right: 25px;
  }

  .saas_two_banner_section .s2-banner_area .banner_mockup {
    max-width: 630px;
    top: 30px;
  }

  .saas_two_feature_section .s2-feature_right .s2-feature_list {
    padding: 22px 20px 20px;
  }
}
@media screen and (max-width: 991px) {
  .saas_two_banner_section:before {
    width: 100%;
    top: 0;
    height: 100%;
  }

  .saas_two_banner_section {
    padding-bottom: 0;
    z-index: 1;
  }

  .saas_two_banner_section .s2-banner_shape1 {
    z-index: -1;
  }

  .saas_two_banner_section .s2-banner_area .banner_mockup {
    position: static;
    margin: 0 auto;
  }

  .saas_two_banner_section .s2-banner_content {
    max-width: 750px;
    margin: 0 auto;
    text-align: center;
    padding-bottom: 50px;
  }

  .about_content_s2 {
    max-width: 750px;
    margin: 0 auto;
  }

  .saas_two_main_header .s2-main-navigation {
    display: none;
  }

  .saas_two_banner_section .s2-banner_shape3 {
    top: 60px;
  }

  .saas_two_footer_section .s2-newslatter_section .newsletter_pattern_1 {
    display: none;
  }

  .saas_two_footer_section:before {
    background-size: cover;
    background-repeat: no-repeat;
  }

  .newsletter_pattern_4 {
    display: none;
  }

  .newsletter_pattern_5 {
    display: none;
  }

  .saas_two_footer_section {
    background-color: #1b0234;
  }

  .saas_two_footer_section .s2_footer_widget {
    margin-bottom: 30px;
  }

  .saas_two_footer_section .s2_footer_widget .s2_widget_title {
    padding-bottom: 20px;
  }

  .s2-mobile_menu_button {
    display: block;
  }

  .saas_two_main_header .saas_sign_up_btn {
    position: absolute;
    top: 3px;
    right: 70px;
    height: 40px;
    width: 120px;
    line-height: 40px;
  }

  .saas_two_main_header .saas_sign_up_btn a {
    padding-left: 0;
  }

  .saas_two_main_header .saas_sign_up_btn a:before {
    display: none;
  }

  .saas_two_about_section .about_content_s2 {
    padding: 30px 50px 30px 50px;
  }

  .saas_two_feature_section .s2-feature_right {
    padding-left: 0;
    margin-top: 40px;
  }

  .s2-pricing_section .s2-pricing_item {
    margin-bottom: 40px;
  }

  .saas_two_footer_section {
    padding-top: 0;
  }
}
@media screen and (max-width: 767px) {
  .saas_two_main_header .saas_sign_up_btn {
    top: -40px;
  }

  .saas_two_main_header.saas_2-menu-bg-overlay .saas_sign_up_btn {
    top: -42px;
  }

  .saas_two_service_section .service_content_box {
    max-width: 370px;
    margin: 0 auto;
    margin-bottom: 50px;
  }
}
@media screen and (max-width: 480px) {
  .saas_two_banner_section {
    padding-top: 180px;
  }

  .saas_two_banner_section .s2-banner_content h1 {
    font-size: 45px;
  }

  .saas_two_banner_section .s2-banner_content .banner_btn a {
    height: 40px;
    width: 140px;
    margin-right: 10px;
    line-height: 36px;
  }

  .saas_two_banner_section .s2-banner_content .banner_btn a i {
    font-size: 16px;
    margin-right: 5px;
  }

  .saas_two_banner_section .s2-banner_content {
    padding-bottom: 30px;
  }

  .saas_two_service_section {
    padding: 60px 0px;
  }

  .saas_two_about_section {
    padding: 60px 0px 30px;
  }

  .saas_two_about_section .about_content_s2 {
    padding: 20px 25px 20px 25px;
  }

  .saas_two_about_section:before {
    display: none;
  }

  .saas_two_about_section .s2-about_text_icon {
    padding-top: 0;
    margin-bottom: 30px;
  }

  .s2-about_img {
    margin-bottom: 30px;
  }

  .saas_two_feature_section {
    padding: 60px 0px 25px;
  }

  .saas_two_feature_section .s2-feature_right .s2-feature_list .s2-feature_icon {
    margin: 0 auto;
    float: none;
    margin-bottom: 20px;
  }

  .saas_two_feature_section .s2-feature_right .s2-feature_list .s2-feature_text_box {
    text-align: center;
    max-width: 100%;
  }

  .saas_two_team_section {
    padding: 60px 0px;
  }

  .s2-faq_section .s2_faq_content button {
    padding-right: 20px;
  }

  .s2-faq_section {
    padding: 60px 0px 25px;
  }

  .s2-pricing_section {
    padding: 60px 0 40px;
  }

  .saas_two_section_title h2 {
    font-size: 30px;
  }
}
@media screen and (max-width: 420px) {
  .saas_two_section_title h2 {
    font-size: 30px;
  }

  .saas_two_service_section .service_content {
    padding-top: 40px;
  }

  .saas_two_about_section .s2-about_text_icon .s2-about_text h3 {
    font-size: 30px;
  }

  .saas_two_about_section .s2-about_text_icon {
    margin-bottom: 15px;
  }

  .saas_two_feature_section .s2-feature_text h2 {
    font-size: 30px;
  }

  .saas_two_feature_section .s2-feature_text {
    padding: 20px 0 0 0px;
  }

  .saas_two_feature_section .s2-feature_text p {
    padding-bottom: 25px;
  }

  .integration_section .integration_text {
    padding-right: 0;
  }

  .integration_section .integration_text h2 {
    font-size: 30px;
  }

  .s2-faq_section .s2_faq_content {
    padding-top: 40px;
  }

  .s2-pricing_section .s2-pricing_content {
    padding-top: 40px;
  }

  .saas_two_footer_section {
    padding-bottom: 30px;
  }

  .saas_two_footer_section .s2-newslatter_section .s2-newslatter-form button {
    width: 100px;
  }

  .s2-pricing_section .s2-pricing_item .s2-pricing_list li {
    padding: 18px 20px 16px 20px;
  }

  .s2-pricing_section .s2-pricing_item .s2-pricing_price .s2-pricing_text strong {
    font-size: 40px;
  }

  .scrollup {
    width: 40px;
    height: 40px;
    right: 10px;
    line-height: 40px;
  }

  .saas_two_feature_section .s2-feature_right .s2-feature_list:hover {
    margin-left: 0;
  }

  .saas_two_main_header .saas_sign_up_btn {
    right: 55px;
  }
}
@media screen and (max-width: 380px) {
  .saas_two_banner_section .s2-banner_content h1 {
    font-size: 36px;
  }

  .saas_two_about_section .s2-about_text_icon .s2-about_text h3 {
    font-size: 26px;
  }
}
@media screen and (max-width: 320px) {
  .saas_two_banner_section .s2-banner_content h1 {
    font-size: 34px;
  }

  .saas_two_section_title h2 {
    font-size: 26px;
  }

  .saas_two_about_section .s2-about_text_icon .s2-about_text h3 {
    font-size: 24px;
  }
}
/*---------------------------------------------------- */
/*---------------------------------------------------- */
/*----------------------------------------------------*/
.str-team-section .str-team-member-item {
  padding-top: 30px;
}
.str-team-section .str-team-member-item .owl-nav .owl-prev, .str-portfolio-section .str-portfolio-area .owl-nav .owl-prev,
.str-team-section .str-team-member-item .owl-nav .owl-next,
.str-portfolio-section .str-portfolio-area .owl-nav .owl-next {
  top: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  line-height: 40px;
  text-align: center;
  position: absolute;
  border-radius: 100%;
  background-color: #d5dbe6;
  transform: translateY(-50%);
  transition: 0.3s all ease-in-out;
}

.str-team-section .str-team-member-item .owl-nav .owl-prev:hover, .str-portfolio-section .str-portfolio-area .owl-nav .owl-prev:hover,
.str-team-section .str-team-member-item .owl-nav .owl-next:hover,
.str-portfolio-section .str-portfolio-area .owl-nav .owl-next:hover {
  color: #fff;
  background-color: #4de3ef;
}

.str-team-section .str-team-member-item .owl-nav .owl-next, .str-portfolio-section .str-portfolio-area .owl-nav .owl-next {
  right: -20px;
}

.str-team-section .str-team-member-item .owl-nav .owl-prev, .str-portfolio-section .str-portfolio-area .owl-nav .owl-prev {
  left: -20px;
}
@keyframes borderpls {
  0% {
    box-shadow: 0 0 0 0 #082680;
  }
  70% {
    box-shadow: 0 0 0 30px #082680;
    opacity: 0;
  }
  to {
    box-shadow: 0 0 0 0 #082680;
    opacity: 0;
  }
}
@keyframes borderpls2 {
  0% {
    box-shadow: 0 0 0 0 #082680;
  }
  70% {
    box-shadow: 0 0 0 10px #082680;
    opacity: 0;
  }
  to {
    box-shadow: 0 0 0 0 #082680;
    opacity: 0;
  }
}
@keyframes fadeFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeFromUp {
  animation-name: fadeFromUp;
}

.fadeFromRight {
  animation-name: fadeFromRight;
}

.fadeFromLeft {
  animation-name: fadeFromLeft;
}

/*global area*/
/*----------------------------------------------------*/
.str-home {
  margin: 0;
  padding: 0;
  color: #494949;
  font-size: 16px;
  overflow-x: hidden;
  line-height: 1.625;
  font-family: "Roboto";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

.str-preloader {
  background-color: #fff;
  background: #fff url("../img/startup/pre.svg") no-repeat center center;
}

.decoration-wrapper {
  overflow: hidden;
  position: relative;
}
@keyframes zooming {
  0% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.05, 1.05);
  }
  100% {
    transform: scale(1, 1);
  }
}
.zooming {
  animation: zooming 18s infinite both;
}

.str-headline h1,
.str-headline h2,
.str-headline h3,
.str-headline h4,
.str-headline h5,
.str-headline h6 {
  margin: 0;
  font-family: "Poppins";
}

.str-section-title {
  max-width: 530px;
  margin: 0 auto;
}

.str-section-title .str-title-tag {
  color: #53117f;
  font-size: 18px;
  font-weight: 500;
}

.str-section-title h2 {
  color: #010101;
  font-size: 40px;
  font-weight: 700;
  position: relative;
  padding: 25px 0px 0px;
}

.str-section-title h2:before {
  left: 0;
  right: 0;
  top: 13px;
  height: 3px;
  width: 42px;
  content: "";
  margin: 0 auto;
  position: absolute;
  background-image: linear-gradient(125deg, #6407c1 0%, #324add 49%, #008df9 100%);
}

.str-section-title.str-title-left h2:before {
  right: auto;
}

.str-scrollup {
  width: 55px;
  right: 20px;
  z-index: 5;
  height: 55px;
  bottom: 20px;
  display: none;
  position: fixed;
  border-radius: 100%;
  line-height: 55px;
  background-color: #6e24c8;
}

.str-scrollup i {
  color: #fff;
  font-size: 20px;
}

/*---------------------------------------------------- */
/*header area*/
/*----------------------------------------------------*/
.str-main-header {
  width: 100%;
  z-index: 9;
  padding-top: 22px;
  position: absolute;
}

.str-main-header .str-header-top .str-social a {
  color: #fff;
  margin-left: 10px;
  transition: 0.3s all ease-in-out;
}

.str-main-header .str-header-top .str-social a:hover {
  color: #6e24c8;
}

.str-main-header .str-header-top .str-language {
  padding: 0px 20px 0px 60px;
  position: relative;
}

.str-main-header .str-header-top .str-language:after {
  top: 0;
  right: 0;
  color: #fff;
  content: "";
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.str-main-header .str-header-top .str-language:before {
  top: 2px;
  left: 28px;
  width: 2px;
  content: "";
  height: 20px;
  position: absolute;
  background-color: #cfcfcf;
}

.str-main-header .str-header-top .str-language a {
  color: #fff;
  font-size: 13px;
}

.str-main-header .str-header-top .str-language a img {
  margin-right: 8px;
}

.str-main-header .str-header-top .str-language ul {
  top: 50px;
  opacity: 0;
  z-index: 5;
  width: 120px;
  visibility: hidden;
  position: absolute;
  padding-bottom: 5px;
  background-color: #010101;
  transition: 0.3s all ease-in-out;
}

.str-main-header .str-header-top .str-language ul li a {
  display: block;
  padding: 5px 15px 3px;
  border-bottom: 1px solid #5a5a5a;
}

.str-main-header .str-header-top .str-language ul li:last-child a {
  border-bottom: none;
}

.str-main-header .str-header-top .str-language:hover ul {
  top: 35px;
  opacity: 1;
  visibility: visible;
}

.str-main-header .str-main-menu {
  margin-top: 18px;
}

.str-main-header .str-main-menu-item .navbar-nav {
  display: inherit;
}

.str-main-header .str-main-menu-item .str-main-navigation {
  background-color: #010004;
}

.str-main-header .str-main-menu-item .str-main-navigation li a {
  z-index: 1;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  position: relative;
  padding: 14px 40px;
  font-family: "Poppins";
  display: inline-block;
}

.str-main-header .str-main-menu-item .str-main-navigation li a:after {
  top: 0;
  right: 0;
  left: auto;
  width: 0;
  content: "";
  z-index: -1;
  height: 100%;
  position: absolute;
  transition: 0.5s all ease-in-out;
  background-image: linear-gradient(125deg, #6407c1 0%, #324add 49%, #008df9 100%);
}

.str-main-header .str-main-menu-item .str-main-navigation li a.active:after,
.str-main-header .str-main-menu-item .str-main-navigation li a:hover:after {
  left: 0;
  width: 100%;
  right: auto;
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown {
  position: relative;
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown:after {
  top: 18px;
  color: #fff;
  right: 24px;
  font-size: 10px;
  content: "";
  font-weight: 900;
  position: absolute;
  transition: 0.3s all ease-in-out;
  font-family: "Font Awesome 5 Free";
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown .dropdown-menu {
  top: 50px;
  left: -20px;
  opacity: 0;
  z-index: 2;
  margin: 0px;
  padding: 0px;
  height: auto;
  width: 200px;
  border: none;
  display: block;
  overflow: hidden;
  border-radius: 0;
  visibility: hidden;
  position: absolute;
  background-color: #fff;
  transition: all 0.4s ease-in-out;
  box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown .dropdown-menu li {
  width: 100%;
  margin-left: 0;
  border-bottom: 1px solid #e5e5e5;
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown .dropdown-menu li a {
  width: 100%;
  color: #343434;
  display: block;
  font-size: 14px;
  padding: 10px 25px;
  position: relative;
  transition: 0.3s all ease-in-out;
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown .dropdown-menu li a:before {
  display: none;
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown .dropdown-menu li a:after {
  left: 10px;
  top: 18px;
  width: 8px;
  height: 8px;
  content: "";
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  background-image: none;
  background-color: #4de3ef;
  transition: 0.3s all ease-in-out;
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown .dropdown-menu li a:hover {
  background-color: #6e24c8;
  color: #fff;
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown .dropdown-menu li a:hover:after {
  transform: scale(1);
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown .dropdown-menu li:last-child {
  border-bottom: none;
}

.str-main-header .str-main-menu-item .str-main-navigation .dropdown:hover .dropdown-menu {
  left: 0;
  opacity: 1;
  visibility: visible;
}

.str-sticky-menu {
  top: -40px;
  z-index: 9;
  position: fixed;
  padding: 10px 0px 15px;
  background-color: #010101;
  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
}

.str-main-header.str-sticky-menu .str-main-menu-item .str-main-navigation .dropdown .dropdown-menu {
  top: 55px;
}

/*---------------------------------------------------- */
/*banner area*/
/*----------------------------------------------------*/
.str-banner-slide-area {
  padding: 280px 0px 250px;
}

.str-banner-slide-area .owl-nav .owl-next,
.str-banner-slide-area .owl-nav .owl-prev {
  top: 50%;
  opacity: 0;
  width: 60px;
  height: 60px;
  font-size: 20px;
  cursor: pointer;
  line-height: 60px;
  text-align: center;
  position: absolute;
  border-radius: 100%;
  transform: translateY(-50%);
  background-color: #d5dbe6;
  transition: 0.5s all ease-in-out;
}

.str-banner-slide-area .owl-nav .owl-next:hover,
.str-banner-slide-area .owl-nav .owl-prev:hover {
  color: #fff;
  background-color: #6e24c8;
}

.str-banner-slide-area .owl-nav .owl-prev {
  left: -200px;
}

.str-banner-slide-area .owl-nav .owl-next {
  right: -200px;
}

.str-banner-slide-area .str-banner-content {
  max-width: 765px;
}

.str-banner-slide-area .str-banner-content h1 {
  color: #fff;
  font-size: 70px;
  font-weight: 700;
  position: relative;
  padding-bottom: 70px;
  opacity: 0;
  transform: scaleY(0);
  transform-origin: top;
  transition: all 1000ms ease;
}

.str-banner-slide-area .str-banner-content h1:after {
  left: 0px;
  bottom: 35px;
  height: 5px;
  width: 92px;
  content: "";
  position: absolute;
  background-image: linear-gradient(125deg, #6407c1 0%, #324add 49%, #008df9 100%);
}

.str-banner-slide-area .str-banner-content p {
  opacity: 0;
  color: #fff;
  font-size: 18px;
  max-width: 630px;
  padding-bottom: 40px;
  transform: scaleY(0);
  transform-origin: top;
  transition: all 1000ms ease;
}

.str-banner-slide-area .str-banner-content a {
  height: 60px;
  width: 180px;
  color: #fff;
  font-weight: 700;
  line-height: 60px;
  border-radius: 40px;
  opacity: 0;
  font-family: "Poppins";
  position: relative;
  transform: scaleY(0);
  transform-origin: top;
  overflow: hidden;
  z-index: 1;
  background-color: #028bf8;
  transition: all 1000ms ease;
}

.str-banner-slide-area .str-banner-content a i {
  width: 38px;
  color: #fff;
  height: 38px;
  margin-right: 5px;
  line-height: 38px;
  border-radius: 100%;
  display: inline-block;
  background-color: #6e24c8;
  transition: 0.3s all ease-in-out;
}

.str-banner-slide-area .str-banner-content a:after {
  content: "";
  height: 100%;
  top: -100%;
  left: 0;
  width: 100%;
  z-index: -1;
  position: absolute;
  background-color: #6e24c8;
  transition: 0.3s all ease-in-out;
}

.str-banner-slide-area .str-banner-content a:hover:after {
  top: 0;
}

.str-banner-slide-area .str-banner-content a:hover i {
  background-color: #028bf8;
}

.str-banner-slide-area .owl-item.active .str-banner-content h1 {
  opacity: 1;
  transform: scaleY(1);
  transition-delay: 300ms;
}

.str-banner-slide-area .owl-item.active .str-banner-content p {
  opacity: 1;
  transform: scaleY(1);
  transition-delay: 600ms;
}

.str-banner-slide-area .owl-item.active .str-banner-content a {
  opacity: 1;
  transform: scaleY(1);
  transition-delay: 900ms;
}

.str-banner-section:hover .owl-prev {
  left: -150px;
  opacity: 1;
}

.str-banner-section:hover .owl-next {
  right: -150px;
  opacity: 1;
}

/*---------------------------------------------------- */
/*feature area*/
/*----------------------------------------------------*/
.str-feature-section {
  padding: 50px 0px 110px;
}

.str-feature-section .str-feature-icon-text {
  z-index: 1;
  overflow: hidden;
  padding: 40px 0px;
  position: relative;
  border-radius: 130px;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 11px 54px 0px rgba(13, 0, 30, 0.2);
}

.str-feature-section .str-feature-icon-text .str-feature-icon {
  width: 125px;
  height: 125px;
  margin: 0 auto;
  line-height: 125px;
  margin-bottom: 18px;
  border-radius: 100%;
  transition: 0.3s all ease-in-out;
  background-image: linear-gradient(125deg, #efe7f9 0%, #eaedfc 49%, #e5f3fe 100%);
}

.str-feature-section .str-feature-icon-text .str-feature-icon img {
  width: initial;
  margin: 0 auto;
  display: initial;
  text-align: center;
}

.str-feature-section .str-feature-icon-text .str-feature-text h3 {
  color: #010101;
  font-size: 22px;
  font-weight: 700;
  position: relative;
  padding-bottom: 35px;
  transition: 0.3s all ease-in-out;
}

.str-feature-section .str-feature-icon-text .str-feature-text h3:before {
  left: 0;
  right: 0;
  content: "";
  width: 10px;
  bottom: 15px;
  height: 10px;
  margin: 0 auto;
  position: absolute;
  border-radius: 100%;
  background-color: #2786f1;
}

.str-feature-section .str-feature-icon-text .str-feature-text .str-feature-list {
  text-align: left;
  padding-left: 80px;
  padding-bottom: 10px;
}

.str-feature-section .str-feature-icon-text .str-feature-text .str-feature-list li {
  margin-bottom: 3px;
  position: relative;
  transition: 0.3s all ease-in-out;
}

.str-feature-section .str-feature-icon-text .str-feature-text .str-feature-list li:before {
  top: 3px;
  left: -20px;
  font-size: 12px;
  content: "";
  font-weight: 900;
  position: absolute;
  transition: 0.3s all ease-in-out;
  font-family: "Font Awesome 5 Free";
}

.str-feature-section .str-feature-icon-text:after, .str-feature-section .str-feature-icon-text:before {
  opacity: 0;
  width: 400px;
  z-index: -1;
  content: "";
  height: 360px;
  position: absolute;
  background-repeat: no-repeat;
  transition: 0.5s all ease-in-out;
}

.str-feature-section .str-feature-icon-text:after {
  bottom: 0;
  left: -130px;
  z-index: -1;
  background-image: url(../img/startup/shape/fs1.png);
  transition-delay: 0.3s;
}

.str-feature-section .str-feature-icon-text:before {
  z-index: -2;
  bottom: -70px;
  left: -120px;
  background-image: url(../img/startup/shape/fs2.png);
}

.str-feature-section .str-feature-icon-text:hover {
  background-color: #010004;
}

.str-feature-section .str-feature-icon-text:hover .str-feature-icon {
  background-color: #fff;
}

.str-feature-section .str-feature-icon-text:hover:after {
  opacity: 1;
  left: -90px;
}

.str-feature-section .str-feature-icon-text:hover:before {
  left: -95px;
  opacity: 1;
}

.str-feature-section .str-feature-icon-text:hover h3 {
  color: #fff;
}

.str-feature-section .str-feature-icon-text:hover h3:before {
  background-color: #fff;
}

.str-feature-section .str-feature-icon-text:hover li {
  color: #fff;
}

.str-feature-section .str-feature-icon-text:hover li:before {
  color: #fff;
}

.str-feature-section .str-feature-content {
  margin-top: 50px;
}

.str-feature-section .str-feature-content .owl-nav .owl-prev,
.str-feature-section .str-feature-content .owl-nav .owl-next {
  top: 50%;
  width: 40px;
  height: 40px;
  font-size: 18px;
  cursor: pointer;
  line-height: 40px;
  position: absolute;
  text-align: center;
  border-radius: 100%;
  background-color: #bfdafa;
  transform: translateY(-50%);
  transition: 0.3s all ease-in-out;
}

.str-feature-section .str-feature-content .owl-nav .owl-prev:hover,
.str-feature-section .str-feature-content .owl-nav .owl-next:hover {
  color: #fff;
  background-color: #6e24c8;
}

.str-feature-section .str-feature-content .owl-nav .owl-prev {
  left: -20px;
}

.str-feature-section .str-feature-content .owl-nav .owl-next {
  right: -20px;
}

.str-feature-section .str-feature-content .owl-stage-outer {
  overflow: visible;
}

.str-feature-section .str-feature-content .owl-stage-outer .owl-item {
  opacity: 0;
  transition: opacity 500ms;
}

.str-feature-section .str-feature-content .owl-stage-outer .owl-item.active {
  opacity: 1;
}

.str-feature-section .str-feature-content .owl-stage-outer:nth-child(1) .owl-item .str-feature-text h3:before {
  background-color: #2786f1;
}

.str-feature-section .str-feature-content .owl-stage-outer:nth-child(2) .owl-item .str-feature-text h3:before {
  background-color: #6e27ca;
}

.str-feature-section .str-feature-content .owl-stage-outer:nth-child(3) .owl-item .str-feature-text h3:before {
  background-color: #43d3e8;
}

.str-feature-section .str-feature-content .owl-stage-outer:nth-child(4) .owl-item .str-feature-text h3:before {
  background-color: #00b17f;
}

.str-feature-box {
  position: relative;
}

.str-feature-box .str-hover-icon {
  top: 10px;
  right: 5px;
  z-index: 3;
  width: 55px;
  color: #fff;
  height: 55px;
  line-height: 50px;
  position: absolute;
  border-radius: 100%;
  border: 3px solid #fff;
  transform: scale(0);
  transition: 0.3s all ease-in-out;
  background-image: linear-gradient(-30deg, #008df9 0%, #6006b3 100%);
}

.str-feature-box:hover .str-hover-icon {
  transform: scale(1);
}

/*---------------------------------------------------- */
/*abiut area*/
/*----------------------------------------------------*/
.str-about-section {
  background-color: #edf2f9;
  padding: 115px 0px 110px;
}

.str-about-section .str-aboutbg1 {
  top: -20%;
  left: 0;
}

.str-about-section .str-aboutbg2 {
  top: 20%;
  right: -5%;
}

.str-about-section .str-aboutbg3 {
  bottom: -10%;
  left: -10%;
}

.str-about-section .str-about-content .str-section-title {
  padding-bottom: 25px;
}

.str-about-section .str-about-content .str-about-textarea {
  color: #010101;
  font-size: 17px;
  font-weight: 500;
  line-height: 1.647;
  padding-bottom: 25px;
}

.str-about-section .str-about-content .str-about-list {
  padding: 25px 0px 22px;
}

.str-about-section .str-about-content .str-about-list li {
  color: #000000;
  position: relative;
  margin-bottom: 5px;
  padding-left: 25px;
}

.str-about-section .str-about-content .str-about-list li:before {
  top: 0;
  left: 0;
  color: #6e24c8;
  content: "";
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.str-about-section .str-about-content .str-about-img {
  display: flex;
  margin-left: 50px;
}

.str-about-section .str-about-content .str-about-img .str-about-shape {
  right: 0;
  top: 40%;
  position: absolute;
  transform: translateY(-50%);
}

.str-about-section .str-about-content .str-about-img .str-about-logo {
  top: 50%;
  left: 0;
  right: 0;
  position: absolute;
  text-align: center;
  transform: translateY(-50%);
}

.str-about-section .str-about-content .str-about-img .str-about-img-field {
  width: 50%;
}

.str-about-section .str-about-content .str-about-img .str-about-pic {
  margin: 5px;
  display: inline-block;
}

.str-about-section .str-about-content .str-progress-area {
  margin-top: 20px;
  max-width: 290px;
}

.str-about-section .str-about-content .str-progress-area h3 {
  color: #17161a;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 5px;
}

.str-about-section .str-about-content .str-progress-area .barfiller {
  width: 100%;
  height: 12px;
  background: #ebebeb;
  position: relative;
  margin-bottom: 16px;
}

.str-about-section .str-about-content .str-progress-area .barfiller .fill {
  display: block;
  position: relative;
  width: 0px;
  height: 100%;
  z-index: 1;
  background-image: linear-gradient(125deg, #6407c1 0%, #324add 49%, #008df9 100%) !important;
}

.str-about-section .str-about-content .str-progress-area .barfiller .tipWrap {
  display: none;
}

.str-about-section .str-about-content .str-progress-area .barfiller .tip {
  left: 0px;
  z-index: 2;
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 4px;
  background: #333;
  margin-top: -30px;
  position: absolute;
  font-family: "Poppins";
}

.str-about-section .str-about-content .str-progress-area .barfiller .tip:after {
  left: 9px;
  z-index: 9;
  content: "";
  bottom: -4px;
  border: solid;
  display: block;
  position: absolute;
  border-width: 6px 6px 0 6px;
  border-color: rgba(0, 0, 0, 0.8) transparent;
}

.str-about-section .str-about-feature-box {
  border-radius: 5px;
  padding: 25px 30px;
}

.str-about-section .str-about-feature-box .str-about-feature-icon {
  width: 85px;
  height: 85px;
  line-height: 85px;
  margin-right: 25px;
  border-radius: 100%;
}

.str-about-section .str-about-feature-box .str-about-feature-icon i {
  color: #fff;
  font-size: 45px;
}

.str-about-section .str-about-feature-box .str-about-feature-text h3 {
  color: #fff;
  font-size: 24px;
  margin-top: 14px;
  font-weight: 500;
  line-height: 1.333;
}

.str-about-section .str-about-feature-area {
  padding-top: 75px;
}

.str-about-section .str-about-feature-area .col-lg-4:nth-child(1) .str-about-feature-box {
  background-image: linear-gradient(90deg, #3a006c 1%, #510498 49%, #6807c3 100%);
}

.str-about-section .str-about-feature-area .col-lg-4:nth-child(1) .str-about-feature-icon {
  background-color: #683992;
}

.str-about-section .str-about-feature-area .col-lg-4:nth-child(2) .str-about-feature-box {
  background-image: linear-gradient(90deg, #6006b3 1%, #3317b4 49%, #0627b5 100%);
}

.str-about-section .str-about-feature-area .col-lg-4:nth-child(2) .str-about-feature-icon {
  background-color: #7a40c4;
}

.str-about-section .str-about-feature-area .col-lg-4:nth-child(3) .str-about-feature-box {
  background-image: linear-gradient(90deg, #1422b5 1%, #0c57da 49%, #048cff 100%);
}

.str-about-section .str-about-feature-area .col-lg-4:nth-child(3) .str-about-feature-icon {
  background-color: #4464d2;
}

.str-btn {
  font-size: 15px;
  font-weight: 700;
  position: relative;
  font-family: "Poppins";
}

.str-btn i {
  color: #6e24c8;
  margin-left: 5px;
  transition: 0.3s all ease-in-out;
}

.str-btn:before {
  bottom: -1px;
  content: "";
  height: 2px;
  width: 112px;
  position: absolute;
  background-color: #161616;
}

.str-btn:hover i {
  margin-left: 10px;
}

/*---------------------------------------------------- */
/*abiut area*/
/*----------------------------------------------------*/
@keyframes rotate-anim {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.str-work-process-section {
  padding: 105px 0px 115px;
}

.str-work-process-section .str-work-icon-text .str-work-icon {
  width: 155px;
  height: 155px;
  margin: 0 auto;
  position: relative;
  line-height: 155px;
  margin-bottom: 45px;
  border-radius: 100%;
  border: 2px solid #d7e7fd;
}

.str-work-process-section .str-work-icon-text .str-work-icon i {
  color: #444444;
  font-size: 45px;
}

.str-work-process-section .str-work-icon-text .str-work-icon .str-icon-border {
  top: -10px;
  left: -10px;
  width: 170px;
  height: 170px;
  position: absolute;
  border-radius: 100%;
  border: 15px solid #4ae0f0;
  border-right-color: transparent;
  animation-duration: 1500ms;
  animation: rotate-anim 3s infinite linear;
  animation-play-state: paused;
}

.str-work-process-section .str-work-icon-text .str-work-icon .str-icon-border .work-circle-shape {
  content: "";
  height: 35px;
  width: 35px;
  right: -25px;
  top: 50px;
  position: absolute;
  border-radius: 100%;
  background-image: linear-gradient(-30deg, #54e8ec 0%, #33cefa 100%);
  box-shadow: -1.045px 9.945px 16px 0px rgba(52, 207, 251, 0.4);
}

.str-work-process-section .str-work-icon-text .str-work-icon .icon-inner-shadow {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  border-radius: 100%;
  box-shadow: -1.045px 9.945px 16px 0px rgba(52, 207, 251, 0.4);
}

.str-work-process-section .str-work-icon-text:hover .str-icon-border {
  animation-play-state: running;
}

.str-work-process-section .str-work-icon-text .str-work-text h3 {
  color: #010101;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 15px;
}

.str-work-process-section .str-work-icon-text .str-work-text p {
  margin: 0 auto;
  max-width: 200px;
  padding-bottom: 5px;
}

.str-work-process-section .str-work-icon-text .str-work-text .str-btn {
  transition: 0.3s all ease-in-out;
}

.str-work-process-section .str-work-icon-text .str-work-text .str-btn i {
  color: #494949;
  margin-left: 0;
}

.str-work-process-section .str-work-icon-text .str-work-text .str-btn:hover {
  color: #6e24c8;
}

.str-work-process-section .str-work-icon-text .str-work-text .str-btn:hover i {
  margin-left: 5px;
  color: #6e24c8;
}

.str-work-process-section .str-work-icon-text .str-work-text .str-btn:before {
  width: 100%;
  height: 1px;
}

.str-work-process-section .str-work-process-content {
  padding-top: 75px;
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(2) .str-work-icon-text {
  position: relative;
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(2) .str-work-icon-text:before {
  top: 70px;
  left: -50px;
  content: "";
  width: 70px;
  height: 20px;
  position: absolute;
  background-repeat: no-repeat;
  background-image: url(../img/startup/shape/rv1.png);
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(2) .str-work-icon-text .icon-inner-shadow {
  display: none;
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(2) .str-work-icon-text .str-icon-border {
  border: 15px solid #0095ff;
  border-right-color: transparent;
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(2) .str-work-icon-text .work-circle-shape {
  box-shadow: -1.045px 9.945px 16px 0px rgba(0, 122, 255, 0.4);
  background-image: linear-gradient(-30deg, #00bdff 0%, #007aff 100%);
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(3) .str-work-icon-text {
  position: relative;
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(3) .str-work-icon-text:before {
  top: 70px;
  left: -50px;
  content: "";
  width: 70px;
  height: 20px;
  position: absolute;
  background-repeat: no-repeat;
  background-image: url(../img/startup/shape/rv2.png);
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(3) .str-work-icon-text .icon-inner-shadow {
  display: none;
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(3) .str-work-icon-text .str-icon-border {
  border: 15px solid #590fb8;
  border-right-color: transparent;
}

.str-work-process-section .str-work-process-content .col-lg-4:nth-child(3) .str-work-icon-text .work-circle-shape {
  box-shadow: none;
  background-image: linear-gradient(176deg, #008df9 0%, #324add 49%, #6407c1 100%);
}

/*---------------------------------------------------- */
/*abiut area*/
/*----------------------------------------------------*/
.str-portfolio-section:after {
  left: 0;
  top: 60px;
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: #edf2f9;
}

.str-portfolio-section .str-port-img-text {
  overflow: hidden;
  position: relative;
}

.str-portfolio-section .str-port-img-text:after {
  top: 85px;
  opacity: 0;
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  transition: 0.3s all ease-in-out;
  background: gradient(linear, left top, left bottom, from(#a0843b), color-stop(75%, #a0843b));
  background: linear-gradient(to bottom, rgba(21, 37, 65, 0) 0%, rgba(105, 36, 245, 0.79) 75%, rgba(36, 151, 245, 0.79) 100%);
}

.str-portfolio-section .str-port-img-text .str-port-text {
  left: 0;
  right: 0;
  z-index: 1;
  opacity: 0;
  bottom: 0px;
  position: absolute;
  transition: 0.3s all ease-in-out;
  transition-delay: 0.3s;
}

.str-portfolio-section .str-port-img-text .str-port-text h3 {
  color: #fff;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 5px;
}

.str-portfolio-section .str-port-img-text .str-port-text span {
  color: #fff;
}

.str-portfolio-section .str-port-img-text .str-port-text .str-port-popup {
  color: #fff;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  font-size: 14px;
  margin-top: 8px;
  line-height: 40px;
  border-radius: 100%;
  background-image: linear-gradient(-30deg, #00bdff 0%, #007aff 100%);
}

.str-portfolio-section .str-portfolio-area .owl-item.active.center .str-port-img-text:after {
  top: 0;
  opacity: 1;
}

.str-portfolio-section .str-portfolio-area .owl-item.active.center .str-port-img-text .str-port-text {
  opacity: 1;
  bottom: 20px;
}

/*---------------------------------------------------- */
/*abiut area*/
/*----------------------------------------------------*/
.str-testimonial_section {
  z-index: 1;
  position: relative;
  background-color: #edf2f9;
  padding: 105px 0px 100px 0px;
}

.str-testimonial_section .str-tst-vector {
  bottom: 0;
  right: 0;
  z-index: -1;
  position: absolute;
}

.str-testimonial_section .carousel_preview .str-section-title h2 {
  padding-bottom: 20px;
}

.str-testimonial_content {
  padding: 20px;
  display: inline-block;
}

.str-testimonial_content .str-testimonial_rating li {
  font-size: 14px;
  color: #f6b91c;
}

.str-testimonial_content .str-testimonial_text {
  z-index: 1;
  padding: 15px 0 25px;
  border-radius: 10px;
  border-bottom-left-radius: 0px;
  overflow: hidden;
}

.str-testimonial_content .str-testimonial_text h4 {
  color: #010101;
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 20px;
}

.str-testimonial_name_designation .str-testimonial_img {
  height: 95px;
  width: 95px;
  border-radius: 100%;
  margin-right: 18px;
  overflow: hidden;
  border: 5px solid #fff;
}

.str-testimonial_name_designation .str-testimonial_meta {
  margin-top: 25px;
  display: inline-block;
}

.str-testimonial_name_designation .str-testimonial_meta h4 {
  color: #010101;
  font-size: 20px;
  font-weight: 700;
  padding-bottom: 5px;
}

.str-testimonial_name_designation .str-testimonial_meta p {
  font-size: 14px;
  color: #444444;
}

.str-testimonial_slider .carousel-control-prev,
.str-testimonial_slider .carousel-control-next {
  top: auto;
  left: 190px;
  bottom: 40px;
  font-size: 18px;
  color: #373a5b;
  width: 50px;
  height: 50px;
  opacity: 1;
  line-height: 58px;
  text-align: center;
  border-radius: 50px;
  font-weight: 700;
  display: inline-block;
  margin: 0px 15px 0px 0px;
  background-color: #fff;
  transition: all 300ms ease;
  box-shadow: 0px 7px 7px 0px rgba(0, 15, 44, 0.18);
}

.str-testimonial_slider .carousel-control-prev:before,
.str-testimonial_slider .carousel-control-next:before {
  position: absolute;
  content: "";
  left: 0px;
  top: 0px;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50px;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
}

.str-testimonial_slider .carousel-control-prev:hover,
.str-testimonial_slider .carousel-control-next:hover {
  background-color: #000;
  color: #fff;
}

.str-testimonial_slider .carousel-control-prev {
  left: 120px;
}

.str-testimonial_slider .carousel-control-prev:before {
  content: "";
}

.carousel-indicators {
  position: static;
}

.str-testimonial_indicator {
  margin: 60px 20px 0 0;
  display: block;
  height: 510px;
  max-width: 100%;
  position: relative;
  background-repeat: no-repeat;
  background-image: url(../img/startup/testimonial/in-bg.png);
}

.str-testimonial_indicator .carousel-indicators li {
  position: absolute;
  width: 60px;
  height: 60px;
  text-align: center;
  color: #202120;
  border-radius: 50%;
  border: 5px solid #fff;
  overflow: hidden;
  transition: all 500ms ease;
  box-shadow: 0px 7px 6px 0px rgba(0, 15, 44, 0.19), inset -1px 0px 27px 0px rgba(0, 0, 0, 0.29);
}

.str-testimonial_indicator .carousel-indicators li img {
  display: block;
}

.str-testimonial_indicator .carousel-indicators li:nth-child(1) {
  bottom: 105px;
  right: 100px;
}

.str-testimonial_indicator .carousel-indicators li:nth-child(2) {
  left: 90px;
  bottom: 45px;
}

.str-testimonial_indicator .carousel-indicators li:nth-child(3) {
  top: 40%;
  left: 0px;
}

.str-testimonial_indicator .carousel-indicators li:nth-child(4) {
  top: 90px;
  left: 100px;
}

.str-testimonial_indicator .carousel-indicators li:nth-child(5) {
  top: 35%;
  left: 0;
  right: 0;
  margin: 0 auto;
}

.str-testimonial_indicator .carousel-indicators li:nth-child(6) {
  right: 120px;
  top: -30px;
}

.str-testimonial_indicator .active {
  transform: scale(1.5);
}

.str-testimonial_indicator-dot .carousel-indicators2 {
  right: 45px;
  bottom: 25px;
  position: absolute;
  justify-content: flex-end;
}

.str-testimonial_indicator-dot .carousel-indicators2 li {
  width: 25px;
  height: 25px;
  cursor: pointer;
  position: relative;
  display: inline-block;
}

.str-testimonial_indicator-dot .carousel-indicators2 li:before {
  content: "";
  width: 6px;
  height: 6px;
  left: 10px;
  top: 9px;
  position: absolute;
  background-color: #010101;
}

.str-testimonial_indicator-dot .carousel-indicators2 li:after {
  top: 0;
  left: 0;
  content: "";
  width: 25px;
  height: 25px;
  display: none;
  border-radius: 100%;
  border: 2px solid #010101;
  transition: 0.3s all ease-in-out;
}

.str-testimonial_indicator-dot .carousel-indicators2 li.active:after {
  display: block;
}

/*---------------------------------------------------- */
/*abiut area*/
/*----------------------------------------------------*/
.str-partner-section {
  padding: 50px 0px 65px;
}

.str-partner-area .owl-nav {
  display: none;
}

.str-partner-area .str-partner-img img {
  width: inherit;
  margin: 0 auto;
  filter: grayscale(1);
  transition: 0.3s all ease-in-out;
}

.str-partner-area .str-partner-img img:hover {
  filter: grayscale(0);
}

/*---------------------------------------------------- */
/*abiut area*/
/*----------------------------------------------------*/
.str-newslatter-content {
  z-index: 1;
  padding: 55px 0px;
}

.str-newslatter-content:before {
  top: 0;
  left: 0;
  z-index: -1;
  opacity: 0.9;
  width: 100%;
  content: "";
  height: 100%;
  position: absolute;
  background-color: #b8c7e3;
}

.str-newslatter-content .str-mail-icon {
  top: 55%;
  left: 30px;
  transform: translateY(-50%);
}

.str-newslatter-content .str-newslatter-text h2 {
  color: #010101;
  font-size: 36px;
  font-weight: 700;
  padding-bottom: 15px;
}

.str-newslatter-content .str-newslatter-text p {
  color: #010101;
  margin: 0 auto;
  font-size: 18px;
  max-width: 510px;
  padding-bottom: 25px;
}

.str-newslatter-content .str-newslatter-form {
  margin: 0 auto;
  max-width: 660px;
}

.str-newslatter-content .str-newslatter-form input {
  width: 100%;
  height: 60px;
  border: none;
  max-width: 495px;
  padding-left: 30px;
  border-radius: 5px;
  box-shadow: 0px 6px 40px 0px rgba(5, 41, 140, 0.25);
}

.str-newslatter-content .str-newslatter-form .nws-button {
  top: 0;
  right: 0;
  position: absolute;
}

.str-newslatter-content .str-newslatter-form .nws-button button {
  color: #fff;
  width: 150px;
  height: 60px;
  border: none;
  font-weight: 700;
  border-radius: 5px;
  font-family: "Poppins";
  background-color: transparent;
  background-image: linear-gradient(125deg, #6407c1 0%, #324add 49%, #008df9 100%) !important;
  background-size: 200% auto;
  transition: 0.3s all ease-in-out;
}

.str-newslatter-content .str-newslatter-form .nws-button button:hover {
  background-position: right center;
  transition: 0.3s all ease-in-out;
}

/*---------------------------------------------------- */
/*team area*/
/*----------------------------------------------------*/
.str-team-section {
  padding: 105px 0px 80px;
}

.str-team-section .str-section-title {
  padding: 0 0 30px 80px;
}

.str-team-section .str-team-title-text {
  max-width: 540px;
  padding-top: 60px;
}

.str-team-section .str-team-member-item .owl-stage-outer {
  padding-bottom: 30px;
}

.str-team-section .str-team-member-item .owl-nav .owl-next,
.str-team-section .str-team-member-item .owl-nav .owl-prev {
  top: 40%;
  border: 2px solid #1d1b4c;
}

.str-team-section .str-team-member-item .owl-nav .owl-next:hover,
.str-team-section .str-team-member-item .owl-nav .owl-prev:hover {
  background-color: #1d1b4c;
}

.str-team-img-text {
  padding-bottom: 45px;
}

.str-team-img-text .str-team-text {
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  height: 75px;
  max-width: 215px;
  overflow: hidden;
  padding: 12px 15px;
  position: absolute;
  background-color: #fff;
  transition: 0.3s all ease-in-out;
  box-shadow: -3.536px 3.536px 40px 0px rgba(29, 27, 76, 0.15);
}

.str-team-img-text .str-team-text h4 {
  color: #1d1b4c;
  font-size: 22px;
  font-weight: 700;
  transition: 0.3s all ease-in-out;
}

.str-team-img-text .str-team-text span {
  font-size: 15px;
  transition: 0.3s all ease-in-out;
  background-image: linear-gradient(125deg, #6407c1 0%, #324add 49%, #008df9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.str-team-img-text .str-team-text p {
  opacity: 0;
  color: #c1c1c6;
  font-size: 14px;
  visibility: hidden;
  padding: 5px 0px 10px;
  transform: translateY(10px);
  transition: 0.3s all ease-in;
  transition-delay: 0.3s;
}

.str-team-img-text .str-team-text .str-social-team {
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: 0.4s all ease-in;
  transition-delay: 0.5s;
}

.str-team-img-text .str-team-text .str-social-team a {
  color: #5e6877;
  margin: 0px 7px;
  transition: 0.3s all ease-in-out;
}

.str-team-img-text .str-team-text .str-social-team a:hover {
  color: #fff;
}

.str-team-img-text:hover .str-team-text {
  height: 200px;
  background-color: #010101;
}

.str-team-img-text:hover .str-team-text p,
.str-team-img-text:hover .str-team-text .str-social-team {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.str-team-img-text:hover .str-team-text span {
  color: #148eff;
  -webkit-text-fill-color: inherit;
}

.str-team-img-text:hover .str-team-text h4 {
  color: #fff;
}

/*---------------------------------------------------- */
/*get in touch area*/
/*----------------------------------------------------*/
.str-get-in-touch-section {
  padding-bottom: 110px;
}

.str-get-in-touch-section .str-get-touch-icon-text {
  z-index: 1;
  width: 100%;
  padding: 15px;
  position: relative;
  display: inline-block;
  border: 1px solid #e9eaf6;
}

.str-get-in-touch-section .str-get-touch-icon-text:after {
  top: 0;
  left: auto;
  right: 0;
  width: 0%;
  z-index: -1;
  content: "";
  height: 100%;
  position: absolute;
  transition: 0.5s all ease-in-out;
  background-image: linear-gradient(90deg, #1422b5 1%, #0c57da 49%, #048cff 100%);
}

.str-get-in-touch-section .str-get-touch-icon-text .str-get-touch-icon {
  height: 72px;
  width: 70px;
  line-height: 70px;
  margin-right: 14px;
  background-color: #fff;
}

.str-get-in-touch-section .str-get-touch-icon-text .str-get-touch-text {
  padding-top: 10px;
}

.str-get-in-touch-section .str-get-touch-icon-text .str-get-touch-text h3 {
  color: #010101;
  font-size: 20px;
  font-weight: 700;
  padding-bottom: 3px;
  transition: 0.3s all ease-in-out;
}

.str-get-in-touch-section .str-get-touch-icon-text .str-get-touch-text span {
  color: #444444;
  font-size: 15px;
  transition: 0.3s all ease-in-out;
}

.str-get-in-touch-section .str-get-touch-icon-text:hover:after {
  left: 0;
  right: auto;
  width: 100%;
}

.str-get-in-touch-section .str-get-touch-icon-text:hover .str-get-touch-text h3, .str-get-in-touch-section .str-get-touch-icon-text:hover .str-get-touch-text span {
  color: #fff;
}
#iframemap {
  width: 100%;
  border: none;
}
.str-maplocation {
  top: 40%;
  left: 30%;
  z-index: 1;
  cursor: pointer;
  position: absolute;
  box-shadow: 0px 3px 6px 0px #082680;
}

.str-maplocation:hover .str-location-iiner {
  opacity: 1;
  visibility: visible;
}

.str-location-iiner {
  z-index: 1;
  width: 215px;
  top: -130px;
  left: -80px;
  padding: 15px;
  width: 180px;
  opacity: 0;
  visibility: hidden;
  border-radius: 10px;
  position: absolute;
  background-color: #fff;
  transition: 0.4s all ease-in-out;
  box-shadow: 0px 0px 49px 0px rgba(2, 21, 78, 0.18);
}

.str-location-iiner:after {
  content: "";
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  position: absolute;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 15px solid #fff;
}

.str-location-iiner .str-location-counter {
  margin-right: 10px;
}

.str-location-iiner h4 {
  color: #010101;
  font-size: 18px;
  font-weight: 700;
  padding-bottom: 5px;
}

.str-location-iiner p {
  line-height: 1.2;
}

.str-feature-indicatior {
  width: 15px;
  height: 15px;
  border-radius: 100%;
  background-color: #082680;
}

.str-feature-indicatior .str-indicator-border1 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
  border-radius: inherit;
  color: inherit;
  box-shadow: 0 0 0 2px #082680;
  animation: borderpls 1.5s infinite cubic-bezier(0.4, 0, 1, 1) both;
}

/*---------------------------------------------------- */
/*get in touch area*/
/*----------------------------------------------------*/
.str-blog-section {
  padding: 90px 0px 100px;
}

.str-blog-section .str-team-title-text {
  padding-top: 40px;
}

.str-blog-section .str-blog-area {
  padding-top: 40px;
}

.str-blog-section .str-blog-img-text:before {
  left: 0;
  top: auto;
  z-index: 1;
  bottom: 0;
  width: 100%;
  content: "";
  opacity: 0.8;
  height: 0%;
  position: absolute;
  background-color: #000;
  transition: 0.4s all ease-in-out;
}

.str-blog-section .str-blog-img-text .str-blog-meta {
  top: 20px;
  right: 20px;
  width: 62px;
  z-index: 3;
  color: #fff;
  height: 62px;
  font-size: 20px;
  line-height: 1.1;
  font-weight: 700;
  border-radius: 4px;
  position: absolute;
  padding-top: 10px;
  font-family: "Poppins";
  background-image: linear-gradient(-30deg, #008df9 0%, #6006b3 100%);
  box-shadow: -1.045px 9.945px 16px 0px rgba(68, 46, 200, 0.4);
}

.str-blog-section .str-blog-img-text .str-blog-text {
  opacity: 0;
  left: 30px;
  bottom: 55px;
  z-index: 2;
  max-width: 280px;
  visibility: hidden;
  position: absolute;
  transition: 0.3s all ease-in-out;
  transition-delay: 0.3s;
}

.str-blog-section .str-blog-img-text .str-blog-text .str-post-meta {
  font-size: 14px;
  color: #dbe0e6;
}

.str-blog-section .str-blog-img-text .str-blog-text h3 {
  color: #fff;
  font-size: 22px;
  font-weight: 700;
  line-height: 1.455;
  padding: 2px 0px 20px;
}

.str-blog-section .str-blog-img-text .str-blog-text .str-read-more {
  color: #fff;
  font-size: 14px;
  font-weight: 700;
  padding-left: 70px;
  position: relative;
  font-family: "Poppins";
}

.str-blog-section .str-blog-img-text .str-blog-text .str-read-more:before {
  left: 0;
  top: -10px;
  width: 38px;
  content: "";
  height: 38px;
  position: absolute;
  transform: scale(0);
  background-color: #2786f1;
  transition: 0.5s all ease-in-out;
  transition-delay: 0.5s;
}

.str-blog-section .str-blog-img-text .str-blog-text .str-read-more:after {
  top: 10px;
  left: 20px;
  content: "";
  width: 0px;
  height: 1px;
  position: absolute;
  background-color: #fff;
  transition: 0.75s all ease-in-out;
  transition-delay: 0.7s;
}

.str-blog-section .str-blog-img-text:hover:before {
  top: 0;
  bottom: auto;
  height: 100%;
}

.str-blog-section .str-blog-img-text:hover .str-blog-text {
  opacity: 1;
  bottom: 35px;
  visibility: visible;
}

.str-blog-section .str-blog-img-text:hover .str-blog-text .str-read-more:before {
  transform: scale(1);
}

.str-blog-section .str-blog-img-text:hover .str-blog-text .str-read-more:after {
  width: 35px;
}

.str-blog-section .str-blog-area .owl-nav {
  display: none;
}

.str-blog-section .str-blog-area .owl-dots {
  margin-top: 30px;
  text-align: center;
}

.str-blog-section .str-blog-area .owl-dots .owl-dot {
  height: 6px;
  width: 6px;
  margin: 0px 8px;
  position: relative;
  display: inline-block;
  background-color: #010101;
}

.str-blog-section .str-blog-area .owl-dots .owl-dot:after {
  top: -10px;
  left: -10px;
  height: 26px;
  width: 26px;
  content: "";
  display: none;
  position: absolute;
  border-radius: 100%;
  border: 2px solid #010101;
}

.str-blog-section .str-blog-area .owl-dots .owl-dot.active:after {
  display: block;
}

/*---------------------------------------------------- */
/*Footer area*/
/*----------------------------------------------------*/
.str-footer-area .footer-content {
  padding-bottom: 80px;
}

.str-footer-area .footer-content .str-ft-about-widget p {
  line-height: 1.688;
  padding: 15px 0px 30px;
}

.str-footer-area .footer-content .str-ft-about-widget .ft-about-btn a {
  color: #fff;
  height: 35px;
  width: 100px;
  display: block;
  font-size: 15px;
  font-weight: 700;
  line-height: 35px;
  border-radius: 3px;
  text-align: center;
  font-family: "Poppins";
  box-shadow: -1.045px 9.945px 16px 0px rgba(68, 46, 200, 0.4);
  background-image: linear-gradient(-30deg, #008df9 0%, #6006b3 100%);
}

.str-footer-area .footer-content .str-footer-widget .str-widget-title {
  color: #080808;
  font-size: 24px;
  font-weight: 600;
  padding-bottom: 25px;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget p {
  font-size: 15px;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget form {
  position: relative;
  margin-top: 25px;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget input {
  width: 100%;
  border: none;
  height: 45px;
  padding: 0px 30px;
  background-color: #eeeeee;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget button {
  top: 0;
  right: 0;
  width: 60px;
  color: #fff;
  height: 100%;
  border: none;
  position: absolute;
  background-color: #010101;
  transition: 0.3s all ease-in-out;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget button:hover {
  background-color: #6e24c8;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget .str-social-footer {
  margin-top: 20px;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget .str-social-footer a {
  margin-right: 18px;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget .str-social-footer a:nth-child(1) {
  color: #16599b;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget .str-social-footer a:nth-child(2) {
  color: #03a9f4;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget .str-social-footer a:nth-child(3) {
  color: #ed679b;
}

.str-footer-area .footer-content .str-footer-widget .str-newslatter-widget .str-social-footer a:nth-child(4) {
  color: #87b7ff;
}

.str-footer-area .footer-content .str-footer-widget .str-office-widget {
  padding-left: 10px;
}

.str-footer-area .footer-content .str-footer-widget .str-office-widget .str-office-icon-text {
  padding-bottom: 10px;
}

.str-footer-area .footer-content .str-footer-widget .str-office-widget .str-office-icon-text .str-office-icon {
  margin-right: 12px;
}

.str-footer-area .footer-content .str-footer-widget .str-office-widget .str-office-icon-text .str-office-icon i {
  text-shadow: -1.045px 9.945px 16px rgba(68, 46, 200, 0.4);
  background-image: linear-gradient(125deg, #6407c1 0%, #324add 49%, #008df9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.str-footer-area .footer-content .str-footer-widget .str-office-widget .str-office-icon-text p {
  max-width: 140px;
  font-size: 14px;
  overflow: hidden;
  line-height: 1.714;
}

.str-footer-area .footer-content .str-footer-widget .str-office-widget .str-open-hour span {
  color: #010101;
  font-size: 14px;
  font-weight: 700;
}

.str-footer-area .footer-content .str-footer-widget .str-office-widget .str-open-hour p {
  font-size: 14px;
  padding-top: 5px;
  max-width: 150px;
}

.str-footer-area .footer-content .str-footer-widget .str-insta-widget .str-insta-feed {
  margin: 0 -5px;
}

.str-footer-area .footer-content .str-footer-widget .str-insta-widget a {
  float: left;
  margin: 5px;
  position: relative;
}

.str-footer-area .footer-content .str-footer-widget .str-insta-widget a:after {
  top: 0;
  left: 0;
  opacity: 0;
  content: "";
  height: 100%;
  width: 100%;
  position: absolute;
  background-color: #010101;
  transition: 0.3s all ease-in-out;
}

.str-footer-area .footer-content .str-footer-widget .str-insta-widget a:hover:after {
  opacity: 0.8;
}

.str-footer-area .str-copywright-text {
  color: #ffff;
  padding: 15px 0;
  font-size: 14px;
  font-family: "Poppins";
  background-color: #010101;
}

.str-footer-area .str-copywright-text a {
  color: #50a2ff;
}

/*---------------------------------------------------- */
/*mobile menu area*/
/*----------------------------------------------------*/
.str-mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 280px;
  overflow-y: scroll;
  background-color: #1c1c1c;
  padding: 40px 0px;
  transition: all 0.5s ease-in;
}

.str-mobile_menu_content .str-mobile-main-navigation {
  width: 100%;
}

.str-mobile_menu_content .str-mobile-main-navigation .navbar-nav {
  width: 100%;
}

.str-mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}

.str-mobile_menu_content .str-mobile-main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  padding: 13px 15px 13px 0px;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.1);
}

.str-mobile_menu_content .str-mobile-main-navigation .navbar-nav li a {
  color: #d5d5d5;
  padding: 0;
  width: 100%;
  display: block;
  font-size: 14px;
  font-weight: 400;
  padding: 5px 30px 0px;
  text-transform: uppercase;
}

.str-mobile_menu_content .m-brand-logo {
  width: 160px;
  margin: 0 auto;
  margin-bottom: 30px;
}

.str-mobile_menu_wrap.mobile_menu_on .str-mobile_menu_content {
  right: 0px;
  transition: all 0.7s ease-out;
}

.mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: 0%;
  height: 120vh;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.75);
  transition: all 0.5s ease-in-out;
}

.mobile_menu_overlay_on {
  overflow: hidden;
}

.str-mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  opacity: 1;
  visibility: visible;
}

.str-mobile_menu_button {
  position: absolute;
  display: none;
  right: 0;
  cursor: pointer;
  line-height: 40px;
  color: #5805a6;
  text-align: center;
  font-size: 30px;
  top: -44px;
  z-index: 5;
}

.str-mobile_menu .str-mobile-main-navigation .navbar-nav li a:after {
  display: none;
}

.str-mobile_menu .str-mobile-main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}

.str-mobile_menu .str-mobile_menu_content .str-mobile-main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
  width: 100%;
  background-color: transparent;
}

.str-mobile_menu .str-mobile_menu_content .str-mobile-main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 0 20px;
  line-height: 1;
  margin-bottom: 5px;
}

.str-mobile_menu .dropdown {
  position: relative;
}

.str-mobile_menu .dropdown .dropdown-btn {
  position: absolute;
  right: 15px;
  top: 10px;
  text-align: center;
  line-height: 35px;
  background: none;
  color: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 0px;
  padding: 0px;
  width: 36px;
  height: 35px;
}

.str-mobile_menu .str-mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  height: 20px;
  width: 20px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

.str-main-header.str-sticky-menu .str-mobile_menu_content {
  top: 15px;
}

/*---------------------------------------------------- */
/*responsive area*/
/*----------------------------------------------------*/
@media screen and (max-width: 1024px) {
  .str-feature-section .str-feature-icon-text .str-feature-text .str-feature-list {
    padding-left: 50px;
  }

  .str-about-section .str-about-content .str-about-img .str-about-shape {
    display: none;
  }

  .str-about-section .str-about-feature-box {
    padding: 25px 15px;
  }

  .str-about-section .str-about-feature-box .str-about-feature-text h3 {
    font-size: 22px;
  }
}
@media screen and (max-width: 991px) {
  .str-mobile_menu_button {
    display: block;
  }

  .str-main-header .str-main-menu-item .str-main-navigation {
    display: none;
  }

  .str-header-top {
    display: none;
  }

  .str-sticky-menu {
    top: -15px;
  }
  .str-about-content {
    max-width: 530px;
    margin: 0 auto;
  }
  .str-section-title {
    margin: inherit;
  }
  .str-about-section .str-about-content .str-about-img {
    margin-left: 0;
    padding-top: 30px;
  }

  .str-about-section .str-about-feature-box {
    padding: 25px 30px;
    margin-bottom: 30px;
  }

  .str-about-section .str-about-feature-box .str-about-feature-text h3 {
    font-size: 24px;
  }

  .str-work-icon-text {
    margin-bottom: 40px;
  }

  .str-work-process-section .str-work-process-content .col-lg-4:nth-child(2) .str-work-icon-text:before,
  .str-work-process-section .str-work-process-content .col-lg-4:nth-child(3) .str-work-icon-text:before {
    display: none;
  }

  .str-team-section .str-section-title {
    padding-left: 0;
  }

  .str-team-section .str-team-title-text {
    padding: 30px 0px;
  }

  .str-get-in-touch-section .str-get-touch-icon-text {
    margin-bottom: 30px;
  }

  .str-footer-widget {
    margin-bottom: 40px;
  }
}
@media screen and (max-width: 480px) {
  .str-banner-slide-area .str-banner-content h1 {
    font-size: 40px;
  }

  .str-banner-slide-area .str-banner-content a {
    height: 50px;
    width: 150px;
    line-height: 50px;
  }

  .str-banner-slide-area .str-banner-content a i {
    height: 30px;
    width: 30px;
    line-height: 30px;
  }

  .str-banner-slide-area {
    padding: 200px 0px 150px;
  }

  .str-section-title h2 {
    font-size: 32px;
  }

  .str-feature-box {
    max-width: 270px;
    margin: 0 auto;
  }

  .str-feature-section .str-feature-content .owl-nav .owl-prev,
  .str-feature-section .str-feature-content .owl-nav .owl-next,
  .str-portfolio-section .str-portfolio-area .owl-nav .owl-next,
  .str-team-section .str-team-member-item .owl-nav .owl-next,
  .str-portfolio-section .str-portfolio-area .owl-nav .owl-prev,
  .str-team-section .str-team-member-item .owl-nav .owl-prev {
    position: static;
    display: inline-block;
    margin: 0 5px;
    transform: translateY(0);
  }

  .str-feature-section .str-feature-content .owl-nav,
  .str-portfolio-section .str-portfolio-area .owl-nav,
  .str-team-section .str-team-member-item .owl-nav {
    text-align: center;
    margin-top: 40px;
  }

  .str-feature-section .str-feature-icon-text .str-feature-text .str-feature-list {
    padding-left: 80px;
  }

  .str-testimonial_indicator-dot .carousel-indicators2 {
    position: static;
    text-align: center;
    margin-top: 30px;
    justify-content: center;
  }

  .str-newslatter-content {
    padding: 40px 30px;
  }

  .str-team-img-text {
    margin: 0 auto;
    max-width: 270px;
  }

  .str-feature-section {
    padding: 25px 0px 60px;
  }

  .str-about-section {
    padding: 50px 0px;
  }

  .str-about-section .str-about-feature-area {
    padding-top: 50px;
  }

  .str-work-process-section {
    padding: 50px 0px;
  }

  .str-testimonial_section {
    padding: 50px 0px;
  }

  .str-team-section {
    padding: 50px 0px;
  }

  .str-team-section .str-team-title-text {
    padding-top: 15px;
  }

  .str-team-section .str-team-member-item .owl-nav {
    margin-top: 20px;
  }

  .str-get-in-touch-section {
    padding-bottom: 60px;
  }

  .str-blog-section {
    padding: 50px 0px;
  }

  .str-blog-section .str-team-title-text {
    padding-top: 20px;
  }
}
@media screen and (max-width: 420px) {
  .str-banner-slide-area .str-banner-content h1 {
    font-size: 34px;
  }

  .str-about-section .str-about-content .str-about-img {
    display: inline-block;
  }

  .str-about-section .str-about-content .str-about-img .str-about-img-field {
    width: 100%;
  }

  .str-about-section .str-about-content .str-about-img .str-about-logo {
    display: none;
  }

  .str-about-section .str-about-content .str-progress-area {
    max-width: 100%;
  }

  .str-section-title h2 {
    font-size: 28px;
  }

  .str-work-process-section .str-work-process-content {
    padding-top: 40px;
  }

  .str-testimonial_indicator {
    display: none;
  }

  .str-newslatter-content .str-newslatter-text h2 {
    font-size: 30px;
  }

  .str-newslatter-content .str-newslatter-text p {
    font-size: 16px;
  }

  .str-newslatter-content .str-newslatter-form .nws-button {
    position: static;
    margin-top: 30px;
    text-align: center;
  }

  .str-newslatter-content .str-mail-icon {
    display: none;
  }
}
@media screen and (max-width: 380px) {
  .str-banner-slide-area .str-banner-content h1 {
    font-size: 30px;
  }

  .str-section-title h2 {
    font-size: 26px;
  }

  .str-about-section .str-about-feature-box .str-about-feature-text h3 {
    font-size: 20px;
  }

  .str-newslatter-content .str-newslatter-text h2 {
    font-size: 26px;
  }
}
/*---------------------------------------------------- */
/*---------------------------------------------------- */
/*----------------------------------------------------*/
.dia-section-title span,
.dia-banner-section .dia-banner-content .dia-banner-tag {
  font-weight: 700;
  background-image: linear-gradient(81deg, #ff6600 0%, #ff9903 75%, #ffcb05 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dia-banner-section .dia-banner-content .dia-banner-btn .dia-abt-btn,
.dia-service-section .dia-service-btn .dia-service-more,
.dia-exp-section .dio-exp-text-area .dia-exp-btn,
.dia-port-more a,
.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .nws-button button {
  z-index: 1;
  overflow: hidden;
  position: relative;
}

.dia-banner-section .dia-banner-content .dia-banner-btn .dia-abt-btn:before,
.dia-service-section .dia-service-btn .dia-service-more:before,
.dia-exp-section .dio-exp-text-area .dia-exp-btn:before,
.dia-port-more a:before,
.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .nws-button button:before {
  left: 0;
  width: 100%;
  content: "";
  z-index: -1;
  height: 100%;
  bottom: -100%;
  position: absolute;
  transition: 0.5s all ease-in-out;
}

.dia-banner-section .dia-banner-content .dia-banner-btn .dia-abt-btn:hover:before,
.dia-service-section .dia-service-btn .dia-service-more:hover:before,
.dia-exp-section .dio-exp-text-area .dia-exp-btn:hover:before,
.dia-port-more a:hover:before,
.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .nws-button button:hover:before {
  bottom: 0;
}
@keyframes borderpls {
  0% {
    box-shadow: 0 0 0 0 #082680;
  }
  70% {
    box-shadow: 0 0 0 30px #082680;
    opacity: 0;
  }
  to {
    box-shadow: 0 0 0 0 #082680;
    opacity: 0;
  }
}
@keyframes borderpls2 {
  0% {
    box-shadow: 0 0 0 0 #082680;
  }
  70% {
    box-shadow: 0 0 0 10px #082680;
    opacity: 0;
  }
  to {
    box-shadow: 0 0 0 0 #082680;
    opacity: 0;
  }
}
@keyframes fadeFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeFromUp {
  animation-name: fadeFromUp;
}

.fadeFromRight {
  animation-name: fadeFromRight;
}

.fadeFromLeft {
  animation-name: fadeFromLeft;
}

/*global area*/
/*----------------------------------------------------*/
.dia-home {
  margin: 0;
  padding: 0;
  color: #585476;
  font-size: 16px;
  overflow-x: hidden;
  line-height: 1.667;
  font-family: "Roboto";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

div#preloader {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999;
  width: 100%;
  height: 100%;
  overflow: visible;
  background-color: #fff;
  background: #fff url("../img/pre.svg") no-repeat center center;
}

.decoration-wrapper {
  overflow: hidden;
  position: relative;
}
@keyframes zooming {
  0% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.05, 1.05);
  }
  100% {
    transform: scale(1, 1);
  }
}
.zooming {
  animation: zooming 18s infinite both;
}

.dia-headline h1,
.dia-headline h2,
.dia-headline h3,
.dia-headline h4,
.dia-headline h5,
.dia-headline h6 {
  margin: 0;
  font-family: "Poppins";
}

.dia-section-title span {
  font-size: 16px;
}

.dia-section-title h2 {
  color: #282350;
  font-size: 40px;
  font-weight: 700;
  padding-top: 5px;
  line-height: 1.375;
}

.dia-scrollup {
  width: 55px;
  right: 20px;
  z-index: 5;
  height: 55px;
  bottom: 20px;
  display: none;
  position: fixed;
  border-radius: 100%;
  line-height: 55px;
  background-color: #ff6700;
}

.dia-scrollup i {
  color: #fff;
}

/*---------------------------------------------------- */
/*Header area*/
/*----------------------------------------------------*/
.dia-main-header {
  z-index: 5;
  width: 100%;
  padding-top: 15px;
  position: absolute;
}

.dia-main-header .dia-logo {
  margin-top: 15px;
}

.dia-main-header .dropdown {
  position: relative;
}

.dia-main-header .dropdown:after {
  top: -2px;
  right: -14px;
  content: "+";
  font-size: 18px;
  font-weight: 700;
  position: absolute;
  transition: 0.3s all ease-in-out;
}

.dia-main-header .dropdown .dropdown-menu {
  top: 65px;
  left: 0;
  opacity: 0;
  z-index: 2;
  margin: 0px;
  padding: 0px;
  height: auto;
  width: 200px;
  border: none;
  display: block;
  border-radius: 0;
  overflow: hidden;
  visibility: hidden;
  position: absolute;
  border-radius: 10px;
  background-color: #fff;
  transition: all 0.4s ease-in-out;
  border-bottom: 2px solid #ff6626;
  box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
}

.dia-main-header .dropdown .dropdown-menu li {
  width: 100%;
  margin-left: 0;
  border-bottom: 1px solid #e5e5e5;
}

.dia-main-header .dropdown .dropdown-menu li a {
  width: 100%;
  color: #343434;
  display: block;
  font-size: 14px;
  padding: 10px 25px;
  position: relative;
  transition: 0.3s all ease-in-out;
}

.dia-main-header .dropdown .dropdown-menu li a:before {
  display: none;
}

.dia-main-header .dropdown .dropdown-menu li a:after {
  left: 10px;
  top: 18px;
  width: 8px;
  height: 8px;
  content: "";
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}

.dia-main-header .dropdown .dropdown-menu li a:hover {
  background-color: #ff8533;
  color: #fff;
}

.dia-main-header .dropdown .dropdown-menu li a:hover:after {
  transform: scale(1);
}

.dia-main-header .dropdown .dropdown-menu li:last-child {
  border-bottom: none;
}

.dia-main-header .dropdown:hover .dropdown-menu {
  top: 45px;
  opacity: 1;
  visibility: visible;
}

.dia-main-header .navbar-nav {
  display: inherit;
}

.dia-main-header .dia-main-navigation {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 20px 40px;
}

.dia-main-header .dia-main-navigation li {
  margin: 0px 32px;
}

.dia-main-header .dia-main-navigation li a {
  color: #282350;
  font-weight: 700;
  padding-bottom: 20px;
  display: inline;
  position: relative;
  font-family: "Poppins";
}

.dia-main-header .dia-main-navigation li a:before {
  content: "";
  left: 0;
  right: 0;
  bottom: 0;
  height: 5px;
  width: 0%;
  margin: 0 auto;
  position: absolute;
  transition: 0.5s all ease-in-out;
  background-image: linear-gradient(81deg, #ff6600 0%, #ff9903 75%, #ffcb05 100%);
}

.dia-main-header .dia-main-navigation li a:hover:before, 
.dia-main-header .dia-main-navigation li a.active:before {
  width: 100%;
}

.dia-sticky-menu {
  top: 20px;
  position: fixed;
  background-color: #fff;
  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
}

.dia-main-header.dia-sticky-menu {
  top: 0px;
  z-index: 9;
  z-index: 20;
  padding-top: 0;
  box-shadow: 0 0 20px -10px rgba(0, 0, 0, 0.8);
}

.dia-main-header.dia-sticky-menu .dropdown:hover .dropdown-menu {
  top: 45px;
}

/*---------------------------------------------------- */
/*Mobile Menu area*/
/*----------------------------------------------------*/
.dia-mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 280px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 40px 0px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s ease-in;
}

.dia-mobile_menu_content .dia-mobile-main-navigation {
  width: 100%;
}

.dia-mobile_menu_content .dia-mobile-main-navigation .navbar-nav {
  width: 100%;
}

.dia-mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}

.dia-mobile_menu_content .dia-mobile-main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid #dcdcdc;
}

.dia-mobile_menu_content .dia-mobile-main-navigation .navbar-nav li:first-child {
  border-top: 1px solid #dcdcdc;
}

.dia-mobile_menu_content .dia-mobile-main-navigation .navbar-nav li a {
  color: #000;
  padding: 0;
  width: 100%;
  display: block;
  font-size: 14px;
  font-weight: 400;
  padding: 5px 30px;
  text-transform: uppercase;
}

.dia-mobile_menu_content .m-brand-logo {
  width: 160px;
  margin: 0 auto;
  margin-bottom: 30px;
}

.dia-mobile_menu_wrap.mobile_menu_on .dia-mobile_menu_content {
  right: 0px;
  transition: all 0.7s ease-out;
}

.mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: 0%;
  height: 120vh;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.5s ease-in-out;
}

.mobile_menu_overlay_on {
  overflow: hidden;
}

.dia-mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  opacity: 1;
  visibility: visible;
}

.dia-mobile_menu_button {
  position: absolute;
  display: none;
  right: 0;
  cursor: pointer;
  line-height: 40px;
  color: #ff8833;
  text-align: center;
  font-size: 30px;
  top: -35px;
  z-index: 5;
}

.dia-mobile_menu .dia-mobile-main-navigation .navbar-nav li a:after {
  display: none;
}

.dia-mobile_menu .dia-mobile-main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}

.dia-mobile_menu .dia-mobile_menu_content .dia-mobile-main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
  width: 100%;
  border-top: 1px solid #dcdcdc;
}

.dia-mobile_menu .dia-mobile_menu_content .dia-mobile-main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 0 20px;
  line-height: 1;
}

.dia-mobile_menu .dropdown {
  position: relative;
}

.dia-mobile_menu .dropdown .dropdown-btn {
  position: absolute;
  top: 0px;
  right: 0;
  height: 30px;
  padding: 5px 10px;
}

.dia-mobile_menu .dropdown .dropdown-btn:before {
  content: "";
  position: absolute;
  height: 100%;
  width: 1px;
  top: 0;
  left: 0;
  background-color: #dcdcdc;
}

.dia-mobile_menu .dia-mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

/*---------------------------------------------------- */
/*Banner area*/
/*----------------------------------------------------*/
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.dia-banner-section {
  padding-bottom: 10px;
}

.dia-banner-section .banner-side-img {
  right: 0;
  top: -10px;
  z-index: -1;
}

.dia-banner-section .banner-side-img.banner-img1 {
  opacity: 0;
  right: -300px;
  transition-delay: 200ms;
  transition: all 1200ms ease;
}

.dia-banner-section .banner-side-img.banner-img2 {
  opacity: 0;
  right: -300px;
  transition-delay: 500ms;
  transition: all 1500ms ease;
}

.dia-banner-section .banner-side-img.banner-img1.view-on {
  right: 0;
  opacity: 1;
  transition-delay: 200ms;
}

.dia-banner-section .banner-side-img.banner-img2.view-on {
  right: 0;
  opacity: 1;
  transition-delay: 500ms;
}

.dia-banner-section .dia-banner-content {
  max-width: 460px;
  padding: 275px 0px 185px;
}

.dia-banner-section .dia-banner-content .dia-banner-tag {
  font-size: 18px;
}

.dia-banner-section .dia-banner-content h1 {
  color: #282350;
  font-size: 70px;
  font-weight: 700;
  line-height: 1.071;
  padding: 15px 0px 25px;
}

.dia-banner-section .dia-banner-content p {
  font-size: 22px;
  color: #403b64;
}

.dia-banner-section .dia-banner-content .dia-banner-btn {
  margin-top: 65px;
}

.dia-banner-section .dia-banner-content .dia-banner-btn a {
  width: 100%;
  display: block;
  margin-right: 20px;
}

.dia-banner-section .dia-banner-content .dia-banner-btn .dia-play-btn {
  width: 70px;
  height: 70px;
  line-height: 70px;
  margin-right: 20px;
  border-radius: 100%;
  transition: 0.3s all ease-in-out;
  background-color: #5409d6;
}

.dia-banner-section .dia-banner-content .dia-banner-btn .dia-play-btn i {
  color: #fff;
}

.dia-banner-section .dia-banner-content .dia-banner-btn .dia-play-btn:hover {
  background-image: none;
  background-color: #ff6700;
}

.dia-banner-section .dia-banner-content .dia-banner-btn .dia-abt-btn {
  color: #fff;
  height: 70px;
  width: 230px;
  font-weight: 700;
  line-height: 70px;
  border-radius: 40px;
  font-family: "Poppins";
  background-color: #ff6700;
}

.dia-banner-section .dia-banner-content .dia-banner-btn .dia-abt-btn:before {
  background-color: #5409d6;
}

.dia-banner-section .cd-headline.clip span {
  display: inline-block;
  padding-bottom: 10px;
}

.dia-banner-section .cd-headline.clip .cd-words-wrapper {
  display: inline-block;
  position: relative;
  text-align: left;
  vertical-align: top;
}

.dia-banner-section .cd-headline.clip .cd-words-wrapper::after {
  content: "";
  top: 10px;
  right: 0;
  width: 2px;
  height: 65px;
  position: absolute;
  background-color: #282350;
}

.dia-banner-section .cd-headline.clip b {
  opacity: 0;
}

.dia-banner-section .cd-headline.clip b.is-visible {
  opacity: 1;
}

.dia-banner-section .cd-words-wrapper {
  display: inline-block;
  position: relative;
  text-align: left;
}

.dia-banner-section .cd-words-wrapper b {
  display: inline-block;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  white-space: nowrap;
}

.dia-banner-section .cd-words-wrapper b.is-visible {
  opacity: 1;
  position: relative;
}

.dia-banner-section .no-js .cd-words-wrapper b {
  opacity: 0;
}

.dia-banner-section .banner-side-shape1,
.dia-banner-section .banner-side-shape2 {
  bottom: 135px;
}

.dia-banner-section .banner-shape1 {
  left: 50%;
  bottom: 15%;
  z-index: -2;
  animation-name: spin;
  animation-duration: 15000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

.dia-banner-section .banner-shape2 {
  top: 30%;
  left: 12%;
}

/*---------------------------------------------------- */
/*service area*/
/*----------------------------------------------------*/
.dia-service-section {
  padding: 90px 0px 115px;
}

.dia-service-section .dia-service-img {
  margin-top: 55px;
}

.dia-service-section .dia-service-img .dia-service-shape2 {
  top: 0;
  z-index: -2;
}

.dia-service-section .dia-service-img .dia-service-shape1 {
  left: 0;
  top: 10px;
  z-index: -1;
  animation-name: spin;
  animation-duration: 20000ms;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

.dia-service-section .dia-service-text {
  padding-left: 60px;
}

.dia-service-section .dia-service-details {
  margin: 35px 0px 5px;
}

.dia-service-section .dia-service-details .dia-service-item {
  width: 50%;
  float: left;
  margin-bottom: 30px;
}

.dia-service-section .dia-service-details .dia-service-item h3 {
  color: #282350;
  font-size: 20px;
  font-weight: 700;
  padding-bottom: 16px;
}

.dia-service-section .dia-service-details .dia-service-item li {
  padding-left: 15px;
  position: relative;
  padding-bottom: 5px;
}

.dia-service-section .dia-service-details .dia-service-item li:before {
  top: 0;
  left: 0;
  content: "+";
  position: absolute;
}

.dia-service-section .dia-service-details .dia-service-item:nth-child(even) {
  padding-left: 35px;
}

.dia-service-section .dia-service-btn .dia-service-more {
  height: 50px;
  width: 225px;
  margin-top: 10px;
  line-height: 50px;
  border-radius: 40px;
  background-color: #390ed2;
}

.dia-service-section .dia-service-btn .dia-service-more a {
  color: #fff;
  width: 100%;
  display: block;
  font-size: 15px;
  font-weight: 700;
  font-family: "Poppins";
}

.dia-service-section .dia-service-btn .dia-service-more:before {
  background-color: #ff6700;
}

.dia-service-section .dia-service-review {
  max-width: 215px;
}

.dia-service-section .dia-service-review .dia-service-rate ul {
  margin-bottom: 8px;
}

.dia-service-section .dia-service-review .dia-service-rate li {
  font-size: 14px;
  color: #f7c903;
}

.dia-service-section .dia-service-review .dia-service-rate-text {
  font-size: 14px;
}

.dia-service-section .dia-service-review .dia-service-rate-text span {
  color: #282350;
  font-weight: 700;
}

/*---------------------------------------------------- */
/*Fun fact area*/
/*----------------------------------------------------*/
.dia-fun-fact-section .dia-fun-fact-title {
  margin: 0 auto;
  max-width: 945px;
}

.dia-fun-fact-section .dia-fun-fact-title h2 {
  color: #282350;
  font-size: 36px;
  font-weight: 700;
  line-height: 1.528;
}

.dia-fun-fact-section .dia-fun-fact-counter {
  padding-top: 68px;
}

.dia-fun-fact-section .dia-fun-fact-counter .col-lg-4:nth-child(1) .dia-fun-fact-item .fun-fact-tag {
  color: #09d32f;
}

.dia-fun-fact-section .dia-fun-fact-counter .col-lg-4:nth-child(2) .dia-fun-fact-item .fun-fact-tag {
  color: #ff7b01;
}

.dia-fun-fact-section .dia-fun-fact-counter .col-lg-4:nth-child(3) .dia-fun-fact-item .fun-fact-tag {
  color: #390ed2;
}

.dia-fun-fact-section .dia-fun-fact-item span {
  font-weight: 700;
  font-family: "Poppins";
}

.dia-fun-fact-section .dia-fun-fact-item .fun-fact-number {
  line-height: 1;
  padding: 18px 0px;
  justify-content: center;
}

.dia-fun-fact-section .dia-fun-fact-item .fun-fact-number h3 {
  color: #282350;
  font-size: 90px;
  font-weight: 500;
}

.dia-fun-fact-section .dia-fun-fact-item .fun-fact-number span {
  color: #282350;
  font-size: 90px;
  line-height: 1.2;
  font-weight: 500;
  font-family: "Poppins";
}

.dia-fun-fact-section .dia-fun-fact-item p {
  margin: 0 auto;
  max-width: 325px;
}

/*---------------------------------------------------- */
/*About area*/
/*----------------------------------------------------*/
.dia-about-section {
  padding-top: 130px;
}

.dia-about-section .dia-about-title-text {
  padding-top: 25px;
}

.dia-about-content .dia-about-title-text {
  max-width: 455px;
}

.dia-about-content .dia-about-text {
  font-size: 18px;
  padding: 25px 0px 75px;
}

.dia-about-content .dia-about-list li {
  width: 50%;
  float: left;
  color: #282350;
  font-weight: 700;
  padding-left: 30px;
  position: relative;
  margin-bottom: 18px;
}

.dia-about-content .dia-about-list li:after {
  left: 0;
  top: -3px;
  color: #25d98f;
  font-size: 20px;
  content: "";
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.dia-about-content .dia-about-img {
  z-index: 1;
  padding-left: 40px;
}

.dia-about-content .dia-about-img .ab-shape1 {
  top: 30px;
  left: -10px;
  z-index: -1;
}

.dia-about-content .dia-about-img .ab-shape2 {
  right: 0;
  bottom: 0;
  z-index: -1;
}

.dia-exp-section {
  padding: 115px 0px 0px;
}

.dia-exp-section .dia-exp-img .ab-shape1 {
  top: 60px;
  left: -5px;
  z-index: -1;
}

.dia-exp-section .dia-exp-img .ab-shape2 {
  bottom: 20px;
  right: 20px;
  z-index: -1;
}

.dia-exp-section .dio-exp-text-area {
  max-width: 520px;
}

.dia-exp-section .dio-exp-text-area .dia-about-text {
  margin-top: 30px;
}

.dia-exp-section .dio-exp-text-area .dia-exp-btn {
  color: #fff;
  height: 48px;
  width: 155px;
  font-size: 15px;
  font-weight: 700;
  line-height: 48px;
  border-radius: 40px;
  font-family: "Poppins";
  background-color: #390ed2;
}

.dia-exp-section .dio-exp-text-area .dia-exp-btn a {
  width: 100%;
  display: block;
}

.dia-exp-section .dio-exp-text-area .dia-exp-btn:before {
  background-color: #ff6700;
}

.dia-exp-section .skill-progress-bar {
  padding: 20px 0px 20px;
}

.dia-exp-section .skill-progress-bar .skill-set-percent {
  margin-bottom: 30px;
}

.dia-exp-section .skill-progress-bar .skill-set-percent h4 {
  font-size: 16px;
  font-weight: 700;
  font-family: "Roboto";
}

.dia-exp-section .skill-progress-bar .skill-set-percent .progress {
  height: 6px;
  border-radius: 0;
  overflow: visible;
  position: relative;
  background-color: #dededf;
}

.dia-exp-section .skill-progress-bar .skill-set-percent .progress span {
  right: 0;
  top: -30px;
  color: #282350;
  font-size: 16px;
  font-weight: 700;
  position: absolute;
}

.dia-exp-section .skill-progress-bar .skill-set-percent .progress-bar {
  width: 0;
  float: left;
  height: 100%;
  position: relative;
  background-color: #ff6700;
  transition: 1s all ease-in-out;
}

/*---------------------------------------------------- */
/*Portfolio area*/
/*----------------------------------------------------*/
.dia-portfolio-section {
  padding-top: 100px;
}
.dia-portfolio-img-text {
  transition: 0.4s all ease-in-out;
}

.dia-portfolio-img-text .dia-portfolio-img {
  overflow: hidden;
  position: relative;
}

.dia-portfolio-img-text .dia-portfolio-img img {
  transition: 1s all ease-in-out;
}

.dia-portfolio-img-text .dia-portfolio-img:after {
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  content: "";
  height: 100%;
  position: absolute;
  background-color: #000;
  transition: all 700ms ease-out 0s;
  transform: rotateX(180deg) scale(0.5, 0.5);
}

.dia-portfolio-img-text .dia-portfolio-text {
  margin-top: 18px;
  padding-bottom: 28px;
}

.dia-portfolio-img-text .dia-portfolio-text h3 {
  color: #282350;
  font-size: 24px;
  display: inline-block;
  font-weight: 700;
  position: relative;
  padding-bottom: 12px;
}

.dia-portfolio-img-text .dia-portfolio-text h3:before {
  left: 0;
  right: 0;
  width: 0%;
  content: "";
  bottom: 8px;
  height: 2px;
  margin: 0 auto;
  position: absolute;
  background-color: #282350;
  transition: 0.4s all ease-in-out;
}

.dia-portfolio-img-text .dia-portfolio-text span {
  display: block;
  font-weight: 700;
}

.dia-portfolio-img-text:hover {
  box-shadow: -1.308px 24.966px 22px 0px rgba(0, 0, 0, 0.11);
}

.dia-portfolio-img-text:hover .dia-portfolio-img img {
  transform: scale(1.4);
}

.dia-portfolio-img-text:hover .dia-portfolio-img:after {
  opacity: 0.8;
  transform: rotateX(0deg) scale(1, 1);
}

.dia-portfolio-img-text:hover h3:before {
  width: 100%;
}

.dia-portfolio-slide-area {
  margin: 45px 0px 20px;
}

.dia-portfolio-slide-area .owl-stage-outer {
  padding-bottom: 35px;
}

.dia-portfolio-slide-area .owl-nav .owl-prev,
.dia-portfolio-slide-area .owl-nav .owl-next {
  top: 40%;
  z-index: 1;
  width: 60px;
  height: 60px;
  cursor: pointer;
  line-height: 60px;
  position: absolute;
  text-align: center;
  border-radius: 100%;
  background-color: #ffffff;
  transform: translateY(-50%);
  transition: 0.4s all ease-in-out;
  box-shadow: 0px 0px 22px 0px rgba(0, 0, 0, 0.28);
}

.dia-portfolio-slide-area .owl-nav .owl-prev:before,
.dia-portfolio-slide-area .owl-nav .owl-next:before {
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  content: "";
  z-index: -1;
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  transition: 0.4s all ease-in-out;
  background-image: linear-gradient(-81deg, #6806d9 0%, #4f0ad6 57%, #360ed2 100%);
}

.dia-portfolio-slide-area .owl-nav .owl-prev:hover,
.dia-portfolio-slide-area .owl-nav .owl-next:hover {
  color: #fff;
}

.dia-portfolio-slide-area .owl-nav .owl-prev:hover:before,
.dia-portfolio-slide-area .owl-nav .owl-next:hover:before {
  transform: scale(1);
}

.dia-portfolio-slide-area .owl-nav .owl-prev {
  left: 12px;
}

.dia-portfolio-slide-area .owl-nav .owl-next {
  right: 12px;
}

.dia-port-more a {
  color: #fff;
  height: 58px;
  width: 200px;
  margin: 0 auto;
  font-size: 15px;
  font-weight: 700;
  line-height: 58px;
  border-radius: 40px;
  font-family: "Poppins";
  background-color: #ff6700;
}

.dia-port-more a:before {
  background-color: #390ed2;
}

/*---------------------------------------------------- */
/*Team area*/
/*----------------------------------------------------*/
.dia-team-section {
  padding-top: 110px;
}

.dia-team-section .dia-section-title {
  margin: 0 auto;
  max-width: 530px;
}

.dia-team-section .dia-section-title h2 {
  padding-bottom: 10px;
}

.dia-team-section .dia-team-content {
  padding-top: 55px;
}

.dia-team-section .dia-team-pic-text .dia-team-img:after {
  width: 236px;
  height: 236px;
  content: "";
  position: absolute;
  top: 0;
  left: 100px;
  opacity: 0;
  visibility: hidden;
  border: double 2px transparent;
  border-radius: 100%;
  background-image: linear-gradient(white, white), radial-gradient(circle at top left, #ff6600, #ff9903, #ffcb05);
  background-origin: border-box;
  transition: 0.3s all ease-in-out;
  background-clip: content-box, border-box;
}

.dia-team-section .dia-team-pic-text .dia-team-img .team-mem-img-ei {
  z-index: 1;
  width: 236px;
  height: 236px;
  margin: 0 auto;
  border-radius: 100%;
  overflow: hidden;
  background-color: #eff0f7;
}

.dia-team-section .dia-team-pic-text .dia-team-img .team-mem-img-ei .mshape-bg {
  width: 100%;
  z-index: -1;
  height: 100%;
  position: absolute;
}

.dia-team-section .dia-team-pic-text .dia-team-img .team-mem-img-ei .shape-bg1 {
  top: 90px;
  left: -75px;
}

.dia-team-section .dia-team-pic-text .dia-team-img .team-mem-img-ei .shape-bg2 {
  top: 100px;
  z-index: -2;
  right: -68px;
}

.dia-team-section .dia-team-pic-text .dia-team-social {
  top: 47px;
  z-index: 1;
  right: 50px;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  transition-delay: 0.2s;
  transition: 0.5s all ease-in-out;
}

.dia-team-section .dia-team-pic-text .dia-team-social a {
  width: 35px;
  height: 35px;
  display: block;
  text-align: center;
  line-height: 37px;
  margin-bottom: 12px;
  border-radius: 100%;
  position: relative;
  z-index: 1;
  background-color: #fff;
  box-shadow: 0px 0px 18px 0px rgba(43, 1, 68, 0.23);
}

.dia-team-section .dia-team-pic-text .dia-team-social a:after {
  position: absolute;
  content: "";
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  width: 100%;
  z-index: -1;
  transition: 0.3s all ease-in-out;
  border-radius: 100%;
  background-image: linear-gradient(81deg, #ff6600 0%, #ff9903 75%, #ffcb05 100%);
}

.dia-team-section .dia-team-pic-text .dia-team-social a:nth-child(1) {
  color: #a80202;
}

.dia-team-section .dia-team-pic-text .dia-team-social a:nth-child(2) {
  transform: translateX(15px);
  color: #03a9f4;
}

.dia-team-section .dia-team-pic-text .dia-team-social a:nth-child(3) {
  transform: translateX(9px);
  margin-bottom: 5px;
  color: #a80202;
}

.dia-team-section .dia-team-pic-text .dia-team-social a:nth-child(4) {
  color: #0c6eff;
  transform: translateX(-20px);
}

.dia-team-section .dia-team-pic-text .dia-team-social a:hover {
  color: #fff;
}

.dia-team-section .dia-team-pic-text .dia-team-social a:hover:after {
  opacity: 1;
  visibility: visible;
}

.dia-team-section .dia-team-pic-text .dia-team-text {
  margin-top: 20px;
}

.dia-team-section .dia-team-pic-text .dia-team-text h3 {
  color: #282350;
  font-size: 22px;
  font-weight: 700;
}

.dia-team-section .dia-team-pic-text .dia-team-text p {
  color: #383838;
  font-size: 15px;
}

.dia-team-section .dia-team-pic-text:hover .dia-team-img:after {
  opacity: 1;
  left: 102px;
  visibility: visible;
}

.dia-team-section .dia-team-pic-text:hover .dia-team-social {
  opacity: 1;
  right: 25px;
  visibility: visible;
}

.dia-team-section .dia-team-content {
  z-index: 1;
  margin: 0 auto;
  max-width: 1100px;
}

.dia-team-section .dia-team-content .owl-nav .owl-prev,
.dia-team-section .dia-team-content .owl-nav .owl-next {
  top: 50%;
  width: 52px;
  height: 52px;
  cursor: pointer;
  line-height: 52px;
  text-align: center;
  position: absolute;
  border-radius: 100%;
  display: inline-block;
  background-color: #fff;
  transform: translateY(-50%);
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 7px 18px 0px rgba(16, 31, 60, 0.25);
}

.dia-team-section .dia-team-content .owl-nav .owl-prev:hover,
.dia-team-section .dia-team-content .owl-nav .owl-next:hover {
  color: #fff;
  background-color: #282350;
}

.dia-team-section .dia-team-content .owl-nav .owl-prev {
  left: -40px;
}

.dia-team-section .dia-team-content .owl-nav .owl-next {
  right: -50px;
}

.dia-team-section .dia-team-content .owl-item.active.center .dia-team-img:after {
  left: 102px;
  opacity: 1;
  visibility: visible;
}

.dia-team-section .dia-team-content .owl-item.active.center .dia-team-social {
  opacity: 1;
  right: 25px;
  visibility: visible;
}

/*---------------------------------------------------- */
/*Testimonial area*/
/*----------------------------------------------------*/
.dia-testimonial-section {
  overflow: visible;
  padding: 135px 0 100px;
}

.dia-testimonial-section .tst-side-shape {
  right: 0;
  top: -100px;
}

.dia-testimonial-section .dia-section-title {
  margin: 0 auto;
  max-width: 500px;
}

.dia-testimonial-section .dia-testimonial_slider-area {
  margin: 0 auto;
  margin-top: 55px;
  max-width: 725px;
  background-color: #fff;
  box-shadow: -1.046px 11.954px 22px 0px rgba(0, 0, 0, 0.08);
}

.dia-testimonial-section .dia-testimonial_slider-area:after {
  top: -50px;
  left: 25px;
  font-size: 60px;
  content: "";
  font-weight: 900;
  position: absolute;
  color: rgba(203, 203, 226, 0.75);
  font-family: "Font Awesome 5 Free";
}

.dia-testimonial-section .dia-testimonial_slider-area .test-shape1 {
  top: -100px;
  z-index: -1;
  left: -175px;
}

.dia-testimonial-section .dia-testimonial_slider-area .test-shape2 {
  z-index: -1;
  right: -175px;
  bottom: -100px;
}

.dia-testimonial-section .dia-testimonial_indicator .carousel-indicators li {
  width: 90px;
  height: 90px;
  display: inherit;
  text-align: center;
  color: #202120;
  border-radius: 50%;
  overflow: hidden;
  transition: all 500ms ease;
}

.dia-testimonial-section .carousel-inner .carousel-item {
  padding: 80px 90px 70px;
}

.dia-testimonial-section .carousel-inner .dia-testimonial_rating li {
  color: #ffba00;
}

.dia-testimonial-section .carousel-inner .dia-testimonial_text {
  margin: 18px 0px 28px;
  font-size: 22px;
  line-height: 1.636;
}

.dia-testimonial-section .carousel-inner .dia-testimonial_meta h4 {
  color: #282350;
  font-size: 32px;
  font-weight: 700;
  padding-bottom: 3px;
}

.dia-testimonial-section .carousel-inner .dia-testimonial_meta p {
  font-size: 18px;
}

.dia-testimonial-section .carousel-indicators {
  top: 70px;
  margin: 0;
  left: auto;
  right: -45px;
  position: absolute;
  display: inherit;
}

.dia-testimonial-section .carousel-indicators li {
  text-indent: 0;
  margin-bottom: 15px;
  transform: scale(0.9);
  border: 3px solid rgba(255, 255, 255, 0);
}

.dia-testimonial-section .carousel-indicators li img {
  border-radius: 100%;
}

.dia-testimonial-section .carousel-indicators li.active {
  padding: 5px;
  transform: scale(1);
  border: 3px solid #5409d6;
}

.dia-testimonial_indicator-dot .carousel-indicators2 {
  top: 60%;
  right: 45px;
  left: -35px;
  bottom: 25px;
  padding-left: 0;
  margin-bottom: 0;
  position: absolute;
  transform: translateY(-50%);
}

.dia-testimonial_indicator-dot .carousel-indicators2 li {
  width: 15px;
  height: 15px;
  display: block;
  cursor: pointer;
  position: relative;
  border-radius: 100%;
  margin-bottom: 12px;
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}

.dia-testimonial_indicator-dot .carousel-indicators2 li:after {
  top: 3px;
  left: 3px;
  content: "";
  width: 60%;
  height: 60%;
  border-radius: 100%;
  position: absolute;
  transform: scale(0);
  transition: 0.3s all ease-in-out;
  background: linear-gradient(81deg, #ff6600 0%, #ff9903 75%, #ffcb05 100%);
}

.dia-testimonial_indicator-dot .carousel-indicators2 li.active {
  transform: scale(1.2);
}

.dia-testimonial_indicator-dot .carousel-indicators2 li.active:after {
  transform: scale(1);
}

/*---------------------------------------------------- */
/*blog area*/
/*----------------------------------------------------*/
.dia-blog-section {
  padding: 100px 0px 60px;
}

.dia-blog-section .dia-section-title {
  max-width: 450px;
}

.dia-blog-section .dia-section-title h2 {
  padding-bottom: 10px;
}

.dia-blog-section .dia-blog-content {
  padding-top: 50px;
}

.dia-blog-section .dia-blog-img-text {
  max-width: 370px;
}

.dia-blog-section .dia-blog-img-text .dia-blog-text {
  margin-top: 25px;
}

.dia-blog-section .dia-blog-img-text .dia-blog-text h3 {
  color: #282350;
  font-size: 24px;
  font-weight: 700;
  padding: 15px 0px 30px;
  transition: 0.3s all ease-in-out;
}

.dia-blog-section .dia-blog-img-text .dia-blog-text h3:hover {
  color: #ff6700;
}

.dia-blog-section .dia-blog-img-text .dia-author-area {
  width: 60%;
}

.dia-blog-section .dia-blog-img-text .dia-author-area .dia-athur-img {
  width: 43px;
  height: 43px;
  overflow: hidden;
  margin-right: 15px;
  border-radius: 100%;
}

.dia-blog-section .dia-blog-img-text .dia-author-area .dia-author-name {
  margin-top: 10px;
}

.dia-blog-section .dia-blog-img-text .dia-author-area .dia-author-name span {
  font-size: 18px;
}

.dia-blog-section .dia-blog-img-text .dia-author-area .dia-author-name span a {
  color: #6606d9;
  font-weight: 700;
}

.dia-blog-section .dia-blog-img-text .dia-date-meta {
  margin-top: 10px;
  font-size: 18px;
}

/*---------------------------------------------------- */
/*Newslatter area*/
/*----------------------------------------------------*/
.dia-newslatter-section {
  z-index: 1;
  padding: 65px 0px 80px;
  background-color: #f6ebe6;
}

.dia-newslatter-section .newslatter-shape {
  z-index: -1;
}

.dia-newslatter-section .n-shape1 {
  top: 0;
  left: 0;
}

.dia-newslatter-section .n-shape4 {
  left: 10%;
  bottom: 30px;
}

.dia-newslatter-section .n-shape5 {
  top: 50%;
  right: 12%;
}

.dia-newslatter-section .n-shape2 {
  top: 10px;
  left: 14%;
}

.dia-newslatter-section .n-shape3 {
  top: 45px;
  right: 13%;
}

.dia-newslatter-section .n-shape6 {
  right: 30px;
  bottom: -80px;
}

.dia-newslatter-section .dia-newslatter-content {
  border-radius: 30px;
  background-color: #fff;
  padding: 55px 55px 65px 75px;
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-text h3 {
  color: #282350;
  font-size: 30px;
  font-weight: 700;
  padding-bottom: 12px;
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-text p {
  font-size: 17px;
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form {
  margin-top: 10px;
  padding-left: 25px;
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form input {
  width: 100%;
  border: none;
  height: 50px;
  max-width: 330px;
  padding-left: 20px;
  border-radius: 5px;
  background-color: #ebeff3;
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .nws-button {
  top: 0;
  right: 0;
  position: absolute;
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .nws-button button {
  color: #fff;
  height: 50px;
  width: 140px;
  border: none;
  font-weight: 700;
  border-radius: 5px;
  font-family: "Poppins";
  background-image: linear-gradient(-100deg, #ff6600 0%, #ff9903 75%, #ffcb05 100%);
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .nws-button button:before {
  background-color: #390ed2;
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .dia-newslatter-checkbox {
  margin-top: 15px;
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .dia-newslatter-checkbox input {
  height: inherit;
  width: inherit;
  padding-left: 0;
  margin-right: 5px;
  max-width: inherit;
}

.dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .dia-newslatter-checkbox label {
  font-size: 14px;
  margin-bottom: 0;
}

/*---------------------------------------------------- */
/*Newslatter area*/
/*----------------------------------------------------*/
.dia-footer-section {
  padding-top: 140px;
}

.dia-footer-widget .dia-widget-title {
  font-size: 16px;
  color: #05071f;
  font-weight: 700;
  padding-bottom: 20px;
}

.dia-footer-widget .dia-footer-logo {
  margin-bottom: 18px;
}

.dia-footer-widget p {
  max-width: 220px;
  line-height: 1.875;
}

.dia-footer-widget .dia-payment-mathod {
  margin-top: 30px;
}

.dia-footer-widget ul li {
  width: 50%;
  float: left;
  font-size: 14px;
  padding-left: 15px;
  margin-bottom: 15px;
  font-family: "Poppins";
}

.dia-footer-widget ul li a {
  color: #05071f;
  font-weight: 500;
  position: relative;
  transition: 0.3s all ease-in-out;
}

.dia-footer-widget ul li a:before {
  left: -15px;
  top: 0px;
  content: "";
  font-size: 12px;
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}

.dia-footer-widget ul li a:after {
  content: "";
  position: absolute;
  height: 1px;
  width: 0%;
  left: 0;
  bottom: 0;
  background-color: #0072fd;
  transition: 0.3s all ease-in-out;
}

.dia-footer-widget ul li a:hover {
  color: #0072fd;
  margin-left: 10px;
}

.dia-footer-widget ul li a:hover:after {
  width: 100%;
}

.dia-footer-widget h4 {
  width: 45%;
  float: left;
  font-size: 14px;
  font-weight: 700;
  padding-left: 24px;
  margin-right: 15px;
  position: relative;
  display: inline-block;
}

.dia-footer-widget h4 i {
  left: 0;
  top: 2px;
  color: #a80202;
  font-size: 16px;
  margin-right: 5px;
  position: absolute;
}

.dia-footer-widget h4 span {
  font-family: "Roboto";
  font-weight: 400;
  margin-top: 10px;
  display: block;
  color: #818181;
}

.dia-footer-widget .download-btn {
  width: 100%;
  margin-top: 20px;
  display: inline-block;
}

.dia-footer-widget .download-btn a {
  margin-right: 10px;
}

.dia-footer-widget .dia-footer-social {
  margin-top: 15px;
}

.dia-footer-widget .dia-footer-social a {
  height: 30px;
  width: 30px;
  border-radius: 100%;
  background-color: #fff;
  line-height: 30px;
  text-align: center;
  margin-right: 5px;
  display: inline-block;
  transition: 0.3s all ease-in-out;
  box-shadow: 0px 0px 9px 0px rgba(15, 54, 131, 0.07);
}

.dia-footer-widget .dia-footer-social a:nth-child(1) {
  color: #16599b;
}

.dia-footer-widget .dia-footer-social a:nth-child(2) {
  color: #03a9f4;
}

.dia-footer-widget .dia-footer-social a:nth-child(3) {
  color: #a80202;
}

.dia-footer-widget .dia-footer-social a:nth-child(4) {
  color: #0d6fff;
}

.dia-footer-widget .dia-footer-social a:hover {
  transform: translateY(5px);
}

.dia-footer-copyright {
  color: #373a5b;
  font-size: 14px;
  margin-top: 25px;
  font-weight: 500;
  font-family: "Poppins";
}

.dia-footer-copyright .dia-footer-copyright-content {
  padding: 25px 0px 15px;
  border-top: 2px solid #dee2ef;
}

.dia-footer-copyright .dia-copyright-menu {
  float: right;
}

.dia-footer-copyright .dia-copyright-menu a {
  margin-left: 35px;
}
@keyframes left-right-move {
  0% {
    transform: translateX(-100px);
  }
  50% {
    transform: translateX(-10px);
  }
  100% {
    transform: translateX(-100px);
  }
}
.dia-footer-shape3 {
  left: 0;
  right: 0;
  opacity: 1;
  z-index: -1;
  top: 100px;
  margin: 0 auto;
  text-align: center;
  animation-duration: 25s;
  animation-name: left-right-move;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

/*---------------------------------------------------- */
/*responsive area*/
/*----------------------------------------------------*/
@media only screen and (min-width: 1367px) and (max-width: 1700px) {
  .dia-banner-section .banner-side-img.banner-img1,
  .dia-banner-section .banner-side-img.banner-img2 {
    right: -450px;
  }

  .dia-banner-section .banner-side-img.banner-img1.view-on,
  .dia-banner-section .banner-side-img.banner-img2.view-on {
    right: -200px;
  }

  .dia-banner-section .banner-side-shape1,
  .dia-banner-section .banner-side-shape2 {
    left: -150px;
  }
}
@media screen and (max-width: 1440px) {
  .dia-banner-section .banner-side-img.banner-img1,
  .dia-banner-section .banner-side-img.banner-img2 {
    right: -600px;
  }

  .dia-banner-section .banner-side-img.banner-img1.view-on,
  .dia-banner-section .banner-side-img.banner-img2.view-on {
    right: -250px;
  }

  .dia-banner-section .banner-shape2 {
    left: 20px;
  }

  .dia-newslatter-section .n-shape4 {
    left: 10px;
  }

  .dia-banner-section .banner-side-shape1,
  .dia-banner-section .banner-side-shape2 {
    left: -150px;
  }
}
@media screen and (max-width: 1280px) {
  .dia-banner-section .banner-side-shape1,
  .dia-banner-section .banner-side-shape2,
  .dia-testimonial-section .tst-side-shape {
    display: none;
  }

  .dia-banner-section .banner-shape2 {
    z-index: -1;
  }
}
@media screen and (max-width: 1024px) {
  .dia-banner-section .banner-side-img.banner-img1,
  .dia-banner-section .banner-side-img.banner-img2 {
    right: -800px;
  }

  .dia-banner-section .banner-side-img.banner-img1.view-on,
  .dia-banner-section .banner-side-img.banner-img2.view-on {
    right: -500px;
  }

  .dia-service-section .dia-service-details .dia-service-item:nth-child(even) {
    padding-left: 0px;
  }

  .dia-service-section .dia-service-btn .dia-service-more {
    width: 185px;
  }

  .dia-team-section .dia-team-content .owl-nav {
    text-align: center;
    margin-top: 30px;
    padding-bottom: 20px;
  }
  .dia-testimonial-section {
    overflow: hidden;
  }
  .dia-team-section .dia-team-content .owl-nav .owl-prev,
  .dia-team-section .dia-team-content .owl-nav .owl-next {
    position: static;
    margin: 0px 8px;
    transform: translateY(0);
  }

  .dia-team-section .dia-team-content .owl-item.active.center .dia-team-img:after,
  .dia-team-section .dia-team-pic-text:hover .dia-team-img:after {
    left: 70px;
  }

  .dia-footer-widget .download-btn a {
    margin-right: 0px;
  }
}
@media screen and (max-width: 991px) {
  .dia-banner-section .dia-banner-content {
    max-width: 100%;
    text-align: center;
  }

  .dia-banner-section .dia-banner-content .dia-banner-btn {
    justify-content: center;
  }

  .dia-testimonial-section .dia-testimonial_slider-area .test-shape1,
  .dia-testimonial-section .dia-testimonial_slider-area .test-shape2 {
    display: none;
  }

  .dia-main-navigation {
    display: none;
  }

  .dia-banner-section .banner-side-img,
  .dia-about-content .dia-about-img .ab-shape1,
  .dia-about-content .dia-about-img .ab-shape2,
  .dia-exp-section .dia-exp-img .ab-shape1,
  .dia-exp-section .dia-exp-img .ab-shape2 {
    display: none;
  }

  .dia-main-header.dia-sticky-menu {
    padding-top: 15px;
  }

  .dia-main-header .dia-logo {
    margin-top: 0;
  }

  .dia-sticky-menu {
    padding: 15px 0px;
  }

  .dia-mobile_menu_button {
    display: block;
  }

  .dia-service-section .dia-service-img {
    margin-top: 0;
    margin-bottom: 35px;
  }

  .dia-fun-fact-section .dia-fun-fact-item .fun-fact-number span,
  .dia-fun-fact-section .dia-fun-fact-item .fun-fact-number h3 {
    font-size: 80px;
  }

  .dia-about-content .dia-about-img {
    margin-left: 0;
    margin-top: 30px;
  }

  .dia-exp-img {
    margin-bottom: 30px;
  }

  .dia-team-section .dia-team-content .owl-item.active.center .dia-team-img:after,
  .dia-team-section .dia-team-pic-text:hover .dia-team-img:after {
    left: 105px;
  }

  .dia-testimonial-section {
    overflow: hidden;
  }

  .dia-testimonial-section .carousel-indicators {
    display: none;
  }

  .dia-banner-section .dia-banner-content {
    padding: 150px 0px 0px;
  }

  .dia-testimonial_indicator-dot .carousel-indicators2 {
    position: static;
    text-align: center;
  }

  .dia-testimonial_indicator-dot .carousel-indicators2 li {
    display: inline-block;
    background-color: #390ed2;
  }

  .dia-blog-img-text {
    margin-bottom: 40px;
  }

  .dia-newslatter-section .dia-newslatter-content .dia-newslatter-form {
    padding-left: 0;
    margin-top: 20px;
  }

  .dia-newslatter-section .dia-newslatter-content .dia-newslatter-form input {
    max-width: 100%;
  }

  .dia-footer-widget {
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 580px) {
  .dia-banner-section .dia-banner-content h1 {
    font-size: 50px;
  }

  .dia-banner-section .cd-headline.clip .cd-words-wrapper::after {
    height: 35px;
  }

  .dia-banner-section .dia-banner-content p {
    font-size: 20px;
  }

  .dia-banner-section .dia-banner-content .dia-banner-btn {
    margin-top: 30px;
  }

  .dia-team-pic-text {
    max-width: 370px;
    margin: 0 auto;
  }
}
@media screen and (max-width: 480px) {
  .dia-banner-section .dia-banner-content .dia-banner-btn .dia-play-btn {
    width: 50px;
    height: 50px;
    line-height: 50px;
    margin-right: 10px;
  }

  .dia-banner-section .dia-banner-content .dia-banner-btn .dia-abt-btn {
    height: 50px;
    width: 200px;
    line-height: 50px;
  }

  .dia-banner-section .dia-banner-content h1 {
    font-size: 40px;
  }

  .dia-banner-section .dia-banner-content {
    padding: 130px 0px 0px;
  }

  .dia-banner-section {
    padding-bottom: 50px;
  }

  .dia-service-section .dia-service-text {
    padding-left: 0;
  }

  .dia-service-section .dia-service-details {
    margin: 25px 0px 0px;
  }

  .dia-service-section {
    padding-bottom: 55px;
  }

  .dia-section-title h2 {
    font-size: 30px;
  }

  .dia-fun-fact-section {
    padding-bottom: 10px;
  }

  .dia-fun-fact-section .dia-fun-fact-title h2 {
    font-size: 30px;
  }

  .dia-fun-fact-section .dia-fun-fact-counter {
    padding-top: 30px;
  }

  .dia-fun-fact-section .dia-fun-fact-item .fun-fact-number {
    padding: 5px 0px;
  }

  .dia-fun-fact-section .dia-fun-fact-item .fun-fact-number span,
  .dia-fun-fact-section .dia-fun-fact-item .fun-fact-number h3 {
    font-size: 45px;
  }

  .dia-fun-fact-item {
    margin-bottom: 40px;
  }

  .dia-about-section {
    padding-top: 20px;
  }

  .dia-service-section .dia-service-details .dia-service-item {
    width: 100%;
  }

  .dia-about-content .dia-about-list li {
    width: 100%;
  }

  .dia-about-content .dia-about-text {
    font-size: 16px;
    padding: 20px 0px 30px;
  }

  .dia-about-content .dia-about-img {
    padding-left: 0;
  }

  .dia-exp-section {
    padding: 60px 0px 60px;
  }

  .dia-exp-section .skill-progress-bar {
    padding: 20px 0px 10px;
  }

  .dia-team-section {
    padding-top: 60px;
  }

  .dia-team-section {
    padding-top: 60px;
  }

  .dia-testimonial-section {
    padding: 45px 0 60px;
  }

  .dia-testimonial-section .carousel-inner .carousel-item {
    padding: 50px 30px 60px;
  }

  .dia-testimonial-section .carousel-inner .dia-testimonial_text {
    font-size: 18px;
  }

  .dia-testimonial-section .carousel-inner .dia-testimonial_meta h4 {
    font-size: 26px;
  }

  .dia-testimonial-section .dia-testimonial_slider-area:after {
    top: -15px;
    font-size: 40px;
  }

  .dia-blog-section .dia-blog-img-text .dia-blog-text h3 {
    font-size: 22px;
    padding: 10px 0px 20px;
  }

  .dia-blog-section {
    padding-top: 0px;
    padding-bottom: 20px;
  }

  .dia-newslatter-section .dia-newslatter-content .dia-newslatter-text h3 {
    font-size: 28px;
  }

  .dia-newslatter-section .dia-newslatter-content {
    padding: 45px 25px 45px 25px;
  }

  .dia-footer-section {
    padding-top: 50px;
  }

  .dia-footer-widget .dia-payment-mathod {
    margin-top: 20px;
  }

  .dia-footer-copyright .dia-copyright-menu {
    float: none;
  }

  .dia-footer-copyright .dia-copyright-menu a {
    margin-left: 0;
    margin: 0px 8px;
  }

  .dia-footer-copyright-content {
    text-align: center;
  }
  .dia-portfolio-section {
    padding-top: 30px;
  }
  .dia-service-section {
    padding-top: 0;
  }
}
@media screen and (max-width: 420px) {
  .dia-service-section .dia-service-review,
  .dia-service-section .dia-service-btn .dia-service-more {
    float: none !important;
  }

  .dia-service-section .dia-service-btn .dia-service-more {
    margin-bottom: 15px;
  }

  .dia-service-section .dia-service-review .dia-service-rate ul {
    margin-bottom: 0;
  }

  .dia-fun-fact-section .dia-fun-fact-title h2 {
    font-size: 28px;
  }

  .dia-fun-fact-section {
    margin-bottom: 0;
  }

  .dia-port-more a {
    height: 45px;
    width: 150px;
    line-height: 45px;
  }
}
@media screen and (max-width: 380px) {
  .dia-banner-section .dia-banner-content h1 {
    font-size: 36px;
    padding: 10px 0px 15px;
  }

  .dia-banner-section .cd-headline.clip .cd-words-wrapper::after {
    height: 25px;
  }

  .dia-banner-section .dia-banner-content p {
    font-size: 18px;
  }

  .dia-section-title h2 {
    font-size: 26px;
  }

  .dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .nws-button button {
    width: 100px;
  }

  .dia-newslatter-section .dia-newslatter-content .dia-newslatter-form input {
    padding-left: 10px;
  }

  .dia-newslatter-section .dia-newslatter-content .dia-newslatter-form input::placeholder {
    font-size: 14px;
  }

  .dia-team-section .dia-team-content .owl-item.active.center .dia-team-img:after,
  .dia-team-section .dia-team-pic-text:hover .dia-team-img:after {
    left: 85px;
  }
}
@media screen and (max-width: 360px) {
  .dia-team-section .dia-team-content .owl-item.active.center .dia-team-img:after,
  .dia-team-section .dia-team-pic-text:hover .dia-team-img:after {
    left: 70px;
  }

  .dia-blog-section .dia-blog-img-text .dia-author-area .dia-author-name span,
  .dia-blog-section .dia-blog-img-text .dia-date-meta {
    font-size: 14px;
  }
}
@media screen and (max-width: 320px) {
  .dia-banner-section .dia-banner-content h1 {
    font-size: 30px;
  }

  .dia-team-section .dia-team-content .owl-item.active.center .dia-team-img:after,
  .dia-team-section .dia-team-pic-text:hover .dia-team-img:after {
    left: 35px;
  }

  .dia-newslatter-section .dia-newslatter-content .dia-newslatter-form .dia-newslatter-checkbox label {
    font-size: 12px;
  }
}
/*** Start Of blog Section ***/
.saasio-breadcurmb-section {
  background-color: #6c2b95;
  padding: 260px 0px 150px
}
.breadcurmb-title h2 {
  margin: 0;
  color: #fff;
  font-weight: 700;
  font-size: 36px;
  font-family: 'Poppins';
  padding-bottom: 20px;
}
.saasio-page-breadcurmb li a  {
  color: #fff;
  margin: 0px 10px;
  position: relative;
}
.saasio-page-breadcurmb li a:after {
 top: 2px;
 right: -17px;
 content: '\f054';
 font-weight: 900;
 font-size: 12px;
 position: absolute;
 font-family: 'Font Awesome 5 Free';
}
.saasio-page-breadcurmb li:last-child a::after {
  display: none;
}
.news-feed-section {
  padding: 100px 0px;
}
.news-feed-section .blog-feed-post {
  position: relative;
  border-radius: 9px;
  overflow: hidden;
  margin-bottom: 40px;
  box-shadow: 0px 0px 35px rgba(0,0,0,0.15);
}
.news-feed-section .saasio-blog-text {
  padding: 22px 30px 0px 0px;
}
.news-feed-section .saasio-blog-text h3 {
  color: #373a5b;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.6;
  font-family: 'Poppins';
}
.news-feed-section .saasio-blog-text p {
  margin-bottom: 0;
  padding-top: 20px;
}
.blog-read-more  {
  color: #6c2b95;
  display: block;
  font-weight: 700;
  margin-top: 20px;
}
.saasio-post-meta  a {
  color: #777a91;
  margin-right: 15px;
}
.saasio-post-meta  a i {
  margin-right: 3px;
  font-size: 15px;
}
.saasio-pagination {
  padding-top: 30px;
}
.saasio-pagination a {
  height: 40px;
  width: 40px;
  color: #fff;
  font-weight: 700;
  margin: 0px 5px;
  line-height: 40px;
  display: inline-block;
  background-color: #6c2b95;
  transition: .3s all ease-in-out;
}
.saasio-pagination a:hover,
.saasio-pagination a.active {
  background-color: #cc0fda;
}
.saasio-blog-sidebar {
  padding-left: 30px;
}
.side-bar-widget {
  margin-bottom: 60px;
}
.saasio-blog-sidebar .widget-title-2 {
  font-size: 24px;
  font-weight: 700;
  padding-bottom: 30px;
}
.side-bar-widget .search-widget input {
  height: 50px;
  width: 100%;
  border: none;
  padding-left: 15px;
  background-color: #e6e6e6;
}
.side-bar-widget .search-widget button {
  top: 0;
  right: 0;
  color: #fff;
  width: 50px;
  border: none;
  height: 50px;
  position: absolute;
  background-color: #6c2b95;
}
.category-widget li a {
  width: 100%;
  display: block;
  font-family: 'Poppins';
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid #e8d8d8;
}
.category-widget li:last-child a {
  border-bottom: 0;
}
.category-widget li a span {
  float: right;
}
.recent-post-img-text {
  width: 100%;
  display: inline-block;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e8d8d8;
}
.recent-post-img-text:last-child {
  border-bottom: none;
}
.recent-post-img-text .recent-post-img {
  height: 100px;
  width: 100px;
  overflow: hidden;
  margin-right: 15px;
}
.recent-post-img-text .recent-post-text h3 {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.5;
  padding-bottom: 5px;
}
.recent-post-img-text .recent-post-text .rec-post-meta {
  color: #9e9e9b;
}
.popular-widget a {
  color: #fff;
  font-weight: 600;
  padding: 10px 15px;
  margin: 3px;
  float: left;
  display: inline-block;
  background-color: #6c2b95;
}
.blog-details-img {
  margin-bottom: 40px;
}
.blog-details-text h2 {
  color: #373a5b;
  font-size: 36px;
  font-weight: 700;
  padding-bottom: 25px;
}
.blog-details-text h3 {
  color: #373a5b;
  font-size: 30px;
  font-weight: 700;
  padding-bottom: 10px;
}
.blog-details-text article {
  padding: 20px 0px
}
.blog-details-text blockquote {
  z-index: 1;
  color: #fff;
  font-weight: 500;
  font-size: 20px;
  padding: 50px;
  border-radius: 4px;
  font-family: 'Poppins';
  position: relative;
  background-color: #73299a;
}
.blog-details-text blockquote:after {
  top: 50%;
  left: 0;
  right: 0;
  z-index: -1;
  font-size: 175px;
  font-weight: 900;
  content: '\f10d';
  text-align: center;
  position: absolute;
  transform: translateY(-50%);
  color: rgb(255 255 255 / 25%);
  font-family: 'Font Awesome 5 Free';
}
.blog-details-text blockquote h4 {
  font-size: 18px;
  font-weight: 700;
  padding-top: 15px;
}
.blog-details-tag {
  margin-top: 40px;
  padding: 10px 0px;
  border: 1px solid #ded7d7;
  border-left: none;
  border-right: none;
}
.blog-feed-tag  a {
  margin-left: 8px;
  padding: 10px 10px;
  display: inline-block;
  background-color: #f7f7f7;
  transition: .3s all ease-in-out;
}
.blog-details-tag a:hover {
  color: #fff;
  background-color: #73299a;
}
.blog-feed-share a {
  width: 43px;
  height: 43px;
  line-height: 43px;
  text-align: center;
  display: inline-block;
  background-color: #f7f7f7;
  transition: .3s all ease-in-out;
}
.saasio-comment-field {
  padding-top: 80px;
}
.saasio-comment-field h3 {
  font-weight: 600;
  font-size: 30px;
}
.comment-author-img {
  width: 120px;
  height: 120px;
  overflow: hidden;
  margin-right: 20px;
  border-radius: 100%;
}
.comment-list-item {
  margin: 60px 0px;
}
.comment-inner-box {
  padding: 30px;
  margin-bottom: 30px;
  background-color: #f8f8f8;
}
.comment-author-text {
  overflow: hidden;
}
.comment-author-text p {
  margin-bottom: 0;
  padding: 15px 0px 5px;
}
.comment-author-text h4 {
  font-size: 22px;
  font-weight: 700;
  padding-bottom: 10px;
}
.comment-author-text span {
  color: #969696;
}
.comment-reply {
  margin-top: 10px;
  padding: 8px 20px;
  border-radius: 4px;
  display: inline-block;
  color: #fff !important;
  background-color: #73299a;
}
.comment-form {
  margin-top: 40px;
}
.comment-form input,
.comment-form textarea {
  width: 100%;
  height: 60px;
  border:  none;
  padding-left: 20px;
  margin-bottom: 20px;
  background-color: #f0f0f0;
}
.comment-form textarea {
  height: 180px;
  padding-top: 20px;
}
.comment-form button {
  border: none;
  color: #fff;
  padding: 15px 30px;
  background-color: #73299a;
}
@media screen and (max-width: 1024px) { 
  .news-feed-section .saasio-blog-text {
    padding: 22px 30px 30px 0px
  }
  .news-feed-section .saasio-blog-text h3 {
    font-size: 20px;
  }
  .recent-post-img-text .recent-post-text h3 {
    font-size: 18px;
  }
}
@media screen and (max-width: 991px) { 
  .blog-feed-img {
    margin-bottom: 20px;
  } 
  .blog-feed-img-txt {
    padding: 30px;
  }
  .saasio-blog-sidebar {
    padding-left: 0;
  }
}
@media screen and (max-width: 767px) { 
  .saasio-blog-sidebar {
    margin-top: 30px;
  }
  .saasio-breadcurmb-section {
    padding: 150px 0px 100px;
  }
  .breadcurmb-title h2 {
    font-size: 30px;
  }
  .blog-feed-share {
    margin-top: 15px;
    float: left !important;
  }
  .comment-inner-box {
    padding: 15px;
  }
  .comment-author-img {
    height: 80px;
    width: 80px;
  }
}
/*=====================================================================
SaaSio - HR Management
=====================================================================*/
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a .pm-inft-icon i {
  display: block;
  background: linear-gradient(-90deg, #227df8 0%, #0054c4 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pm-footer-section .pm-footer-widget .pm-footer-support, .header-type-one .pm-header-support {
  font-size: 14px;
  padding-left: 60px;
}
.pm-footer-section .pm-footer-widget .pm-footer-support span, .header-type-one .pm-header-support span {
  color: #5c8ed0;
  display: block;
}
.pm-footer-section .pm-footer-widget .pm-footer-support a, .header-type-one .pm-header-support a {
  color: #fff;
  font-weight: 700;
}
.pm-footer-section .pm-footer-widget .pm-footer-support:before, .header-type-one .pm-header-support:before {
  top: 10px;
  left: 25px;
  width: 2px;
  content: "";
  height: 40px;
  position: absolute;
  background-color: #5c8ed0;
}
@keyframes fadeFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeFromUp {
  animation-name: fadeFromUp;
}

.fadeFromRight {
  animation-name: fadeFromRight;
}

.fadeFromLeft {
  animation-name: fadeFromLeft;
}

/*global area*/
/*----------------------------------------------------*/
.pm-home {
  margin: 0;
  padding: 0;
  color: #6480a7;
  font-size: 16px;
  overflow-x: hidden;
  line-height: 1.625;
  font-family: "Roboto";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

.pm-home::selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.pm-home::-moz-selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.pm-preloader {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999;
  width: 100%;
  height: 100%;
  overflow: visible;
  background-color: #fff;
  background: #fff url("../img/pre.svg") no-repeat center center;
}

@keyframes zooming {
  0% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.05, 1.05);
  }
  100% {
    transform: scale(1, 1);
  }
}
.zooming {
  animation: zooming 18s infinite both;
}

.pm-headline h1,
.pm-headline h2,
.pm-headline h3,
.pm-headline h4,
.pm-headline h5,
.pm-headline h6 {
  margin: 0;
  font-family: "Poppins";
}

.pm-title-tag {
  color: #0e65d9;
  font-size: 14px;
  font-weight: 700;
  padding: 8px 20px 5px;
  display: inline-flex;
  background-color: #f3f7ff;
}

.pm-section-title h2 {
  color: #003378;
  font-size: 36px;
  font-weight: 600;
  padding-top: 15px;
}

.pm-scrollup {
  right: 0px;
  z-index: 5;
  width: 60px;
  height: 60px;
  bottom: -17px;
  line-height: 60px;
  position: absolute;
  background-color: #fff;
}
.pm-scrollup i {
  color: #003378;
}

/*---------------------------------------------------- */
/*Header area*/
/*----------------------------------------------------*/
.pm-main-header {
  z-index: 5;
  width: 100%;
  padding-top: 15px;
  position: absolute;
}
.pm-main-header .pm-logo {
  margin-top: 15px;
}
.pm-main-header .dropdown {
  position: relative;
}
.pm-main-header .dropdown:after {
  top: -2px;
  color: #fff;
  right: -14px;
  content: "+";
  font-size: 18px;
  font-weight: 700;
  position: absolute;
  transition: 0.3s all ease-in-out;
}
.pm-main-header .dropdown .dropdown-menu {
  top: 65px;
  left: 0;
  opacity: 0;
  z-index: 2;
  margin: 0px;
  padding: 0px;
  height: auto;
  width: 200px;
  border: none;
  display: block;
  border-radius: 0;
  overflow: hidden;
  visibility: hidden;
  position: absolute;
  background-color: #fff;
  transition: all 0.4s ease-in-out;
  border-bottom: 2px solid #003378;
  box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
}
.pm-main-header .dropdown .dropdown-menu li {
  width: 100%;
  margin-left: 0;
  border-bottom: 1px solid #e5e5e5;
}
.pm-main-header .dropdown .dropdown-menu li a {
  width: 100%;
  color: #343434;
  display: block;
  font-size: 14px;
  padding: 10px 25px;
  position: relative;
  transition: 0.3s all ease-in-out;
}
.pm-main-header .dropdown .dropdown-menu li a:before {
  display: none;
}
.pm-main-header .dropdown .dropdown-menu li a:after {
  left: 10px;
  top: 16px;
  width: 8px;
  height: 8px;
  content: "";
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}
.pm-main-header .dropdown .dropdown-menu li a:hover {
  background-color: #003378;
  color: #fff;
}
.pm-main-header .dropdown .dropdown-menu li a:hover:after {
  transform: scale(1);
}
.pm-main-header .dropdown .dropdown-menu li:last-child {
  border-bottom: none;
}
.pm-main-header .dropdown:hover .dropdown-menu {
  top: 45px;
  opacity: 1;
  visibility: visible;
}
.pm-main-header .navbar-nav {
  display: inherit;
}
.pm-main-header .pm-main-navigation {
  padding: 20px 40px;
}
.pm-main-header .pm-main-navigation li {
  margin: 0px 32px;
}
.pm-main-header .pm-main-navigation li a {
  color: #fff;
  display: inline;
  font-size: 16px;
  font-weight: 700;
  position: relative;
  padding-bottom: 20px;
}
.pm-main-header .pm-main-navigation li a:before {
  left: 0;
  right: 0;
  width: 0%;
  content: "";
  bottom: 5px;
  height: 2px;
  margin: 0 auto;
  position: absolute;
  background-color: #fff;
  transition: 0.5s all ease-in-out;
}
.pm-main-header .pm-main-navigation li:hover a:before,
.pm-main-header .pm-main-navigation li a.active:before {
  width: 100%;
}
.pm-main-header .pm-header-btn {
  height: 60px;
  width: 115px;
  line-height: 60px;
  border: 2px solid #3c87eb;
  transition: 0.3s all ease-in-out;
}
.pm-main-header .pm-header-btn a {
  width: 100%;
  color: #fff;
  display: block;
  font-size: 14px;
  font-weight: 700;
}
.pm-main-header .pm-header-btn:hover {
  background-color: #000;
  border: 2px solid #000;
}

.header-type-one .container {
  max-width: 1450px;
}
.pm-sticky-menu {
  top: 20px;
  position: fixed;
  background-color: #000;
  animation-duration: 0.7s;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
}

.pm-main-header.pm-sticky-menu {
  top: 0px;
  z-index: 9;
  padding: 10px 0;
  box-shadow: 0 0 20px -10px rgba(0, 0, 0, 0.8);
}
.pm-main-header.pm-sticky-menu .dropdown:hover .dropdown-menu {
  top: 55px;
}
.pm-main-header.pm-sticky-menu .pm-header-support {
  display: none !important;
}

/*---------------------------------------------------- */
/*Banner area*/
/*----------------------------------------------------*/
.pm-banner-section-1 {
  padding: 275px 0px;
  background: linear-gradient(-90deg, #227df8 0%, #0054c4 100%);
}
.pm-banner-section-1 .pm-banner-shape1 {
  top: 105px;
  right: 115px;
}
.pm-banner-section-1 .pm-banner-shape2 {
  top: 175px;
  right: 200px;
}
.pm-banner-section-1 .pm-banner-shape3 {
  left: 115px;
  bottom: 40px;
}
.pm-banner-section-1 .pm-banner-shape4 {
  left: 195px;
  bottom: 115px;
}
.pm-banner-section-1 .pm-banner-content .pm-banner-text {
  color: #fff;
  max-width: 570px;
  padding-top: 40px;
}
.pm-banner-section-1 .pm-banner-content .pm-banner-text .pm-title-tag {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.102);
}
.pm-banner-section-1 .pm-banner-content .pm-banner-text h1 {
  color: #fff;
  font-size: 48px;
  font-weight: 600;
  line-height: 1.25;
  padding: 20px 0px;
}
.pm-banner-section-1 .pm-banner-content .pm-banner-text p {
  font-size: 18px;
  padding-bottom: 40px;
}
.pm-banner-section-1 .pm-banner-content .pm-banner-text p span {
  font-weight: 700;
}
.pm-banner-section-1 .pm-banner-content .pm-banner-text a {
  height: 60px;
  width: 200px;
  display: block;
  font-weight: 700;
  line-height: 60px;
  text-align: center;
  background-color: #003378;
  transition: 0.3s all ease-in-out;
}
.pm-banner-section-1 .pm-banner-content .pm-banner-text a:hover {
  background-color: #000;
}
.pm-banner-section-1 .pm-banner-content .pm-banenr-img {
  top: 0;
  right: -125px;
  animation: zoomIn 1.7s both 0.3s;
}

/*---------------------------------------------------- */
/*Partner area*/
/*----------------------------------------------------*/
.pm-partner-section .pm-partner-wrapper {
  top: -90px;
  padding: 60px;
  background-color: #fff;
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.02);
}
.pm-partner-section .pm-partner-slide-area .owl-nav {
  display: none;
}
.pm-partner-section .pm-partner-slide-area .pm-partner-img img {
  transition: 0.3s all ease-in-out;
  filter: grayscale(1);
}
.pm-partner-section .pm-partner-slide-area .pm-partner-img:hover img {
  filter: grayscale(0);
}

/*---------------------------------------------------- */
/*Feature area*/
/*----------------------------------------------------*/
.pm-feature-section {
  padding-bottom: 100px;
}
.pm-feature-section .pm-section-title {
  margin: 0 auto;
  max-width: 515px;
}
.pm-feature-section .pm-feature-cpntent {
  padding-top: 50px;
}

.pm-feature-innerbox {
  padding: 50px 40px;
  border: 2px solid #eff3f7;
  transition: 0.3s all ease-in-out;
}
.pm-feature-innerbox:hover {
  border: 2px solid #fff;
  box-shadow: 0px 30px 60px 0px rgba(0, 51, 120, 0.04);
}
.pm-feature-innerbox .pm-feature-top {
  margin-bottom: 28px;
  padding-bottom: 20px;
  border-bottom: 2px solid #eff3f7;
}
.pm-feature-innerbox .pm-feature-top .pm-feature-icon {
  margin-right: 20px;
}
.pm-feature-innerbox .pm-feature-top .pm-feature-text h3 {
  font-size: 18px;
  font-weight: 600;
  color: #003378;
  padding-bottom: 5px;
  text-transform: capitalize;
}
.pm-feature-innerbox .pm-feature-list li {
  padding-left: 35px;
  position: relative;
  margin-bottom: 20px;
}
.pm-feature-innerbox .pm-feature-list li:after {
  top: 0;
  left: 0;
  color: #3186f8;
  content: "";
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}
.pm-feature-innerbox .pm-feature-list li:last-child {
  margin-bottom: 0;
}

.pm-feature-section-2 {
  background-color: #f8f8f8;
}
.pm-feature-section-2 .pm-feature-content-2 .pm-feature-process {
  z-index: 1;
  margin-bottom: 100px;
  background-color: #fff;
  padding: 80px 80px 65px 80px;
  box-shadow: 0px 30px 60px 0px rgba(0, 51, 120, 0.04);
}
.pm-feature-section-2 .pm-feature-content-2 .pm-feature-process .pm-feature-item2 {
  max-width: 480px;
  float: left;
}
.pm-feature-section-2 .pm-feature-content-2 .pm-feature-process .pm-f-process-img {
  right: -85px;
  box-shadow: 0px 30px 60px 0px rgba(0, 51, 120, 0.04);
}
.pm-feature-section-2 .pm-feature-content-2 .pm-feature-process:after {
  z-index: -1;
  content: "";
  left: -140px;
  width: 1450px;
  height: 685px;
  bottom: -400px;
  position: absolute;
  background-image: url(../img/hrm/shape/dot-s.png);
}
.pm-feature-section-2 .pm-feature-content-2 .pm-feature-item-details {
  padding: 15px 0px 40px;
}
.pm-feature-section-2 .pm-feature-content-2 .pm-feature-item-content {
  width: 100%;
  margin-bottom: 16px;
  display: inline-block;
}
.pm-feature-section-2 .pm-feature-content-2 .pm-feature-item-content .pm-f-process-icon {
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-right: 20px;
  border-radius: 100%;
  background: linear-gradient(-90deg, #227df8 0%, #0054c4 100%);
}
.pm-feature-section-2 .pm-feature-content-2 .pm-feature-item-content .pm-f-process-icon i {
  color: #fff;
}
.pm-feature-section-2 .pm-feature-content-2 .pm-feature-item-content .pm-f-process-text {
  font-size: 18px;
  font-weight: 500;
  padding-top: 12px;
}
.pm-feature-section-2 .pm-feature-content-2:nth-child(even) .pm-feature-process .pm-feature-item2 {
  float: right;
}
.pm-feature-section-2 .pm-feature-content-2:nth-child(even) .pm-feature-process .pm-f-process-img {
  right: auto;
  left: -85px;
}
.pm-feature-section-2 .pm-feature-content-2:last-child .pm-feature-process:after {
  display: none;
}

/*---------------------------------------------------- */
/*intregations area*/
/*----------------------------------------------------*/
.pm-intregation-section {
  padding: 100px 0px;
  background-color: #f8f8f8;
}
.pm-intregation-section .pm-intregation-img .intr-circle-shape {
  top: 35px;
  right: 30px;
}
.pm-intregation-section .pm-intregation-img .intr-img-item {
  display: inline-block;
  margin-right: 30px;
}
.pm-intregation-section .pm-intregation-img .intr-img-top {
  margin-bottom: 30px;
}
.pm-intregation-section .pm-intregation-img .intr-img-top .intr-img-item:nth-child(2) {
  transform: translateY(60px);
}
.pm-intregation-section .pm-intregation-img .intr-img-bottom .intr-img-item:nth-child(1) {
  transform: translateY(-60px);
}
.pm-intregation-section .pm-intregation-text {
  padding-top: 45px;
}
.pm-intregation-section .pm-intregation-text .pm-title-tag {
  background-color: #fff;
}
.pm-intregation-section .pm-intregation-text .pm-intregation-details {
  padding: 25px 0px 45px;
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner {
  margin-bottom: 20px;
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a {
  z-index: 1;
  width: 100%;
  display: block;
  position: relative;
  background-color: #fff;
  padding: 18px 40px 13px;
  transition: all 500ms ease;
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a:before {
  top: 0;
  left: 0;
  content: "";
  width: 0%;
  height: 100%;
  z-index: -1;
  position: absolute;
  transition: all 500ms ease;
  background: linear-gradient(-90deg, #227df8 0%, #0054c4 100%);
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a:after {
  top: 12px;
  right: 40px;
  color: #cfd8e4;
  font-size: 24px;
  content: "";
  font-weight: 900;
  position: absolute;
  font-family: "Font Awesome 5 Free";
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a .pm-inft-icon {
  font-size: 22px;
  margin-top: 3px;
  margin-right: 30px;
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a .pm-inft-icon i {
  transition: 0.3s all ease-in-out;
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a .pm-inft-text {
  color: #003378;
  font-size: 18px;
  font-weight: 700;
  transition: 0.3s all ease-in-out;
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a:hover:before {
  width: 100%;
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a:hover .pm-inft-icon i {
  color: #ffffff;
  background: none;
  -webkit-text-fill-color: inherit;
}
.pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a:hover .pm-inft-text {
  color: #fff;
}

/*---------------------------------------------------- */
/*process area*/
/*----------------------------------------------------*/
.pm-process-section {
  padding: 100px 0px;
}
.pm-process-section .pm-process-wrapper {
  padding-top: 50px;
}
.pm-process-section .pm-process-line {
  top: 50%;
  left: 0;
  right: 0;
  text-align: center;
  transform: translateY(-50%);
}

.pm-process-icon-text {
  margin: 0px 35px;
  display: inline-block;
}
.pm-process-icon-text:nth-child(even) {
  transform: translateY(40px);
}
.pm-process-icon-text .pm-process-icon {
  width: 160px;
  height: 160px;
  line-height: 160px;
  border-radius: 100%;
  margin-bottom: 35px;
  background-color: #fff;
  box-shadow: 0px 30px 60px 0px rgba(0, 51, 120, 0.04);
}
.pm-process-icon-text .pm-process-icon span {
  right: 0;
  bottom: 0;
  color: #fff;
  width: 50px;
  height: 50px;
  font-weight: 600;
  line-height: 50px;
  text-align: center;
  position: absolute;
  border-radius: 100%;
  font-family: "Poppins";
  background: linear-gradient(-90deg, #227df8 0%, #0054c4 100%);
}
.pm-process-icon-text .pm-process-text h3 {
  color: #003378;
  font-size: 18px;
  font-weight: 700;
  font-weight: 600;
}

/*---------------------------------------------------- */
/*counter area*/
/*----------------------------------------------------*/
.pm-counter-section {
  z-index: 1;
}
.pm-counter-section:after {
  left: 0;
  bottom: 0;
  content: "";
  width: 100%;
  height: 80px;
  z-index: -1;
  position: absolute;
  background-color: #f7f7f7;
}
.pm-counter-section .pm-counter-wrapper {
  padding: 75px 0px 40px;
  background-color: #fff;
  box-shadow: 0px 30px 60px 0px rgba(0, 51, 120, 0.04);
}
.pm-counter-section .pm-counter-text-icon .odometer,
.pm-counter-section .pm-counter-text-icon strong {
  line-height: 1;
  color: #003378;
  font-weight: 600;
  font-size: 50px;
  font-family: "Poppins";
}
.pm-counter-section .pm-counter-text-icon .odometer {
  line-height: 0.8;
  font-weight: 600;
}
.pm-counter-section .pm-counter-text-icon strong {
  top: 10px;
  position: relative;
}
.pm-counter-section .pm-counter-text-icon p {
  color: #003378;
  font-weight: 700;
  padding-top: 5px;
}

/*---------------------------------------------------- */
/*testimonial area*/
/*----------------------------------------------------*/
.pm-testimonial-section {
  padding: 100px 0px 60px;
  background-color: #f7f7f7;
}
.pm-testimonial-section .pm-testimonial-bg {
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
}

.pm-testimonial-inner {
  padding: 30px;
  margin-bottom: 40px;
  background-color: #fff;
  box-shadow: 0px 8px 16px 0px rgba(0, 51, 120, 0.03);
}
.pm-testimonial-inner .pm-testimonial-author {
  margin-top: 18px;
}
.pm-testimonial-inner .pm-testimonial-author .pm-author-text h3 {
  color: #003378;
  font-size: 18px;
  font-weight: 600;
}
.pm-testimonial-inner .pm-testimonial-author .pm-author-text ul {
  line-height: 1;
}
.pm-testimonial-inner .pm-testimonial-author .pm-author-text li {
  line-height: 0.5;
}
.pm-testimonial-inner .pm-testimonial-author .pm-author-text li a {
  color: #fbb040;
  font-size: 10px;
}
.pm-testimonial-inner .pm-testimonial-img {
  width: 40px;
  height: 40px;
  overflow: hidden;
  margin-right: 20px;
  border-radius: 100%;
}

.pm-testimonial-content .pm-testimonial-text-item {
  padding-top: 150px;
}
.pm-testimonial-content .col-md-4:nth-child(1) .pm-testimonial-inner:nth-child(2) {
  transform: translateX(-40px);
}
.pm-testimonial-content .col-md-4:nth-child(3) .pm-testimonial-inner:nth-child(2) {
  transform: translateX(40px);
}

/*---------------------------------------------------- */
/*Blog area*/
/*----------------------------------------------------*/
.pm-blog-section {
  padding: 100px 0px;
}
.pm-blog-section .pm-blog-wrapper {
  padding-top: 40px;
  margin: 0px -15px;
}

.pm-blog-img-text {
  float: left;
  display: flex;
  cursor: pointer;
  max-width: 610px;
  padding: 0px 15px;
}
.pm-blog-img-text .pm-blog-text {
  display: none;
  max-width: 330px;
  padding: 50px 0px 0px 40px;
  box-shadow: 0px 8px 16px 0px rgba(0, 51, 120, 0.03);
  transition: opacity 1s ease-out;
  opacity: 0;
}
.pm-blog-img-text .pm-blog-text .pm-blog-meta {
  padding-bottom: 15px;
}
.pm-blog-img-text .pm-blog-text .pm-date-meta {
  color: #9db0cb;
  font-size: 14px;
  font-weight: 700;
  margin-left: 15px;
}
.pm-blog-img-text .pm-blog-text .pm-date-meta i {
  margin-right: 5px;
  color: #003378;
}
.pm-blog-img-text .pm-blog-text .pm-blog-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #003378;
  padding-bottom: 20px;
}
.pm-blog-img-text.active .pm-blog-text {
  opacity: 1;
  display: inline-block;
}

/*---------------------------------------------------- */
/*Footer area*/
/*----------------------------------------------------*/
.pm-footer-section {
  background-color: #f7f7f7;
}
.pm-footer-section .pm-footer-wrapper {
  padding: 100px 0px;
}
.pm-footer-section .footer-widget-area .pm-footer-widget {
  float: left;
  width: 32.33%;
}
.pm-footer-section .footer-widget-area .pm-footer-widget .pm-footer-store a {
  display: block;
  margin-bottom: 5px;
}
.pm-footer-section .pm-footer-widget .widget-title {
  font-size: 18px;
  font-weight: 700;
  color: #003378;
  padding-bottom: 35px;
}
.pm-footer-section .pm-footer-widget .pm-footer-support {
  color: #6480a7;
}
.pm-footer-section .pm-footer-widget .pm-footer-support:before {
  top: -2px;
  width: 1px;
  background: linear-gradient(-90deg, #227df8 0%, #0054c4 100%);
}
.pm-footer-section .pm-footer-widget .pm-footer-support a {
  color: #003378;
}
.pm-footer-section .pm-footer-widget p {
  max-width: 280px;
  padding-top: 38px;
}
.pm-footer-section .pm-footer-widget p a {
  font-weight: 700;
  color: #003378;
}
.pm-footer-section .pm-footer-widget .pm-footer-social {
  margin-top: 38px;
}
.pm-footer-section .pm-footer-widget .pm-footer-social a {
  z-index: 1;
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-right: 10px;
  text-align: center;
  position: relative;
  display: inline-block;
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}
.pm-footer-section .pm-footer-widget .pm-footer-social a:after {
  top: 0;
  left: 0;
  z-index: -1;
  content: "";
  width: 100%;
  height: 0%;
  position: absolute;
  transition: 0.3s all ease-in-out;
  background: linear-gradient(-90deg, #227df8 0%, #0054c4 100%);
}
.pm-footer-section .pm-footer-widget .pm-footer-social a:hover {
  color: #fff;
}
.pm-footer-section .pm-footer-widget .pm-footer-social a:hover:after {
  height: 100%;
}
.pm-footer-section .pm-footer-widget .pm-footer-menu-widget a {
  display: block;
  margin-bottom: 18px;
  transition: 0.3s all ease-in-out;
}
.pm-footer-section .pm-footer-widget .pm-footer-menu-widget a:hover {
  color: #000;
}

.pm-footer-copyright {
  padding: 35px 0px 32px;
  background-color: #efefef;
}
.pm-footer-copyright .pm-footer-copyright-menu a {
  font-size: 16px;
  font-weight: 700;
  color: #003378;
  margin-right: 70px;
  transition: 0.3s all ease-in-out;
}
.pm-footer-copyright .pm-footer-copyright-menu a:hover {
  color: #000;
}

/*---------------------------------------------------- */
/*call to action area*/
/*----------------------------------------------------*/
.pm-call-to-action-section .call-to-action-wrapper {
  z-index: 1;
  padding: 100px 0px;
  background: linear-gradient(-90deg, #227df8 0%, #0054c4 100%);
}
.pm-call-to-action-section .call-to-action-wrapper .pm-banner-shape1 {
  left: 85px;
  bottom: 45px;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-banner-shape2 {
  left: 170px;
  bottom: 75px;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-banner-shape3 {
  top: 65px;
  right: 65px;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-banner-shape4 {
  top: 100px;
  right: 145px;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content {
  z-index: 1;
  margin: 0 auto;
  max-width: 800px;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content p {
  color: #adcdf9;
  font-size: 18px;
  padding-top: 25px;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-section-title .pm-title-tag {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.15);
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-section-title h2 {
  color: #fff;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-call-action-btn {
  margin-top: 35px;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-call-action-btn a {
  color: #fff;
  height: 60px;
  font-weight: 700;
  margin: 0px 10px;
  line-height: 60px;
  text-align: center;
  display: inline-block;
  transition: 0.3s all ease-in-out;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-call-action-btn a:nth-child(1) {
  width: 200px;
  border: 2px solid #003378;
  background-color: #003378;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-call-action-btn a:nth-child(1):hover {
  background-color: #000;
  border: 2px solid #000;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-call-action-btn a:nth-child(2) {
  width: 160px;
  border: 2px solid #6f9fe0;
}
.pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-call-action-btn a:nth-child(2):hover {
  background-color: #000;
  border: 2px solid #000;
}

/*---------------------------------------------------- */
/*Mobile Menu area*/
/*----------------------------------------------------*/
.pm-mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 280px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 40px 0px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s ease-in;
}
.pm-mobile_menu_content .pm-mobile-main-navigation {
  width: 100%;
}
.pm-mobile_menu_content .pm-mobile-main-navigation .navbar-nav {
  width: 100%;
}
.pm-mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}
.pm-mobile_menu_content .pm-mobile-main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  transition: 0.3s all ease-in-out;
  border-bottom: 1px solid #dcdcdc;
}
.pm-mobile_menu_content .pm-mobile-main-navigation .navbar-nav li:first-child {
  border-top: 1px solid #dcdcdc;
}
.pm-mobile_menu_content .pm-mobile-main-navigation .navbar-nav li a {
  color: #000;
  padding: 0;
  width: 100%;
  display: block;
  font-size: 14px;
  font-weight: 400;
  padding: 5px 30px;
  text-transform: uppercase;
}
.pm-mobile_menu_content .m-brand-logo {
  width: 160px;
  margin: 0 auto;
  margin-bottom: 30px;
}

.pm-mobile_menu_wrap.mobile_menu_on .pm-mobile_menu_content {
  right: 0px;
  transition: all 0.7s ease-out;
}

.mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: 0%;
  height: 120vh;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.5s ease-in-out;
}

.mobile_menu_overlay_on {
  overflow: hidden;
}

.pm-mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  opacity: 1;
  visibility: visible;
}

.pm-mobile_menu_button {
  right: 0;
  top: -35px;
  z-index: 5;
  color: #fff;
  display: none;
  cursor: pointer;
  font-size: 30px;
  line-height: 40px;
  position: absolute;
  text-align: center;
}

.pm-mobile_menu .pm-mobile-main-navigation .navbar-nav li a:after {
  display: none;
}
.pm-mobile_menu .pm-mobile-main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.pm-mobile_menu .pm-mobile_menu_content .pm-mobile-main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
  width: 100%;
  border-top: 1px solid #dcdcdc;
}
.pm-mobile_menu .pm-mobile_menu_content .pm-mobile-main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 0 20px;
  line-height: 1;
}
.pm-mobile_menu .dropdown {
  position: relative;
}
.pm-mobile_menu .dropdown .dropdown-btn {
  position: absolute;
  top: 0px;
  right: 0;
  height: 30px;
  padding: 5px 10px;
}
.pm-mobile_menu .dropdown .dropdown-btn:before {
  content: "";
  position: absolute;
  height: 100%;
  width: 1px;
  top: 0;
  left: 0;
  background-color: #dcdcdc;
}
.pm-mobile_menu .pm-mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}
@media screen and (max-width: 1440px) {
  .pm-banner-section-1 .pm-banner-content .pm-banenr-img {
    right: -100px;
  }
}
@media screen and (max-width: 1300px) {
  .pm-banner-section-1 .pm-banner-content .pm-banenr-img {
    width: 50%;
    right: 0px;
  }

  .pm-feature-section-2 .pm-feature-content-2 .pm-feature-process .pm-f-process-img {
    right: -40px;
  }

  .pm-feature-section-2 .pm-feature-content-2:nth-child(even) .pm-feature-process .pm-f-process-img {
    left: -50px;
  }
}
@media screen and (max-width: 1199px) {
  .pm-feature-section-2 .pm-feature-content-2 .pm-feature-process .pm-f-process-img {
    right: 0;
    width: 50%;
  }

  .pm-feature-section-2 .pm-feature-content-2:nth-child(even) .pm-feature-process .pm-f-process-img {
    left: 0;
    width: 50%;
  }

  .pm-testimonial-content .col-md-4:nth-child(1) .pm-testimonial-inner:nth-child(2),
  .pm-testimonial-content .col-md-4:nth-child(3) .pm-testimonial-inner:nth-child(2) {
    transform: translateX(0);
  }
}
@media screen and (max-width: 1024px) {
  .pm-main-header .pm-main-navigation li {
    margin: 0px 15px;
  }

  .pm-banner-section-1 .pm-banner-content .pm-banner-text h1 {
    font-size: 40px;
  }

  .pm-banner-section-1 .pm-banner-content .pm-banner-text {
    max-width: 470px;
  }

  .pm-intregation-section .pm-intregation-img .intr-img-item {
    margin-right: 0;
  }

  .pm-feature-section-2 .pm-feature-content-2 .pm-feature-process {
    padding: 60px 30px;
  }

  .pm-feature-section-2 .pm-feature-content-2 .pm-feature-process .pm-feature-item2 {
    max-width: 450px;
  }

  .pm-blog-img-text {
    width: 100%;
    display: flex;
    margin-bottom: 30px;
  }

  .pm-blog-img-text .pm-blog-text {
    opacity: 1;
    display: block;
  }
}
@media screen and (max-width: 991px) {
  .pm-main-menu-item {
    display: none;
  }

  .pm-banner-section-1 {
    padding: 150px 0px;
  }

  .pm-banner-section-1 .pm-banner-content .pm-banenr-img {
    width: 100%;
    margin-top: 40px;
    position: static !important;
  }

  .pm-feature-innerbox {
    margin-bottom: 30px;
  }

  .pm-intregation-img {
    max-width: 570px;
  }

  .pm-intregation-section .pm-intregation-img .intr-img-item {
    margin-right: 30px;
  }

  .pm-feature-section-2 .pm-feature-content-2 .pm-feature-process .pm-f-process-img {
    width: 100%;
    margin-top: 30px;
    position: static !important;
  }

  .pm-feature-section-2 .pm-feature-content-2:nth-child(even) .pm-feature-process .pm-feature-item2 {
    float: left;
  }

  .pm-feature-section-2 .pm-feature-content-2:nth-child(even) .pm-feature-process .pm-f-process-img {
    width: 100%;
  }

  .pm-process-icon-text:nth-child(even) {
    transform: translateY(0);
  }

  .pm-process-section .pm-process-line {
    display: none;
  }

  .pm-process-icon-text {
    margin-bottom: 30px;
  }

  .pm-testimonial-inner .pm-author-text {
    overflow: hidden;
  }

  .footer-widget-area {
    margin-top: 40px;
  }

  .pm-main-header .pm-logo {
    margin-top: 0;
  }

  .header-type-one .pm-header-support {
    display: none !important;
  }

  .pm-main-header.pm-sticky-menu {
    padding: 15px 0px;
  }

  .pm-mobile_menu_button {
    display: block;
  }
}
@media screen and (max-width: 767px) {
  .pm-counter-text-icon {
    margin-bottom: 30px;
  }

  .pm-testimonial-content .pm-testimonial-text-item {
    padding: 10px 0px 40px;
  }
}
@media screen and (max-width: 480px) {
  .pm-banner-section-1 {
    padding: 100px 0px;
  }

  .pm-banner-section-1 .pm-banner-content .pm-banner-text h1 {
    font-size: 32px;
  }

  .pm-banner-section-1 .pm-banner-content .pm-banner-text p {
    font-size: 16px;
    padding-bottom: 20px;
  }

  .pm-section-title h2 {
    font-size: 26px;
  }

  .pm-banner-section-1 .pm-banner-content .pm-banner-text a {
    width: 50px;
    height: 50px;
    width: 160px;
    line-height: 50px;
  }

  .pm-partner-section .pm-partner-wrapper {
    top: 0;
    margin-bottom: 50px;
  }

  .pm-feature-section {
    padding-bottom: 50px;
  }

  .pm-intregation-section .pm-intregation-img .intr-circle-shape {
    display: none;
  }

  .pm-intregation-section .pm-intregation-img .intr-img-top {
    margin-bottom: 0;
  }

  .pm-intregation-section .pm-intregation-img .intr-img-top .intr-img-item:nth-child(2),
  .pm-intregation-section .pm-intregation-img .intr-img-bottom .intr-img-item:nth-child(1) {
    transform: translateY(0);
  }

  .pm-intregation-section .pm-intregation-img .intr-img-item {
    margin-bottom: 30px;
  }

  .pm-intregation-section {
    padding: 50px 0px 20px;
  }

  .pm-feature-section-2 .pm-feature-content-2 .pm-feature-process {
    margin-bottom: 50px;
  }

  .pm-process-icon-text .pm-process-icon {
    margin: 0 auto;
    margin-bottom: 20px;
  }

  .pm-process-icon-text {
    width: 100%;
    margin: 0 auto;
    display: inline-block;
    margin-bottom: 30px;
  }

  .pm-process-section {
    padding: 50px 0px 20px;
  }

  .pm-counter-section .pm-counter-wrapper {
    padding: 20px 0px;
  }

  .pm-testimonial-section {
    padding: 50px 0px 20px;
  }

  .pm-blog-img-text {
    display: block;
  }

  .pm-blog-img {
    width: 100%;
    float: none;
    display: block;
  }

  .pm-blog-img-text .pm-blog-text {
    max-width: 100%;
    padding-left: 0;
    padding-top: 25px;
    display: inline-block;
  }

  .pm-blog-section {
    padding: 80px 0px 40px;
  }

  .pm-call-to-action-section .call-to-action-wrapper {
    padding: 40px 15px;
  }

  .pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-call-action-btn a:nth-child(1) {
    margin-bottom: 20px;
  }

  .pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-call-action-btn a:nth-child(1),
  .pm-call-to-action-section .call-to-action-wrapper .pm-call-action-content .pm-call-action-btn a:nth-child(2) {
    height: 50px;
    width: 130px;
    font-size: 14px;
    line-height: 50px;
  }

  .pm-footer-section .footer-widget-area .pm-footer-widget {
    width: 100%;
    margin-bottom: 30px;
  }

  .pm-footer-section .pm-footer-wrapper {
    padding: 50px 0px 20px;
  }

  .pm-footer-copyright .pm-footer-copyright-menu a {
    font-size: 14px;
    margin-right: 5px;
  }

  .pm-scrollup {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }

  .pm-counter-section .pm-counter-text-icon .odometer,
  .pm-counter-section .pm-counter-text-icon strong {
    font-size: 30px;
  }

  .pm-counter-section .pm-counter-text-icon strong {
    top: 5px;
  }
}
@media screen and (max-width: 380px) {
  .pm-section-title h2 {
    font-size: 24px;
  }

  .pm-feature-section-2 .pm-feature-content-2 .pm-feature-item-content .pm-f-process-text {
    font-size: 16px;
  }
}
/*---------------------------------------------------- */
/*dark-version area*/
/*----------------------------------------------------*/
.pm-home.dark-version {
  color: #afafaf;
}
.pm-home.dark-version .pm-banner-section-1,
.pm-home.dark-version .pm-call-to-action-section .call-to-action-wrapper {
  background: linear-gradient(-90deg, #151516 0%, #1f1f21 100%);
}
.pm-home.dark-version .pm-title-tag,
.pm-home.dark-version .pm-testimonial-inner {
  background-color: #424242;
}
.pm-home.dark-version .pm-partner-section,
.pm-home.dark-version .pm-feature-section,
.pm-home.dark-version .pm-feature-section-2 .pm-feature-content-2 .pm-feature-process,
.pm-home.dark-version .pm-process-section,
.pm-home.dark-version .pm-counter-section,
.pm-home.dark-version .pm-blog-section,
.pm-home.dark-version .pm-call-to-action-section,
.pm-home.dark-version .pm-footer-section,
.pm-home.dark-version .pm-scrollup {
  background-color: #252222;
}
.pm-home.dark-version .pm-counter-section .pm-counter-wrapper,
.pm-home.dark-version .pm-testimonial-section,
.pm-home.dark-version .pm-counter-section:after {
  background-color: #000;
}
.pm-home.dark-version .pm-section-title h2,
.pm-home.dark-version .pm-feature-innerbox .pm-feature-top .pm-feature-text h3,
.pm-home.dark-version .pm-process-icon-text .pm-process-text h3,
.pm-home.dark-version .pm-testimonial-inner .pm-testimonial-author .pm-author-text h3,
.pm-home.dark-version .pm-counter-section .pm-counter-text-icon .odometer,
.pm-home.dark-version .pm-counter-section .pm-counter-text-icon strong,
.pm-home.dark-version .pm-counter-section .pm-counter-text-icon p,
.pm-home.dark-version .pm-blog-img-text .pm-blog-text .pm-blog-title h3,
.pm-home.dark-version .pm-footer-section .pm-footer-widget .widget-title,
.pm-home.dark-version .pm-footer-section .pm-footer-widget p a,
.pm-home.dark-version .pm-footer-section .pm-footer-widget .pm-footer-support a,
.pm-home.dark-version .pm-footer-copyright .pm-footer-copyright-menu a,
.pm-home.dark-version .pm-scrollup i {
  color: #fff;
}
.pm-home.dark-version .pm-feature-innerbox {
  border: 2px solid #404244;
}
.pm-home.dark-version .pm-feature-innerbox .pm-feature-top {
  border-bottom: 2px solid #404244;
}
.pm-home.dark-version .pm-intregation-section,
.pm-home.dark-version .pm-process-icon-text .pm-process-icon,
.pm-home.dark-version .pm-footer-section .pm-footer-widget .pm-footer-social a,
.pm-home.dark-version .pm-partner-section .pm-partner-wrapper,
.pm-home.dark-version .pm-footer-copyright {
  background-color: #000;
}
.pm-home.dark-version .pm-intregation-section .pm-intregation-text .pm-intr-feature-inner a {
  background-color: #1a1a1a;
}
.pm-home.dark-version .pm-feature-section-2 {
  background-color: #000;
}
.pm-home.dark-version .pm-feature-section-2 .pm-feature-content-2 .pm-feature-process:after {
  display: none;
}
.pm-home.dark-version .pm-process-section .pm-process-line {
  opacity: 0.2;
}
.pm-home.dark-version .pm-testimonial-section .pm-testimonial-bg {
  opacity: 0.1;
}
.pm-home.dark-version .pm-blog-img-text .pm-blog-text {
  background-color: #1d1919;
}

/*---------------------------------------------------- */

/*****Inner Page Style****/
.inner-page-padding {
  padding: 100px 0px;
}
.sa-team-inner-inner-box {
  margin-bottom: 40px;
}
.sa-team-inner-inner-box .str-team-img-text:hover .str-team-text {
  height: 175px;
}
.sa-contact-inner-form {
  margin: 0 auto;
  max-width: 750px;
  padding-top: 50px;
}
.sa-contact-inner-form input,
.sa-contact-inner-form textarea {
  width: 100%;
  height: 50px;
  padding-left: 20px;
  border-radius: 5px;
  margin-bottom: 15px;
  border: 2px solid #e8e8e8;
}
.sa-contact-inner-form textarea {
  height: 150px;
  padding-top: 20px;
}
.sa-contact-inner-form button {
  color: #fff;
  height: 60px;
  width: 170px;
  border: none;
  font-weight: 600;
  border-radius: 30px;
  background-color: #73299a;
}
.sa-contact-inner-btn {
  width: 100%;
}
