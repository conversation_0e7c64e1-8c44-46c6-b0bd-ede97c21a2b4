//  =================
//      Imports
//  =================

@import '../../base/base';    // Base Variables


/*      Alert       */

.alert {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  margin-bottom: 15px;
  padding: 0.9375rem;

  .btn {
    padding: 3px 12px;
    margin-right: 20px;

    &:hover {
      box-shadow: none;
    }
  }

  .alert-icon svg {
    vertical-align: middle;
    width: 33px;
    height: 33px;
  }

  .close {
    color: $white;
    opacity: 1;
    width: 18px;
  }
}

/*Default Alerts*/

.alert-primary {
  color: $white;
  background-color: $primary;
  border-color: $primary;
}

.alert-warning {
  color: $white;
  background-color: $warning;
  border-color: $warning;
}

.alert-success {
  color: $white;
  background-color: $success;
  border-color: $success;
}

.alert-info {
  color: $white;
  background-color: $info;
  border-color: $info;
}

.alert-danger {
  color: $white;
  background-color: $danger;
  border-color: $danger;
}

.alert-dark {
  color: $white;
  background-color: $dark;
  border-color: $dark;
}

/*Outline Alerts*/

.alert-outline-primary {
  border-color: $primary;
  border-radius: 5px;
}

.alert-outline-warning {
  border-color: #dea82a;
  border-radius: 5px;
}

.alert-outline-success {
  border-color: $success;
  border-radius: 5px;
}

.alert-outline-info {
  border-color: #009eda;
  border-radius: 5px;
}

.alert-outline-danger {
  border-color: $danger;
  border-radius: 5px;
}

.alert-outline-dark {
  border-color: #454656;
  border-radius: 5px;
}

.alert {
  &.alert-light .close {
    color: $m-color_10;
  }

  &.solid-alert-3 .close, &.solid-alert-4 .close {
    color: $black;
  }
}

.hide-default {
  display: none;
}

/*      Light Alert         */

.alert-light-primary {
  color: $primary;
  background-color: $l-primary;
  border-color: $primary;

  svg.close {
    color: $primary;
  }
}

.alert-light-warning {
  color: $warning;
  background-color: $l-warning;
  border-color: $warning;

  svg.close {
    color: $warning;
  }
}

.alert-light-success {
  color: $success;
  background-color: $l-success;
  border-color: $success;

  svg.close {
    color: $success;
  }
}

.alert-light-info {
  color: $info;
  background-color: $l-info;
  border-color: $info;

  svg.close {
    color: $info;
  }
}

.alert-light-danger {
  color: $danger;
  background-color: $l-danger;
  border-color: $danger;

  svg.close {
    color: $danger;
  }
}

.alert-light-dark {
  color: $dark;
  background-color: $l-dark;
  border-color: $dark;

  svg.close {
    color: $dark;
  }
}

/*  Background Alerts      */

.alert-background {
  color: $white;
  background: #fff url(../../img/ab-1.jpeg) no-repeat center center;
  background-size: cover;
}

/*  Gradient Alerts      */

.alert-gradient {
  color: $white;
  border: none;
  background-size: cover;
  background-image: linear-gradient(135deg, #bc1a4e 0%, #004fe6 100%);
}

/* Custom Alerts */

/* Default */

.custom-alert-1 {
  background-color: $secondary;
  border-color: $secondary;
  border-radius: 5px;
  color: $white;

  .alert-icon {
    margin-right: 25px;
  }

  .media-body {
    display: flex;
    justify-content: space-between;
  }

  .alert-text {
    margin-right: 10px;

    strong, span {
      vertical-align: sub;
    }
  }
}

/*  Alert with Icon */

.alert-icon-left {
  border-left: 64px solid;

  svg:not(.close) {
    color: $white;
    width: 4rem;
    left: -4rem;
    text-align: center;
    position: absolute;
    top: 50%;
    margin-top: -10px;
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

.alert-icon-right {
  border-right: 64px solid;

  svg:not(.close) {
    color: $white;
    width: 4rem;
    right: -4rem;
    text-align: center;
    position: absolute;
    top: 50%;
    margin-top: -10px;
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  i {
    float: left;
    margin-right: 7px;
  }
}

.alert {
  &[class*=alert-arrow-]:before {
    content: '';
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 0;
    border-left: 8px solid;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left-color: inherit;
    margin-top: -8px;
  }

  &.alert-arrow-right:before {
    left: auto;
    right: 0;
    border-left: 0;
    border-right: 8px solid;
    border-right-color: inherit;
  }
}

@media (max-width: 575px) {
  .custom-alert-1 .media-body {
    display: block;
  }

  .alert .btn {
    margin-top: 8px;
  }
}