/*
    ==================
        Total Sales
    ==================
*/

body.minimal .widget-two {
    border: 1px solid #e0e6ed!important;
    box-shadow: none;
}

.widget-two {
  position: relative;
  background: $white;
  padding: 0;
  border-radius: 8px;
  height: 100%;
  -webkit-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
  -moz-box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);
  box-shadow: 0 4px 6px 0 rgba(85, 85, 85, 0.08), 0 1px 20px 0 rgba(0, 0, 0, 0.07), 0px 1px 11px 0px rgba(0, 0, 0, 0.07);

  .widget-content {
    font-size: 17px;
  }

  .w-chart {
    position: absolute;
    bottom: 0;
    bottom: 0;
    right: 0;
    left: 0;
  }

  .w-numeric-value {
    display: flex;
    color: $white;
    font-weight: 500;
    padding: 20px;
    justify-content: space-between;

    .w-icon {
      display: inline-block;
      background: $l-warning;
      padding: 13px 12px;
      border-radius: 50%;
      display: inline-flex;
      align-self: center;
      height: 45px;
      width: 45px;
    }

    svg {
      display: block;
      color: $warning;
      width: 20px;
      height: 20px;
    }

    .w-value {
      font-size: 19px;
      display: block;
      color: $m-color_10;
      font-weight: 700;
      margin-bottom: -9px;
    }

    .w-numeric-title {
      font-size: 13px;
      color: $m-color_6;
      font-weight: 600;
    }
  }
}