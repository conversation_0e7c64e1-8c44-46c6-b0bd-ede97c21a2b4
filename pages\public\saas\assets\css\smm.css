/*----------------------------------------------------
@File: Default Styles
@Author: 
@URL: 

This file contains the styling for the actual theme, this
is the file you need to edit to change the look of the
theme.
---------------------------------------------------- */
/*=====================================================================
@Template Name: 
@Author:


=====================================================================*/
@import url("https://fonts.googleapis.com/css?family=Poppins:400,600,500,700|Roboto:100,300,400,500,700&display=swap");
.smm-footer-section .smm-footer-widget .smm-footer-support {
  font-size: 14px;
  padding-left: 60px;
}
.smm-footer-section .smm-footer-widget .smm-footer-support span {
  color: #fff;
  display: block;
}
.smm-footer-section .smm-footer-widget .smm-footer-support a {
  color: #fff;
  font-weight: 700;
}
.smm-footer-section .smm-footer-widget .smm-footer-support:before {
  top: 10px;
  left: 25px;
  width: 2px;
  content: "";
  height: 40px;
  position: absolute;
  background-color: #fff;
}
@keyframes fadeFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeFromUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeFromUp {
  animation-name: fadeFromUp;
}

.fadeFromRight {
  animation-name: fadeFromRight;
}

.fadeFromLeft {
  animation-name: fadeFromLeft;
}

/*global area*/
/*----------------------------------------------------*/
.smm-home {
  margin: 0;
  padding: 0;
  color: #102fa5;
  font-size: 16px;
  overflow-x: hidden;
  line-height: 1.625;
  font-family: "Roboto";
  -moz-osx-font-smoothing: antialiased;
  -webkit-font-smoothing: antialiased;
}

.smm-home::selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.smm-home::-moz-selection {
  color: #ffffff;
  background-color: #6e3ebf;
}

.container {
  max-width: 1200px;
}

.ul-li ul {
  margin: 0;
  padding: 0;
}
.ul-li ul li {
  list-style: none;
  display: inline-block;
}

.ul-li-block ul {
  margin: 0;
  padding: 0;
}
.ul-li-block ul li {
  list-style: none;
  display: block;
}

div#smm-preloader {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999;
  width: 100%;
  height: 100%;
  overflow: visible;
  background-color: #fff;
  background: #fff url("../img/pre.svg") no-repeat center center;
}

[data-background] {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

a {
  color: inherit;
  text-decoration: none;
}
a:hover, a:focus {
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

section {
  overflow: hidden;
}

button {
  cursor: pointer;
}

.form-control:focus,
button:visited,
button.active,
button:hover,
button:focus,
input:visited,
input.active,
input:hover,
input:focus,
textarea:hover,
textarea:focus,
a:hover,
a:focus,
a:visited,
a.active,
select,
select:hover,
select:focus,
select:visited {
  outline: none;
  box-shadow: none;
  text-decoration: none;
  color: inherit;
}

.form-control {
  box-shadow: none;
}

.relative-position {
  position: relative;
}

.pera-content p {
  margin-bottom: 0;
}
@keyframes zooming {
  0% {
    transform: scale(1, 1);
  }
  50% {
    transform: scale(1.05, 1.05);
  }
  100% {
    transform: scale(1, 1);
  }
}
.zooming {
  animation: zooming 18s infinite both;
}

.smm-headline h1,
.smm-headline h2,
.smm-headline h3,
.smm-headline h4,
.smm-headline h5,
.smm-headline h6 {
  margin: 0;
  font-family: "Poppins";
}

.smm-section-title span {
  font-weight: 500;
  color: #ff5722;
  padding: 6px 20px;
  display: inline-block;
  margin-bottom: 15px;
  background-color: #fff4f1;
}
.smm-section-title h2 {
  font-size: 40px;
  font-weight: 600;
  line-height: 1.25;
  padding-bottom: 12px;
}

.smm-title-subtext {
  font-size: 18px;
}
.smm-title-subtext a {
  font-weight: 700;
}

/*---------------------------------------------------- */
/*Header area*/
/*----------------------------------------------------*/
.smm-main-header {
  z-index: 99;
  width: 100%;
  padding: 30px 0px;
  position: absolute;
  background-color: #fff;
  box-shadow: 0px 8px 16px 0px rgba(29, 33, 34, 0.04);
}
.smm-main-header .smm-logo {
  margin-top: 7px;
  padding-right: 50px;
}
.smm-main-header .dropdown {
  position: relative;
}
.smm-main-header .dropdown:after {
  top: -2px;
  color: #102fa5;
  right: -14px;
  content: "+";
  font-size: 18px;
  font-weight: 700;
  position: absolute;
  transition: 0.3s all ease-in-out;
}
.smm-main-header .dropdown .dropdown-menu {
  top: 65px;
  left: 0;
  opacity: 0;
  z-index: 2;
  margin: 0px;
  padding: 0px;
  height: auto;
  width: 200px;
  border: none;
  display: block;
  border-radius: 0;
  overflow: hidden;
  visibility: hidden;
  position: absolute;
  background-color: #fff;
  transition: all 0.4s ease-in-out;
  border-bottom: 2px solid #003378;
  box-shadow: 0 5px 10px 0 rgba(83, 82, 82, 0.1);
}
.smm-main-header .dropdown .dropdown-menu li {
  width: 100%;
  margin-left: 0;
  border-bottom: 1px solid #e5e5e5;
}
.smm-main-header .dropdown .dropdown-menu li a {
  width: 100%;
  color: #343434;
  display: block;
  font-size: 14px;
  padding: 10px 25px;
  position: relative;
  transition: 0.3s all ease-in-out;
}
.smm-main-header .dropdown .dropdown-menu li a:before {
  display: none;
}
.smm-main-header .dropdown .dropdown-menu li a:after {
  left: 10px;
  top: 16px;
  width: 8px;
  height: 8px;
  content: "";
  position: absolute;
  border-radius: 100%;
  transform: scale(0);
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}
.smm-main-header .dropdown .dropdown-menu li a:hover {
  background-color: #003378;
  color: #fff;
}
.smm-main-header .dropdown .dropdown-menu li a:hover:after {
  transform: scale(1);
}
.smm-main-header .dropdown .dropdown-menu li:last-child {
  border-bottom: none;
}
.smm-main-header .dropdown:hover .dropdown-menu {
  top: 45px;
  opacity: 1;
  visibility: visible;
}
.smm-main-header .navbar-nav {
  display: inherit;
}
.smm-main-header .smm-main-navigation {
  margin-top: 12px;
}
.smm-main-header .smm-main-navigation li {
  margin: 0px 25px;
}
.smm-main-header .smm-main-navigation li a {
  color: #102fa5;
  font-size: 14px;
  display: inline;
  font-weight: 700;
  position: relative;
  padding-bottom: 20px;
}
.smm-main-header .smm-main-navigation li a:before {
  left: 0;
  right: 0;
  width: 0%;
  content: "";
  bottom: 5px;
  height: 2px;
  margin: 0 auto;
  position: absolute;
  background-color: #102fa5;
  transition: 0.5s all ease-in-out;
}
.smm-main-header .smm-main-navigation li a.active:before,
.smm-main-header .smm-main-navigation li a {
  width: 100%;
}
.smm-main-header .smm-header-btn {
  color: #fff;
  height: 50px;
  width: 170px;
  line-height: 50px;
  border-radius: 7px;
  background-color: #102fa5;
  transition: 0.3s all ease-in-out;
}
.smm-main-header .smm-header-btn a {
  width: 100%;
  display: block;
  font-size: 14px;
  font-weight: 700;
}
.smm-main-header .smm-header-btn:hover {
  color: #fff;
  background-color: #000;
}
.smm-main-header .smm-side-bar-toggle {
  z-index: 1;
  width: 70px;
  height: 70px;
  right: 240px;
  bottom: -60px;
  cursor: pointer;
  line-height: 65px;
  text-align: center;
  position: absolute;
  border-radius: 10px;
  background-color: #fff;
  border: 2px solid #102fa5;
}
.smm-main-header .smm-side-bar-toggle:before {
  top: 8px;
  left: 8px;
  content: "";
  width: 50px;
  height: 50px;
  z-index: -1;
  position: absolute;
  border-radius: 10px;
  background-color: #ff5722;
}
.smm-main-header .smm-side-bar-toggle i {
  color: #fff;
  font-size: 26px;
}

.smm-sticky-menu {
  top: 0px;
  position: fixed;
  padding: 10px 0px;
  animation-duration: 0.7s;
  background-color: #fff;
  animation-fill-mode: both;
  animation-name: fadeInDown;
  animation-timing-function: ease;
  transition: 0.3s all ease-in-out;
}
.smm-sticky-menu .smm-side-bar-toggle {
  display: none;
}

.sm-side_inner_content {
  top: 0px;
  bottom: 0;
  right: -420px;
  height: 110vh;
  z-index: 101;
  position: fixed;
  width: 400px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 50px 50px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s;
}
.sm-side_inner_content p {
  text-align: left;
}
.sm-side_inner_content .side_inner_logo {
  margin: 30px 0px;
}
.sm-side_inner_content .side_contact {
  margin-bottom: 30px;
}
.sm-side_inner_content .side_contact .social_widget {
  margin-bottom: 40px;
}
.sm-side_inner_content .side_contact .social_widget h3 {
  font-size: 20px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}
.sm-side_inner_content .side_contact .social_widget li {
  color: #fff;
  width: 30px;
  height: 30px;
  margin: 0px 3px;
  line-height: 30px;
  text-align: center;
  border-radius: 4px;
  background-color: #102fa5;
}
.sm-side_inner_content .side_contact .social_widget li i {
  font-size: 14px;
}
.sm-side_inner_content .side_contact .smm-sidebar-gallary {
  margin-bottom: 25px;
}
.sm-side_inner_content .side_contact .smm-sidebar-gallary h3 {
  font-size: 20px;
  font-weight: 600;
  padding: 10px 0px 10px 0px;
}
.sm-side_inner_content .side_contact .smm-sidebar-gallary li {
  float: left;
  margin: 5px 3px;
}
.sm-side_inner_content .side_copywright {
  font-size: 14px;
}
.sm-side_inner_content .close_btn {
  top: 30px;
  right: 20px;
  width: 40px;
  height: 40px;
  cursor: pointer;
  line-height: 40px;
  text-align: center;
  position: absolute;
  background-color: #f5f5f5;
  transition: 0.3s all ease-in-out;
}
.sm-side_inner_content .close_btn i {
  font-size: 14px;
}
.sm-side_inner_content .close_btn:hover {
  background-color: #ff5722;
}
.sm-side_inner_content .close_btn:hover i {
  color: #fff;
}

.smm-sidebar-inner.wide_side_on .sm-side_inner_content {
  right: -15px;
  z-index: 99;
  transition: all 0.7s;
}

.smm-sidebar-inner {
  display: inline-block;
}
.smm-sidebar-inner .side_overlay {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  width: 100%;
  z-index: 9;
  height: 110vh;
  visibility: hidden;
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  transition: all 0.3s ease-in-out;
  cursor: url(../img/cl.png), auto;
}

.body_overlay_on {
  overflow: hidden;
}

.smm-sidebar-inner.wide_side_on .side_overlay {
  opacity: 1;
  visibility: visible;
}

/*---------------------------------------------------- */
/*Banner area*/
/*----------------------------------------------------*/
.smm-banner-section {
  background-color: #f7faff;
}
.smm-banner-section .smm-banner-wrapper .smm-banner-img {
  top: 200px;
  right: -110px;
  position: absolute;
  animation: zoomIn 1.7s both 0.3s;
}
.smm-banner-section .smm-banner-text-content {
  max-width: 560px;
  padding: 260px 0px 215px;
}
.smm-banner-section .smm-banner-text-content span {
  font-size: 14px;
  font-weight: 500;
  color: #ff5722;
  padding: 10px 20px;
  background-color: #fff;
}
.smm-banner-section .smm-banner-text-content h1 {
  font-size: 48px;
  font-weight: 600;
  line-height: 1.25;
  position: relative;
  padding: 20px 0px 50px;
}
.smm-banner-section .smm-banner-text-content h1 span {
  color: #ff5722;
}
.smm-banner-section .smm-banner-text-content a {
  color: #fff;
  width: 100%;
  height: 60px;
  width: 200px;
  font-weight: 700;
  line-height: 60px;
  text-align: center;
  border-radius: 8px;
  display: inline-block;
  transition: 0.3s all ease-in-out;
  background-color: #ff5722;
}
.smm-banner-section .smm-banner-text-content a:hover {
  background-color: #102fa5;
}

/*---------------------------------------------------- */
/*social area*/
/*----------------------------------------------------*/
.smm-social-section {
  top: -60px;
  overflow: visible;
  margin-bottom: -60px;
}
.smm-social-section .smm-social-wrapper .smm-social-icon-text {
  padding: 30px 40px 32px;
  background-color: #fff;
  box-shadow: 0px 16px 32px 0px rgba(125, 130, 131, 0.08);
}
.smm-social-section .smm-social-wrapper .smm-social-icon-text .smm-social-icon {
  margin-right: 25px;
}
.smm-social-section .smm-social-wrapper .smm-social-icon-text .smm-social-text p {
  font-size: 18px;
  font-weight: 700;
}
.smm-social-section .smm-social-wrapper .smm-social-icon-text .smm-social-text span {
  font-size: 18px;
}

/*---------------------------------------------------- */
/*About area*/
/*----------------------------------------------------*/
.smm-about-section {
  padding: 100px 0px 100px;
}

.smm-about-text-wrap {
  float: right;
  padding-top: 40px;
  max-width: 520px;
}
.smm-about-text-wrap .smm-title-subtext {
  padding: 12px 0px 35px;
}
.smm-about-text-wrap .smm-about-service-list li {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 20px;
}
.smm-about-text-wrap .smm-about-service-list li i {
  width: 40px;
  height: 40px;
  font-size: 18px;
  border-radius: 8px;
  text-align: center;
  line-height: 40px;
  color: #ff5722;
  margin-right: 20px;
  background-color: #fff4f1;
}

.smm-about-img {
  left: -165px;
  position: absolute;
}

/*---------------------------------------------------- */
/*Counter area*/
/*----------------------------------------------------*/
.smm-counter-section {
  z-index: 5;
  position: relative;
}
.smm-counter-section:after {
  bottom: 0;
  content: "";
  z-index: -1;
  width: 100%;
  height: 80px;
  position: absolute;
  background-color: #f7faff;
}

.smm-counter-text-icon:after {
  right: 0;
  top: 15px;
  width: 2px;
  content: "";
  height: 60px;
  position: absolute;
  background-color: #7b92d0;
}
.smm-counter-text-icon .odometer,
.smm-counter-text-icon strong {
  line-height: 1;
  font-weight: 600;
  font-size: 50px;
  color: #fff;
  font-family: "Poppins";
}
.smm-counter-text-icon .odometer {
  line-height: 0.8;
  font-weight: 600;
}
.smm-counter-text-icon strong {
  top: 10px;
  position: relative;
}
.smm-counter-text-icon p {
  color: #7b92d0;
  font-weight: 700;
  padding-top: 5px;
}

.smm-counter-wrapper {
  padding: 40px;
  border-radius: 10px;
  background-color: #002daa;
}
.smm-counter-wrapper .col-lg-3:last-child .smm-counter-text-icon:after {
  display: none;
}

/*---------------------------------------------------- */
/*Feature area*/
/*----------------------------------------------------*/
.smm-feature-section {
  padding: 100px 0px 70px;
  background-color: #f7faff;
}
.smm-feature-section .smm-feature-wrapper {
  margin-top: 35px;
}

.smm-feature-innerbox {
  padding: 40px;
  overflow: hidden;
  border-radius: 5px;
  position: relative;
  margin-bottom: 30px;
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}
.smm-feature-innerbox:after {
  left: 0;
  right: 0;
  width: 0%;
  bottom: 0;
  height: 4px;
  content: "";
  margin: 0 auto;
  position: absolute;
  background-color: #ff5722;
  transition: 0.4s all ease-in-out;
}
.smm-feature-innerbox .smm-feature-icon {
  margin-right: 25px;
}
.smm-feature-innerbox .smm-feature-icon i {
  font-size: 60px;
  line-height: 1;
}
.smm-feature-innerbox h3 {
  font-size: 20px;
  margin-top: 5px;
  max-width: 160px;
  font-weight: 600;
  display: inline-block;
}
.smm-feature-innerbox .smm-feature-text {
  width: 100%;
  margin-top: 15px;
  display: inline-block;
}
.smm-feature-innerbox:hover {
  box-shadow: 0px 16px 32px 0px rgba(0, 45, 170, 0.04);
}
.smm-feature-innerbox:hover:after {
  width: 100%;
}

/*---------------------------------------------------- */
/*Team area*/
/*----------------------------------------------------*/
.smm-team-section {
  padding: 100px 0px;
}

.smm-team-member-wrapper {
  margin-top: 40px;
}

.smm-team-innerbox {
  border-radius: 10px;
  padding: 40px 0px 38px;
  background-color: #f7faff;
  transition: 0.4s all ease-in-out;
}
.smm-team-innerbox .smm-team-img {
  margin-bottom: 22px;
}
.smm-team-innerbox .smm-team-img:before {
  bottom: 0;
  width: 40px;
  right: 65px;
  content: "+";
  height: 40px;
  font-size: 24px;
  position: absolute;
  border-radius: 100%;
  background-color: #fff;
}
.smm-team-innerbox .smm-team-img img {
  width: 180px;
  height: 180px;
  border-radius: 100%;
}
.smm-team-innerbox .smm-team-img .smm-member-social {
  top: -50px;
  right: 5px;
  opacity: 0;
  visibility: hidden;
  position: absolute;
  transition: 0.4s all ease-in-out;
}
.smm-team-innerbox .smm-team-img .smm-member-social a {
  width: 40px;
  height: 40px;
  display: block;
  margin-bottom: 8px;
  border-radius: 100%;
  background-color: #fff;
  transition: 0.3s all ease-in-out;
}
.smm-team-innerbox .smm-team-img .smm-member-social a:hover {
  color: #fff;
  background-color: #ff5722;
}
.smm-team-innerbox .smm-team-img .smm-member-social a i {
  line-height: 40px;
}
.smm-team-innerbox .smm-team-img .smm-member-social a:nth-child(3) {
  transform: translate(5px, -5px);
}
.smm-team-innerbox .smm-team-img .smm-member-social a:nth-child(2) {
  transform: translate(-25px, 0px);
}
.smm-team-innerbox .smm-team-img .smm-member-social a:nth-child(1) {
  transform: translate(-70px, 25px);
}
.smm-team-innerbox .smm-team-img.social-show:before {
  content: "-";
}
.smm-team-innerbox .smm-team-img.social-show .smm-member-social {
  opacity: 1;
  right: 35px;
  visibility: visible;
}
.smm-team-innerbox .smm-team-text span {
  font-size: 14px;
  font-weight: 700;
  color: #ff5722;
}
.smm-team-innerbox .smm-team-text h3 {
  font-size: 24px;
  font-weight: 700;
  padding-top: 10px;
  transition: 0.3s all ease-in-out;
}
.smm-team-innerbox:hover {
  background-color: #102fa5;
}
.smm-team-innerbox:hover .smm-team-text h3 {
  color: #fff;
}

/*---------------------------------------------------- */
/*Skill area*/
/*----------------------------------------------------*/
.smm-skill-section {
  padding: 0px 0px 80px;
  background-color: #102fa5;
}
.smm-skill-section .smm-skill-section-wrapper {
  margin: 0 auto;
  max-width: 1920px;
  padding-top: 100px;
}
.smm-skill-section .smm-skill-img {
  top: 0;
  left: 0;
  width: 50%;
  position: absolute;
}
.smm-skill-section .smm-skill-img img {
  height: 100%;
}
.smm-skill-section .smm-skill-img .smm-skill-play {
  left: 0;
  top: 50%;
  right: 0;
  text-align: center;
  transform: translateY(-50%);
}
.smm-skill-section .smm-skill-img .smm-skill-play a {
  font-size: 100px;
  color: #fff;
}

.smm-skill-wrapper {
  float: right;
  max-width: 500px;
}
.smm-skill-wrapper .skill-progress-bar {
  margin-top: 55px;
}
.smm-skill-wrapper .smm-section-title span {
  background-color: #ffffff1a;
}
.smm-skill-wrapper .smm-section-title h2 {
  color: #fff;
}
.smm-skill-wrapper .skill-set-percent {
  margin-top: 25px;
}
.smm-skill-wrapper .skill-set-percent h4 {
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 8px;
}
.smm-skill-wrapper .skill-set-percent .progress {
  height: 4px;
  border-radius: 0;
  position: relative;
  overflow: visible;
  background-color: #203fb3;
}
.smm-skill-wrapper .skill-set-percent .progress span {
  top: -40px;
  right: 40px;
  color: #fff;
  height: 20px;
  width: 40px;
  text-align: center;
  font-weight: 700;
  position: relative;
  background-color: #021e8b;
}
.smm-skill-wrapper .skill-set-percent .progress span:before {
  right: 0;
  bottom: 0;
  content: "";
  position: absolute;
  width: 0;
  bottom: -7px;
  border-top: 7px solid #021e8b;
  border-left: 5px solid transparent;
}
.smm-skill-wrapper .skill-set-percent .progress-bar {
  width: 0;
  top: 0px;
  float: left;
  height: 100%;
  position: relative;
  background-color: #fff;
  transition: 1s all ease-in-out;
}

/*---------------------------------------------------- */
/*Case-Study area*/
/*----------------------------------------------------*/
.smm-case-study-section {
  padding: 100px 0px;
}

.smm-case-study-wrapper {
  margin-top: 20px;
}

.smm-case-tab {
  padding: 35px 0px;
  margin-bottom: 40px;
  box-shadow: 0px 16px 32px 0px rgba(0, 45, 170, 0.04);
}
.smm-case-tab .nav {
  display: inherit;
}
.smm-case-tab .nav-tabs {
  border-bottom: none;
}
.smm-case-tab .nav-tabs .nav-link {
  padding: 0;
  border: none;
  font-weight: 700;
  margin: 0px 28px;
  position: relative;
}
.smm-case-tab .nav-tabs .nav-link:after {
  left: 0;
  right: 0;
  content: "";
  width: 5px;
  opacity: 0;
  height: 5px;
  bottom: 0px;
  margin: 0 auto;
  position: absolute;
  border-radius: 100%;
  background-color: #ff5722;
  transition: 0.3s all ease-in-out;
}
.smm-case-tab .nav-tabs .nav-link.active {
  color: #ff5722;
}
.smm-case-tab .nav-tabs .nav-link.active:after {
  opacity: 1;
  bottom: -10px;
}

.smm-case-innerbox {
  overflow: hidden;
  border-radius: 10px;
}
.smm-case-innerbox:before {
  content: "";
  top: 0;
  left: 0;
  opacity: 0;
  height: 100%;
  width: 100%;
  position: absolute;
  transition: 0.3s all ease-in-out;
  background-color: rgba(16, 45, 162, 0.85);
}
.smm-case-innerbox .smm-case-text {
  top: 0px;
  left: 40px;
  opacity: 0;
  z-index: 1;
  visibility: hidden;
  position: absolute;
  transition: 0.4s all ease-in-out;
}
.smm-case-innerbox .smm-case-text h3 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  padding-bottom: 18px;
}
.smm-case-innerbox .smm-case-text p {
  color: #b1bbe1;
  max-width: 285px;
}
.smm-case-innerbox .smm-case-popup {
  z-index: 1;
  left: 40px;
  width: 60px;
  bottom: 40px;
  opacity: 0;
  height: 60px;
  border-radius: 8px;
  position: absolute;
  text-align: center;
  transform: scale(0);
  background-color: #ff5722;
  transition: 0.4s all ease-in-out;
}
.smm-case-innerbox .smm-case-popup a {
  color: #fff;
  width: 100%;
  display: block;
}
.smm-case-innerbox .smm-case-popup a i {
  line-height: 60px;
}
.smm-case-innerbox:hover:before {
  opacity: 1;
}
.smm-case-innerbox:hover .smm-case-text {
  top: 40px;
  opacity: 1;
  visibility: visible;
}
.smm-case-innerbox:hover .smm-case-popup {
  opacity: 1;
  transform: scale(1);
  visibility: visible;
}

/*---------------------------------------------------- */
/*Testimonial area*/
/*----------------------------------------------------*/
.smm-testimonial-section {
  padding: 100px 0px;
}

.smm-testimonial-wrap {
  margin: 0 auto;
  max-width: 1045px;
}
.smm-testimonial-wrap .smm-testimonial-img {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  overflow: hidden;
  margin-bottom: 35px;
  border-radius: 100%;
  border: 5px solid #ff5722;
  box-shadow: 0px 16px 32px 0px rgba(0, 51, 120, 0.1);
}
.smm-testimonial-wrap .smm-testimonial-text p {
  color: #b9c3e9;
  margin: 0 auto;
  font-size: 20px;
  max-width: 780px;
  padding-bottom: 20px;
}
.smm-testimonial-wrap .smm-testimonial-text .smm-testi-author h3 {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
}
.smm-testimonial-wrap .smm-testimonial-text .smm-testi-author span {
  font-size: 14px;
  font-weight: 700;
  color: #ff5722;
}

.smm-testimonial-wrapper .owl-nav .owl-next,
.smm-testimonial-wrapper .owl-nav .owl-prev {
  top: 50%;
  color: #fff;
  width: 60px;
  height: 60px;
  cursor: pointer;
  text-align: center;
  position: absolute;
  border-radius: 10px;
  display: inline-block;
  background-color: #0c2895;
  transform: translateY(-50%);
  transition: 0.3s all ease-in-out;
}
.smm-testimonial-wrapper .owl-nav .owl-next i,
.smm-testimonial-wrapper .owl-nav .owl-prev i {
  line-height: 60px;
}
.smm-testimonial-wrapper .owl-nav .owl-next:hover,
.smm-testimonial-wrapper .owl-nav .owl-prev:hover {
  color: #fff;
  background-color: #ff5722;
}
.smm-testimonial-wrapper .owl-nav .owl-next {
  right: 0;
}
.smm-testimonial-wrapper .owl-dots {
  margin-top: 20px;
  text-align: center;
}
.smm-testimonial-wrapper .owl-dots .owl-dot {
  height: 4px;
  width: 20px;
  margin: 0 4px;
  cursor: pointer;
  display: inline-block;
  background-color: #001565;
  transition: 0.3s all ease-in-out;
}
.smm-testimonial-wrapper .owl-dots .owl-dot.active {
  background-color: #ff5722;
}

/*---------------------------------------------------- */
/*blog area*/
/*----------------------------------------------------*/
.smm-blog-section {
  padding: 100px 0px 0px;
}
.smm-blog-section .smm-blog-wrapper {
  margin-top: 28px;
}

.smm-blog-img-text {
  transition: 0.3s all ease-in-out;
}
.smm-blog-img-text .smm-blog-img {
  overflow: hidden;
  position: relative;
}
.smm-blog-img-text .smm-blog-img img {
  transition: 0.5s all ease-in-out;
}
.smm-blog-img-text .smm-blog-img:before {
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  content: "";
  opacity: 0;
  height: 100%;
  position: absolute;
  background-color: #102fa5;
  transition: 0.3s all ease-in-out;
}
.smm-blog-img-text .smm-blog-text {
  padding: 38px 40px;
  border: 2px solid #f8f8f8;
}
.smm-blog-img-text .smm-blog-text .smm-blog-tag {
  font-size: 14px;
  font-weight: 700;
  color: #ff5722;
}
.smm-blog-img-text .smm-blog-text h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  padding: 10px 0px 25px;
  border-bottom: 2px solid #f8f8f8;
}
.smm-blog-img-text .smm-blog-text .smm-date-meta {
  font-size: 14px;
  font-weight: 500;
}
.smm-blog-img-text .smm-blog-text .smm-date-meta i {
  margin-right: 5px;
  color: #ff5722;
}
.smm-blog-img-text .smm-blog-text .smm-blog-more {
  font-size: 14px;
  font-weight: 700;
  color: #ff5722;
  transition: 0.3s all ease-in-out;
}
.smm-blog-img-text .smm-blog-text .smm-blog-more:hover {
  color: #102fa5;
}
.smm-blog-img-text:hover {
  box-shadow: 0px 8px 16px 0px rgba(16, 47, 165, 0.04);
}
.smm-blog-img-text:hover .smm-blog-img:before {
  opacity: 0.8;
}
.smm-blog-img-text:hover .smm-blog-img img {
  transform: scale(1.1);
}

/*---------------------------------------------------- */
/*Partner area*/
/*----------------------------------------------------*/
.smm-partner-section {
  bottom: -100px;
}
.smm-partner-section .smm-partner-wrapper {
  z-index: 5;
  padding: 60px;
  background-color: #fff;
  border-top: 2px solid #102fa5;
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.02);
}
.smm-partner-section .smm-partner-slider .owl-nav {
  display: none;
}
.smm-partner-section .smm-partner-slider .smm-partner-img img {
  transition: 0.3s all ease-in-out;
  filter: grayscale(1);
}
.smm-partner-section .smm-partner-slider .smm-partner-img:hover img {
  filter: grayscale(0);
}

/*---------------------------------------------------- */
/*Mobile Menu area*/
/*----------------------------------------------------*/
.smm-mobile_menu_content {
  top: 0px;
  bottom: 0;
  right: -350px;
  height: 100vh;
  z-index: 101;
  position: fixed;
  width: 310px;
  overflow-y: scroll;
  background-color: #fff;
  padding: 20px 35px 35px 35px;
  box-shadow: 0px 3px 5px rgba(100, 100, 100, 0.19);
  transition: all 0.5s ease-in;
}
.smm-mobile_menu_content .smm-mobile-main-navigation {
  width: 100%;
}
.smm-mobile_menu_content .smm-mobile-main-navigation .navbar-nav {
  width: 100%;
}
.smm-mobile_menu_content .dropdown:after {
  display: none;
}
.smm-mobile_menu_content .navbar-nav .dropdown-menu {
  position: static !important;
  transform: none !important;
}
.smm-mobile_menu_content .smm-mobile-main-navigation .navbar-nav li {
  width: 100%;
  display: block;
  transition: 0.3s all ease-in-out;
}
.smm-mobile_menu_content .smm-mobile-main-navigation .navbar-nav li a {
  padding: 0;
  width: 100%;
  display: block;
  font-weight: 600;
  font-size: 14px;
  padding: 10px 30px 10px 0;
  text-transform: capitalize;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.smm-mobile_menu_content .m-brand-logo {
  width: 160px;
  margin: 0 auto;
  margin-bottom: 30px;
}

.smm-mobile_menu_wrap.mobile_menu_on .smm-mobile_menu_content {
  right: 0px;
  transition: all 0.7s ease-out;
}

.mobile_menu_overlay {
  top: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 100;
  right: 0%;
  height: 120vh;
  opacity: 0;
  visibility: hidden;
  background-color: rgba(0, 0, 0, 0.9);
  transition: all 0.5s ease-in-out;
}

.mobile_menu_overlay_on {
  overflow: hidden;
}

.smm-mobile_menu_wrap.mobile_menu_on .mobile_menu_overlay {
  opacity: 1;
  visibility: visible;
}

.smm-mobile_menu_button {
  right: 0;
  top: -36px;
  z-index: 5;
  color: #102fa5;
  display: none;
  cursor: pointer;
  font-size: 30px;
  line-height: 40px;
  position: absolute;
  text-align: center;
}

.smm-mobile_menu .smm-mobile-main-navigation .navbar-nav li a:after {
  display: none;
}
.smm-mobile_menu .smm-mobile-main-navigation .dropdown > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.smm-mobile_menu .smm-mobile_menu_content .smm-mobile-main-navigation .navbar-nav .dropdown-menu {
  border: none;
  display: none;
  transition: none;
  box-shadow: none;
  padding: 5px 0px;
  width: 100%;
}
.smm-mobile_menu .smm-mobile_menu_content .smm-mobile-main-navigation .navbar-nav .dropdown-menu li {
  border: none;
  padding: 0 20px;
  line-height: 1;
}
.smm-mobile_menu .smm-mobile_menu_content .smm-mobile-main-navigation .navbar-nav .dropdown-menu li a:hover {
  color: #ff5722;
  background-color: transparent;
}
.smm-mobile_menu .dropdown {
  position: relative;
}
.smm-mobile_menu .dropdown .dropdown-btn {
  color: #9397a7;
  position: absolute;
  top: 3px;
  right: 0;
  height: 30px;
  padding: 5px 10px;
}
.smm-mobile_menu .dropdown .dropdown-btn.toggle-open {
  transform: rotate(90deg);
}
.smm-mobile_menu .smm-mobile_menu_close {
  color: #d60606;
  cursor: pointer;
  top: 15px;
  left: 15px;
  font-size: 20px;
  position: absolute;
}

/*---------------------------------------------------- */
/*Footer area*/
/*----------------------------------------------------*/
.smm-footer-section .smm-footer-wrapper {
  padding: 200px 0px 100px;
}
.smm-footer-section .smm-footer-menu-widget {
  float: left;
  width: 33.33%;
}
.smm-footer-section .smm-footer-menu-widget .smm-footer-store a {
  display: block;
  margin-bottom: 5px;
}
.smm-footer-section .smm-footer-widget {
  color: #bdc4df;
}
.smm-footer-section .smm-footer-widget .widget-title {
  font-size: 18px;
  font-weight: 700;
  color: #fff;
  padding-bottom: 35px;
}
.smm-footer-section .smm-footer-widget .smm-footer-support {
  color: #fff;
}
.smm-footer-section .smm-footer-widget .smm-footer-support:before {
  top: -2px;
  width: 1px;
  background-color: #425bbd;
}
.smm-footer-section .smm-footer-widget .smm-footer-support a {
  color: #fff;
}
.smm-footer-section .smm-footer-widget p {
  max-width: 280px;
  padding-top: 38px;
}
.smm-footer-section .smm-footer-widget p a {
  font-weight: 700;
  color: #fff;
}
.smm-footer-section .smm-footer-widget .smm-footer-social {
  margin-top: 38px;
}
.smm-footer-section .smm-footer-widget .smm-footer-social a {
  z-index: 1;
  width: 50px;
  height: 50px;
  line-height: 50px;
  margin-right: 10px;
  text-align: center;
  position: relative;
  display: inline-block;
  border: 2px solid #2b47b5;
  transition: 0.3s all ease-in-out;
}
.smm-footer-section .smm-footer-widget .smm-footer-social a:hover {
  color: #fff;
  border: 2px solid #fff;
}
.smm-footer-section .smm-footer-widget .smm-footer-menu-widget a {
  display: block;
  margin-bottom: 18px;
  transition: 0.3s all ease-in-out;
}
.smm-footer-section .smm-footer-widget .smm-footer-menu-widget a:hover {
  color: #fff;
}
.smm-footer-section .smm-footer-widget .footer-blog-img-text {
  margin-bottom: 20px;
  display: inline-block;
}
.smm-footer-section .smm-footer-widget .footer-blog-img {
  width: 70px;
  height: 70px;
  overflow: hidden;
  margin-right: 10px;
}
.smm-footer-section .smm-footer-widget .footer-blog-text .smm-date-meta {
  color: #bdc4df;
  font-size: 12px;
  font-weight: 500;
}
.smm-footer-section .smm-footer-widget .footer-blog-text .smm-date-meta i {
  color: #ff5722;
  margin-right: 5px;
}
.smm-footer-section .smm-footer-widget .footer-blog-text h3 {
  color: #fff;
  line-height: 1.5;
  font-size: 14px;
  font-weight: 600;
}

.smm-footer-copyright {
  padding: 35px 0px 32px;
  background-color: #0a2697;
}
.smm-footer-copyright .smm-footer-copyright-menu a {
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  margin-right: 70px;
  transition: 0.3s all ease-in-out;
}
.smm-footer-copyright .smm-footer-copyright-menu a:hover {
  color: #000;
}

.smm-scrollup {
  right: 0px;
  z-index: 5;
  width: 60px;
  height: 60px;
  bottom: -17px;
  line-height: 60px;
  position: absolute;
  background-color: #1332ad;
}
.smm-scrollup i {
  color: #fff;
}

/*---------------------------------------------------- */
/*Dark Version Menu area*/
/*----------------------------------------------------*/
.smm-home.dark-version {
  color: #d8d8d8;
}
.smm-home.dark-version .smm-banner-section,
.smm-home.dark-version .smm-team-section,
.smm-home.dark-version .smm-case-study-section,
.smm-home.dark-version .smm-blog-section,
.smm-home.dark-version .smm-partner-section,
.smm-home.dark-version .smm-sidebar-inner.wide_side_on .sm-side_inner_content,
.smm-home.dark-version .smm-footer-section {
  background-color: #000000;
}
.smm-home.dark-version .smm-main-header,
.smm-home.dark-version .smm-counter-section,
.smm-home.dark-version .smm-feature-section {
  background-color: #121212;
}
.smm-home.dark-version .smm-team-innerbox {
  background-color: #212121;
}
.smm-home.dark-version .smm-social-section {
  position: relative;
}
.smm-home.dark-version .smm-social-section:after {
  width: 100%;
  bottom: -60px;
  content: "";
  position: absolute;
  height: 120px;
  z-index: -1;
  background-color: #121212;
}
.smm-home.dark-version .smm-main-header .smm-main-navigation li a,
.smm-home.dark-version .smm-main-header .dropdown:after,
.smm-home.dark-version .smm-social-section .smm-social-wrapper .smm-social-icon-text .smm-social-text span,
.smm-home.dark-version .smm-social-section .smm-social-wrapper .smm-social-icon-text .smm-social-text p {
  color: #d8d8d8;
}
.smm-home.dark-version .smm-banner-section .smm-banner-text-content span {
  background-color: #232323;
}
.smm-home.dark-version .smm-social-section .smm-social-wrapper .smm-social-icon-text,
.smm-home.dark-version .smm-about-section {
  background-color: #121212;
}
.smm-home.dark-version .smm-section-title span,
.smm-home.dark-version .smm-about-text-wrap .smm-about-service-list li i {
  background-color: #232323;
}
.smm-home.dark-version .smm-main-header .dropdown .dropdown-menu {
  background-color: #121212;
}
.smm-home.dark-version .smm-main-header .dropdown .dropdown-menu li {
  border-bottom: 1px solid #404040;
}
.smm-home.dark-version .smm-counter-section:after {
  background-color: #121212;
  position: relative;
}
.smm-home.dark-version .smm-counter-section:after:after {
  width: 100%;
  bottom: -80px;
  content: "";
  z-index: -1;
  height: 120px;
  position: absolute;
  background-color: #121212;
}
.smm-home.dark-version .smm-counter-wrapper {
  background-color: #272727;
}
.smm-home.dark-version .smm-blog-section {
  padding-bottom: 100px;
}
.smm-home.dark-version .smm-feature-innerbox {
  background-color: #000;
}
.smm-home.dark-version .smm-team-innerbox .smm-team-img::before,
.smm-home.dark-version .smm-team-innerbox .smm-team-img .smm-member-social a {
  background-color: #4e4e4e;
}
.smm-home.dark-version .smm-skill-section {
  background-color: #272727;
}
.smm-home.dark-version .nav-tabs .nav-item.show .nav-link, .smm-home.dark-version .nav-tabs .nav-link.active {
  background-color: transparent;
}
.smm-home.dark-version .smm-blog-img-text .smm-blog-text {
  border: 2px solid #232323;
}
.smm-home.dark-version .smm-partner-section {
  bottom: 0;
}

/*---------------------------------------------------- */
/*responsive area*/
/*----------------------------------------------------*/
@media screen and (max-width: 1024px) {
  .smm-main-header .smm-main-navigation li {
    margin: 0px 15px;
  }

  .smm-banner-section .smm-banner-wrapper .smm-banner-img {
    right: -275px;
  }

  .smm-about-img {
    left: -330px;
  }

  .smm-team-innerbox .smm-team-img.social-show .smm-member-social {
    right: 10px;
  }

  .smm-team-innerbox .smm-team-img:before {
    right: 40px;
  }

  .smm-skill-section .smm-skill-img {
    left: -40px;
  }

  .smm-footer-section .smm-footer-menu-widget {
    width: 29.33%;
  }
}
@media screen and (max-width: 1024px) {
  .smm-main-header {
    padding: 20px 0px;
  }

  .smm-main-header .smm-side-bar-toggle {
    height: 50px;
    width: 50px;
    line-height: 45px;
    bottom: -45px;
  }

  .smm-main-header .smm-side-bar-toggle:before {
    top: 6px;
    left: 6px;
    width: 35px;
    height: 35px;
  }

  .smm-main-header .smm-side-bar-toggle i {
    font-size: 18px;
  }
}
@media screen and (max-width: 991px) {
  .smm-main-menu-item {
    display: none;
  }

  .smm-main-header .smm-side-bar-toggle {
    display: none;
  }

  .smm-mobile_menu_button {
    display: block;
  }

  .smm-banner-section .smm-banner-wrapper .smm-banner-img {
    position: static;
  }

  .smm-banner-section {
    padding-bottom: 100px;
  }

  .smm-banner-section .smm-banner-text-content {
    max-width: 560px;
    padding: 260px 0px 50px;
  }

  .smm-about-img {
    position: static;
  }

  .smm-about-text-wrap {
    float: none;
  }

  .smm-counter-text-icon {
    margin-bottom: 20px;
  }

  .smm-counter-text-icon:after {
    display: none;
  }

  .smm-team-innerbox {
    max-width: 270px;
    margin: 0 auto;
    margin-bottom: 30px;
  }

  .smm-skill-section .smm-skill-img {
    left: 0;
    right: 0;
    width: 100%;
    position: relative;
    text-align: center;
  }
  .smm-skill-section .smm-skill-img img {
    height: auto;
  }

  .smm-skill-wrapper {
    float: none;
    margin-top: 40px;
  }

  .smm-footer-section .smm-footer-widget {
    margin-top: 30px;
  }

  .smm-team-innerbox .smm-team-img.social-show .smm-member-social {
    right: 35px;
  }

  .smm-team-innerbox .smm-team-img:before {
    right: 65px;
  }
}
@media screen and (max-width: 767px) {
  .smm-case-innerbox {
    margin: 0 auto;
    max-width: 370px;
    margin-bottom: 30px;
  }

  .smm-case-tab .nav-tabs .nav-link {
    margin: 0px 5px;
  }

  .smm-testimonial-wrapper .owl-nav {
    text-align: center;
    margin-top: 50px;
  }

  .smm-testimonial-wrapper .owl-dots {
    display: none;
  }

  .smm-testimonial-wrapper .owl-nav .owl-next,
.smm-testimonial-wrapper .owl-nav .owl-prev {
    position: static;
    height: 40px;
    width: 40px;
    transform: translate(0);
  }
  .smm-testimonial-wrapper .owl-nav .owl-next i,
.smm-testimonial-wrapper .owl-nav .owl-prev i {
    line-height: 40px;
  }

  .smm-blog-img-text {
    margin: 0 auto;
    max-width: 370px;
    margin-bottom: 30px;
  }
}
@media screen and (max-width: 680px) {
  .smm-footer-section .smm-footer-menu-widget {
    width: 100%;
    margin-bottom: 30px;
  }

  .smm-footer-section .smm-footer-widget .footer-blog-img-text {
    width: 100%;
  }
}
@media screen and (max-width: 480px) {
  .smm-banner-section .smm-banner-text-content h1 {
    font-size: 30px;
    padding: 20px 0px 20px;
  }

  .smm-banner-section .smm-banner-text-content {
    padding: 170px 0px 50px;
  }

  .smm-banner-section .smm-banner-text-content a {
    height: 50px;
    width: 145px;
    line-height: 50px;
  }

  .smm-section-title h2 {
    font-size: 26px;
  }

  .smm-about-text-wrap .smm-about-service-list li {
    font-size: 16px;
  }

  .smm-social-section {
    top: 0;
    padding-top: 40px;
  }

  .smm-counter-text-icon .odometer,
.smm-counter-text-icon strong {
    font-size: 30px;
  }

  .smm-counter-text-icon strong {
    top: 5px;
  }

  .smm-case-tab .nav-tabs .nav-link:after {
    display: none;
  }

  .smm-testimonial-wrap .smm-testimonial-text p {
    font-size: 18px;
  }

  .smm-testimonial-wrap .smm-testimonial-text .smm-testi-author h3 {
    font-size: 20px;
  }

  .smm-about-section {
    padding: 40px 0px 40px;
  }

  .smm-feature-section {
    padding: 50px 0px 20px;
  }

  .smm-feature-section .smm-feature-wrapper {
    margin-top: 20px;
  }

  .smm-case-study-section {
    padding: 50px 0px;
  }

  .smm-case-tab {
    padding: 15px 0px;
    margin-bottom: 20px;
  }

  .smm-testimonial-section {
    padding: 50px 0px;
  }

  .smm-blog-section {
    padding: 50px 0px 0px;
  }

  .smm-footer-section .smm-footer-wrapper {
    padding: 135px 0px 50px;
  }

  .smm-footer-copyright .smm-footer-copyright-menu a {
    margin-right: 5px;
    font-size: 12px;
    font-weight: 400;
  }

  .smm-scrollup {
    height: 40px;
    width: 40px;
    line-height: 40px;
  }

  .smm-team-section {
    padding: 50px 0px 30px;
  }
}
/*---------------------------------------------------- */