//  =================
//      Imports
//  =================

@import '../../base/base';    // Base Variables

.widget-content-area {
  padding: 10px 20px;
}

.toggle-code-snippet {
  margin-bottom: -6px;
}

/*      Media Object      */

.media {
  margin-top: 20px;
  margin-bottom: 20px;

  img {
    width: 50px;
    height: 50px;
    margin-right: 15px;
  }

  .media-body {
    align-self: center;

    .media-heading {
      color: $dark;
      font-weight: 700;
      margin-bottom: 10px;
      font-size: 17px;
      letter-spacing: 1px;
    }

    .media-text {
      color: $m-color_9;
      margin-bottom: 0;
      font-size: 14px;
      letter-spacing: 0;
    }
  }
}

/*      Right Aligned   */

.media-right-aligned .media img {
  margin-right: 0;
  margin-left: 15px;
}

/* 	Media Notation 	*/

.notation-text .media {
  &:first-child {
    border-top: none;
  }

  .media-body .media-notation {
    margin-top: 8px;
    margin-bottom: 9px;

    a {
      color: $m-color_9;
      font-size: 13px;
      font-weight: 700;
      margin-right: 8px;
    }
  }
}

/* 	Media Notation With Icon	*/

.notation-text-icon .media {
  &:first-child {
    border-top: none;
  }

  .media-body .media-notation {
    margin-top: 8px;
    margin-bottom: 9px;

    a {
      color: $m-color_9;
      font-size: 13px;
      font-weight: 700;
      margin-right: 8px;

      svg {
        color: $m-color_6;
        margin-right: 6px;
        vertical-align: sub;
        width: 18px;
        height: 18px;
        fill: rgba(0, 23, 55, 0.08);
      }
    }
  }
}

/* 	With Labels	*/

.m-o-label .media {
  &:first-child {
    border-top: none;
  }

  .badge {
    float: right;
  }
}

/* 	Dropdown	*/

.m-o-dropdown-list {
  .media {
    &:first-child {
      border-top: none;
    }

    .media-heading {
      display: flex;
      justify-content: space-between;

      div.dropdown-list {
        cursor: pointer;
        color: $m-color_6;
        font-size: 18px;
        float: right;

        a.dropdown-item {
          span {
            align-self: center;
          }

          svg {
            margin-left: 20px;
            color: $m-color_6;
            align-self: center;
            width: 20px;
            height: 20px;
            fill: rgba(0, 23, 55, 0.08);
          }

          &:hover svg {
            color: $primary;
            fill: rgba(27, 85, 226, 0.2392156863);
          }
        }
      }
    }
  }

  .dropdown-menu {
    border-radius: 6px;
    min-width: 9rem;
    border: 1px solid $m-color_2;
    box-shadow: 0px 0px 15px 1px rgba(113, 106, 202, 0.2);
    padding: 9px 0;
  }

  .dropdown-item {
    font-size: 14px;
    color: $m-color_6;
    padding: 5px 12px;
    display: flex;
    justify-content: space-between;

    &:hover {
      color: $m-color_16;
      text-decoration: none;
      background-color: $m-color_1;
    }
  }
}

/* 	Label Icon	*/

.m-o-label-icon .media {
  &:first-child {
    border-top: none;
  }

  svg.label-icon {
    align-self: center;
    width: 30px;
    height: 30px;
    margin-right: 16px;

    &.label-success {
      color: $success;
    }

    &.label-danger {
      color: #ee3d49;
    }

    &.label-warning {
      color: $m-color_15;
    }
  }
}

/* 	Checkbox	*/

.m-o-chkbox .media {
  &:first-child {
    border-top: none;
  }

  .custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
    background-color: $m-color_9;
  }
}

/* 	Checkbox	*/

.m-o-radio .media {
  &:first-child {
    border-top: none;
  }

  .custom-radio .custom-control-input:checked ~ .custom-control-label::before {
    background-color: $m-color_9;
  }
}

.custom-control-label::before {
  background-color: $m-color_5;
}